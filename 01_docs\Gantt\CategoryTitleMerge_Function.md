# 甘特图类别标题合并功能说明文档

## 目录

1. [概述](#概述)
2. [类别标题合并功能的输入](#类别标题合并功能的输入)
3. [类别标题合并功能的输出](#类别标题合并功能的输出)
4. [处理流程](#处理流程)
5. [配置参数](#配置参数)
6. [函数说明](#函数说明)
7. [使用示例](#使用示例)
8. [性能优化](#性能优化)
9. [注意事项](#注意事项)

## 概述

类别标题合并功能是甘特图系统中的一个重要功能，用于将B列中连续空单元格与上方非空类别标题单元格合并，并设置统一的格式。这个功能在基准线绘制完成后执行，确保甘特图中的类别标题区域整洁美观，提高可读性。

本文档详细说明甘特图系统中类别标题合并功能的输入、输出、处理流程和实现细节。

## 类别标题合并功能的输入

### 1. MergeCategoryTitles函数参数

`MergeCategoryTitles`函数是合并类别标题的核心函数，它接收以下输入参数：

| 参数名 | 数据类型 | 说明 |
|--------|----------|------|
| ws | Worksheet | 工作表对象，表示要在哪个工作表上合并类别标题 |
| topRow | Long | 类别标题区域的顶部边界行号，通常为6（甘特图内容的起始行） |
| bottomRow | Long | 类别标题区域的底部边界行号，通常为甘特图的最后一行 |

### 2. 配置表中的类别标题格式参数

类别标题的格式可以通过配置表进行自定义：

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| CategoryFont | Barlow | 类别标题字体名称 |
| CategoryFontSize | 11 | 类别标题字体大小 |
| CategoryFontBold | True | 类别标题是否粗体（True表示是，False表示否） |
| CategoryColumnWidth | 30 | B列宽度（单位：标准Excel列宽） |

## 类别标题合并功能的输出

### 1. 视觉输出

类别标题合并功能的主要输出是在甘特图B列中合并的单元格：

- **合并区域**：从非空类别标题单元格开始，向下合并连续的空单元格，直到下一个非空单元格或到达底部边界
- **外观**：
  - 文字自动换行
  - 水平居中对齐
  - 垂直居中对齐
  - 字体：由配置项CategoryFont决定，默认为Barlow
  - 字体大小：由配置项CategoryFontSize决定，默认为11
  - 字体粗细：由配置项CategoryFontBold决定，默认为粗体
  - B列宽度：由配置项CategoryColumnWidth决定，默认为30

### 2. 函数返回值

`MergeCategoryTitles`函数没有显式的返回值，它是Sub过程而非Function函数。这个函数的主要作用是在工作表上合并类别标题单元格并设置格式。

### 3. 日志输出

函数执行过程中会生成以下类型的日志信息：

- **信息日志**：记录类别标题合并的关键步骤，如找到的类别标题、合并的区域等
- **详细日志**：记录更详细的处理信息，如每个合并区域的具体范围
- **错误日志**：记录致命性问题，如无法合并单元格

## 处理流程

类别标题合并功能的处理流程分为以下几个主要步骤：

### 1. 读取配置参数

首先，函数会从配置表中读取类别标题的格式参数：

```vba
' 直接获取配置参数
Dim fontName As String, fontSize As Single, isBold As Boolean, columnWidth As Double

' 获取字体名称
fontName = CStr(GetConfig("CategoryFont", "Barlow"))

' 获取字体大小
fontSize = CSng(Val(GetConfig("CategoryFontSize", 11)))

' 获取是否粗体
isBold = CBool(Val(GetConfig("CategoryFontBold", True)))

' 获取B列宽度
columnWidth = CDbl(Val(GetConfig("CategoryColumnWidth", 30)))

' 设置B列宽度
ws.Columns("B").ColumnWidth = columnWidth
modDebug.LogVerbose "设置B列宽度为: " & columnWidth, "modGantt.MergeCategoryTitles"
```

### 2. 一次性读取数据到数组

为了提高性能，函数会一次性读取B列从顶部行到底部行的所有数据到数组中：

```vba
' 一次性读取B列数据到数组
Dim dataRange As Range
Set dataRange = ws.Range("B" & topRow & ":B" & bottomRow)

Dim dataArray As Variant
dataArray = dataRange.Value
```

### 3. 在数组中分析需要合并的区域

函数会在数组中分析需要合并的区域，找出每个非空类别标题单元格及其下方连续的空单元格：

```vba
' 在数组中分析需要合并的区域
For i = 1 To UBound(dataArray, 1)
    ' 检查当前单元格是否有内容
    If Trim(CStr(dataArray(i, 1))) <> "" Then
        ' 记录非空单元格索引
        lastNonEmptyIndex = i

        ' 如果已经在合并区域中，记录之前的区域
        If inMergeRegion Then
            ' 确保合并区域至少有两行
            If i - 1 > mergeStartRow Then
                ' 添加合并区域到集合
                mergeRanges.Add Array(mergeStartRow + topRow - 1, i + topRow - 2)
                modDebug.LogVerbose "添加合并区域: B" & (mergeStartRow + topRow - 1) & ":B" & (i + topRow - 2), "modGantt.MergeCategoryTitles"
            End If
        End If

        ' 记录新的合并区域起始行
        mergeStartRow = i
        inMergeRegion = True
    End If
Next i

' 处理最后一个合并区域（如果存在）
If inMergeRegion And UBound(dataArray, 1) > mergeStartRow Then
    mergeRanges.Add Array(mergeStartRow + topRow - 1, bottomRow)
    modDebug.LogVerbose "添加最后合并区域: B" & (mergeStartRow + topRow - 1) & ":B" & bottomRow, "modGantt.MergeCategoryTitles"
End If
```

### 4. 批量执行所有合并操作

最后，函数会批量执行所有合并操作，并设置合并后单元格的格式：

```vba
' 批量执行所有合并操作
If mergeRanges.Count > 0 Then
    Dim j As Long
    Dim mergeRange As Variant

    ' 执行所有合并操作
    For j = 1 To mergeRanges.Count
        mergeRange = mergeRanges(j)

        ' 合并单元格
        ws.Range("B" & mergeRange(0) & ":B" & mergeRange(1)).Merge

        ' 设置合并后单元格的格式
        With ws.Cells(mergeRange(0), 2)
            .HorizontalAlignment = xlCenter ' 水平居中
            .VerticalAlignment = xlCenter   ' 垂直居中
            .WrapText = True                ' 文字自动换行
            .Font.Name = fontName
            .Font.Size = fontSize
            .Font.Bold = isBold
        End With

        modDebug.LogVerbose "已合并并格式化区域: B" & mergeRange(0) & ":B" & mergeRange(1), "modGantt.MergeCategoryTitles"
    Next j
End If
```

## 配置参数

类别标题合并功能支持以下配置参数，这些参数可以在Config工作表的configTable中设置：

| 配置项 | 说明 | 默认值 | 可选值 |
|--------|------|--------|--------|
| CategoryFont | 类别标题字体名称 | Barlow | 任何有效的字体名称 |
| CategoryFontSize | 类别标题字体大小 | 11 | 任何大于0的数值 |
| CategoryFontBold | 类别标题是否粗体 | True | True=粗体, False=常规 |
| CategoryColumnWidth | B列宽度 | 30 | 任何大于0的数值 |

## 函数说明

### MergeCategoryTitles

这是合并类别标题的核心函数，使用高效的数组处理方式。

**函数签名**：
```vba
Private Sub MergeCategoryTitles(ws As Worksheet, topRow As Long, bottomRow As Long)
```

**参数说明**：
- `ws`：工作表对象，表示要在哪个工作表上合并类别标题
- `topRow`：类别标题区域的顶部边界行号，通常为6（甘特图内容的起始行）
- `bottomRow`：类别标题区域的底部边界行号，通常为甘特图的最后一行

**功能**：
- 从配置表中读取类别标题的格式参数
- 一次性读取B列数据到数组
- 在数组中分析需要合并的区域
- 批量执行所有合并操作
- 设置合并后单元格的格式

## 使用示例

### 在DrawTasksAndMilestones函数中调用

```vba
' 统一处理所有基准线
If baselineCollection.Count > 0 Then
    For j = 1 To baselineCollection.Count
        Set baseline = baselineCollection(j)
        DrawBaselineWithBounds baseline("Date"), ws, timelineCoords, 6, lastRowPosition
    Next j
    modDebug.LogInfo "所有基准线处理完成", "modGantt.DrawTasksAndMilestones"
Else
    modDebug.LogInfo "没有基准线需要处理", "modGantt.DrawTasksAndMilestones"
End If

' 合并类别标题区域
modDebug.LogInfo "开始合并类别标题区域", "modGantt.DrawTasksAndMilestones"
MergeCategoryTitles ws, 6, lastRowPosition
modDebug.LogInfo "类别标题区域合并完成", "modGantt.DrawTasksAndMilestones"
```

## 性能优化

类别标题合并功能采用了以下性能优化措施：

1. **一次性读取数据**：将整个B列数据一次性读入数组，避免逐个单元格访问
2. **在内存中分析**：在数组中完成所有分析工作，不与Excel交互
3. **批量处理**：收集所有需要合并的区域，然后一次性执行所有合并操作
4. **临时关闭计算**：临时将计算模式设为手动，减少不必要的重新计算
5. **关闭屏幕更新**：在处理过程中关闭屏幕更新，提高性能

```vba
' 关闭屏幕更新和警告，提高性能
Application.ScreenUpdating = False
Application.DisplayAlerts = False
Application.Calculation = xlCalculationManual

' ... 处理逻辑 ...

' 恢复计算模式和屏幕更新
Application.Calculation = xlCalculationAutomatic
Application.DisplayAlerts = True
Application.ScreenUpdating = True
```

## 注意事项

1. **合并条件**：
   - 只有当非空单元格下方有连续的空单元格时才会进行合并
   - 合并区域至少包含两行（一个非空单元格和至少一个空单元格）

2. **格式设置**：
   - 合并后的单元格会设置为文字自动换行、垂直居中对齐
   - 字体、字体大小和粗细可以通过配置表进行自定义

3. **执行时机**：
   - 类别标题合并功能在基准线绘制完成后执行
   - 这确保了在甘特图的所有元素都已绘制完成后再进行合并操作

4. **错误处理**：
   - 函数包含完善的错误处理机制，确保即使出现异常也能恢复Excel设置
   - 所有错误都会记录到日志中，便于调试和问题排查

5. **性能考虑**：
   - 对于大型甘特图，合并操作可能会消耗一定的时间
   - 通过优化的数组处理方式，已经最大限度地提高了性能
