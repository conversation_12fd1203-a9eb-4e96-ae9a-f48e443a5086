# DrawTask 和 DrawMilestone 函数详细说明文档

本文档详细说明Excel VBA项目管理甘特图系统中的DrawTask和DrawMilestone函数的实现逻辑、输入、输出和处理过程。

## 1. DrawTask 函数

### 1.1 功能概述

`DrawTask` 函数负责在甘特图工作表上绘制任务条形图形。该函数根据任务的开始日期和结束日期，在指定行位置创建矩形形状表示任务，并根据任务进度添加进度条，同时添加任务描述标签。

### 1.2 函数签名

```vba
Private Sub DrawTask(task As Dictionary, row As Long, timelineCoords As Dictionary)
```

### 1.3 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| task | Dictionary | 包含任务信息的字典对象 |
| row | Long | 任务在甘特图上的行位置（行号） |
| timelineCoords | Dictionary | 时间轴坐标系信息 |

#### 1.3.1 task 字典必需的键

- **StartDate**：任务开始日期（Date类型）
- **EndDate**：任务结束日期（Date类型）
- **Color**：任务颜色（十六进制格式，如"#FF0000"）
- **Progress**：任务进度（0-1之间的小数）

#### 1.3.2 task 字典可选的键

- **Description**：任务描述（用于标签）
- **ID**：任务ID（用于日志记录）
- **TextPosition**：标签位置（"right"、"left"、"top"、"bottom"、"inside"）

#### 1.3.3 timelineCoords 字典必需的键

- **OriginX**：坐标系原点的X坐标
- **Width**：坐标系的总宽度
- **StartDate**：坐标系的起始日期
- **EndDate**：坐标系的结束日期
- **TotalDays**：坐标系覆盖的总天数

### 1.4 配置参数

函数使用以下配置参数，可以在Config工作表中设置：

| 配置ID | 配置名称 | 默认值 | 说明 |
|--------|----------|--------|------|
| GT026 | TaskBarHeight | 11 | 任务条高度（像素） |
| GT027 | TaskBarBorderWidth | 0 | 任务条边框宽度（0=无边框） |
| GT028 | ProgressBarColor | #66CC66 | 进度条颜色 |
| GT029 | LabelDistance | 5 | 标签与任务条的距离（像素） |
| GT030 | RowPadding | 3 | 行高上下预留空隙（像素） |

### 1.5 处理流程

```mermaid
flowchart TD
    A[开始] --> B[初始化和获取配置参数]
    B --> C[获取GanttChart工作表引用]
    C --> D[计算任务条位置和大小]
    D --> E[创建任务条形状]
    E --> F{创建成功?}
    F -->|否| G[记录错误并退出]
    F -->|是| H[设置任务条填充颜色和边框]
    H --> I{任务有进度?}
    I -->|是| J[创建进度条]
    I -->|否| K[跳过进度条创建]
    J --> L[添加任务描述标签]
    K --> L
    L --> M{标签创建成功?}
    M -->|否| N[记录警告]
    M -->|是| O[动态调整行高]
    O --> P[记录函数退出]
    N --> P
    P --> Q[结束]
    G --> Q
```

#### 1.5.1 初始化和获取配置参数

```vba
' 记录函数进入
Dim taskId As String, taskDesc As String
taskId = IIf(task.Exists("ID"), task("ID"), "未知ID")
taskDesc = IIf(task.Exists("Description"), task("Description"), "未知描述")
modDebug.LogFunctionEntry "modGantt.DrawTask", "任务ID: " & taskId & ", 描述: " & taskDesc & ", 行: " & row

' 获取配置参数
Dim taskBarHeight As Long
taskBarHeight = Val(GetConfigValue("GT026", "11")) ' 默认任务条高度为11

Dim taskBarBorderWidth As Single
taskBarBorderWidth = Val(GetConfigValue("GT027", "0")) ' 默认无边框

Dim progressBarColor As String
progressBarColor = GetConfigValue("GT028", "#66CC66") ' 默认进度条颜色为绿色

Dim labelDistance As Long
labelDistance = Val(GetConfigValue("GT029", "5")) ' 默认标签距离为5像素

Dim rowPadding As Long
rowPadding = Val(GetConfigValue("GT030", "3")) ' 默认行高上下预留3像素
```

#### 1.5.2 计算任务条位置和大小

```vba
' 计算任务开始和结束的X坐标
Dim startX As Double, endX As Double
startX = CalculateXCoordinate(task("StartDate"), timelineCoords)
endX = CalculateXCoordinate(task("EndDate"), timelineCoords)

' 计算任务条的左边缘、宽度和高度
left = startX
width = endX - startX
height = taskBarHeight

' 计算任务条的垂直居中位置
Dim rowHeight As Double
rowHeight = ws.Cells(row, 1).Height
top = ws.Cells(row, 1).Top + (rowHeight - height) / 2 ' 垂直居中
```

#### 1.5.3 创建任务条形状

```vba
' 创建任务条形状
Dim taskShape As shape
Set taskShape = ws.Shapes.AddShape(msoShapeRectangle, left, top, width, height)

' 设置任务条格式
taskShape.Fill.ForeColor.RGB = GetRGBColor(task("Color"))

' 设置任务条边框
If taskBarBorderWidth = 0 Then
    taskShape.Line.Visible = msoFalse ' 无边框
Else
    taskShape.Line.Visible = msoTrue
    taskShape.Line.Weight = taskBarBorderWidth
    taskShape.Line.ForeColor.RGB = GetRGBColor(task("Color"))
End If
```

#### 1.5.4 创建进度条（如果有进度）

```vba
' 如果有进度，绘制进度条
If task.Exists("Progress") Then
    Dim progress As Double
    progress = CDbl(task("Progress"))
    
    ' 确保进度在0-1之间
    If progress > 0 And progress <= 1 Then
        ' 计算进度条宽度
        Dim progressWidth As Double
        progressWidth = width * progress
        
        ' 创建进度条形状
        Dim progressShape As Shape
        Set progressShape = ws.Shapes.AddShape(msoShapeRectangle, left, top, progressWidth, height)
        
        ' 设置进度条格式
        progressShape.Fill.ForeColor.RGB = GetRGBColor(progressBarColor)
        progressShape.Line.Visible = msoFalse ' 无边框
    End If
End If
```

#### 1.5.5 添加任务描述标签

```vba
' 添加任务描述标签
Dim labelShape As Shape
Set labelShape = AddTaskLabelWithCoordinates2(task, taskShape, startX + width / 2, top + height / 2, CDbl(height), labelDistance)

' 动态调整行高以适应任务和标签
If Not labelShape Is Nothing Then
    AdjustRowHeightWithPadding ws, row, taskShape, labelShape, rowPadding
End If
```

### 1.6 输出

函数没有直接的返回值，但会在工作表上创建以下图形元素：
1. 任务条形状（矩形）
2. 进度条形状（如果任务有进度）
3. 任务描述标签（文本框）

## 2. DrawMilestone 函数

### 2.1 功能概述

`DrawMilestone` 函数负责在甘特图工作表上绘制里程碑图形。该函数根据里程碑的日期，在指定行位置创建菱形形状表示里程碑，并添加里程碑描述标签。

### 2.2 函数签名

```vba
Private Sub DrawMilestone(task As Dictionary, row As Long, timelineCoords As Dictionary)
```

### 2.3 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| task | Dictionary | 包含里程碑信息的字典对象 |
| row | Long | 里程碑在甘特图上的行位置（行号） |
| timelineCoords | Dictionary | 时间轴坐标系信息 |

#### 2.3.1 task 字典必需的键

- **StartDate**：里程碑日期（Date类型）
- **Color**：里程碑颜色（十六进制格式，如"#FF0000"）

#### 2.3.2 task 字典可选的键

- **Description**：里程碑描述（用于标签）
- **ID**：里程碑ID（用于日志记录）
- **TextPosition**：标签位置（"right"、"left"、"top"、"bottom"）

#### 2.3.3 timelineCoords 字典必需的键

- **OriginX**：坐标系原点的X坐标
- **Width**：坐标系的总宽度
- **StartDate**：坐标系的起始日期
- **EndDate**：坐标系的结束日期
- **TotalDays**：坐标系覆盖的总天数

### 2.4 配置参数

函数使用以下配置参数，可以在Config工作表中设置：

| 配置ID | 配置名称 | 默认值 | 说明 |
|--------|----------|--------|------|
| GT026 | TaskBarHeight | 11 | 里程碑大小（像素） |
| GT027 | TaskBarBorderWidth | 0 | 里程碑边框宽度（0=无边框） |
| GT029 | LabelDistance | 5 | 标签与里程碑的距离（像素） |
| GT030 | RowPadding | 3 | 行高上下预留空隙（像素） |

### 2.5 处理流程

```mermaid
flowchart TD
    A[开始] --> B[初始化和获取配置参数]
    B --> C[获取GanttChart工作表引用]
    C --> D[计算里程碑位置]
    D --> E[创建里程碑形状]
    E --> F{创建成功?}
    F -->|否| G[记录错误并退出]
    F -->|是| H[设置里程碑填充颜色和边框]
    H --> I[添加里程碑描述标签]
    I --> J{标签创建成功?}
    J -->|否| K[记录警告]
    J -->|是| L[动态调整行高]
    L --> M[记录函数退出]
    K --> M
    M --> N[结束]
    G --> N
```

#### 2.5.1 初始化和获取配置参数

```vba
' 记录函数进入
Dim taskId As String, taskDesc As String
taskId = IIf(task.Exists("ID"), task("ID"), "未知ID")
taskDesc = IIf(task.Exists("Description"), task("Description"), "未知描述")
modDebug.LogFunctionEntry "modGantt.DrawMilestone", "任务ID: " & taskId & ", 描述: " & taskDesc & ", 行: " & row

' 获取配置参数
Dim milestoneSize As Long
milestoneSize = Val(GetConfigValue("GT026", "11")) ' 默认里程碑大小为11

Dim milestoneBorderWidth As Single
milestoneBorderWidth = Val(GetConfigValue("GT027", "0")) ' 默认无边框

Dim labelDistance As Long
labelDistance = Val(GetConfigValue("GT029", "5")) ' 默认标签距离为5像素

Dim rowPadding As Long
rowPadding = Val(GetConfigValue("GT030", "3")) ' 默认行高上下预留3像素
```

#### 2.5.2 计算里程碑位置

```vba
' 计算里程碑在坐标系中的精确位置
Dim milestoneX As Double, milestoneY As Double
milestoneX = CalculateXCoordinate(task("StartDate"), timelineCoords)

' 计算Y坐标 - 使用行的中心点
Dim rowHeight As Double
rowHeight = ws.Cells(row, 1).Height
milestoneY = ws.Cells(row, 1).Top + rowHeight / 2

' 计算里程碑形状的左上角坐标
Dim shapeLeft As Double, shapeTop As Double
shapeLeft = milestoneX - milestoneSize / 2
shapeTop = milestoneY - milestoneSize / 2
```

#### 2.5.3 创建里程碑形状

```vba
' 创建里程碑形状（菱形）
Dim milestoneShape As Shape
Set milestoneShape = ws.Shapes.AddShape(msoShapeDiamond, shapeLeft, shapeTop, milestoneSize, milestoneSize)

' 设置里程碑格式
milestoneShape.Fill.ForeColor.RGB = GetRGBColor(task("Color"))

' 设置里程碑边框
If milestoneBorderWidth = 0 Then
    milestoneShape.Line.Visible = msoFalse ' 无边框
Else
    milestoneShape.Line.Visible = msoTrue
    milestoneShape.Line.Weight = milestoneBorderWidth
    milestoneShape.Line.ForeColor.RGB = GetRGBColor(task("Color"))
End If
```

#### 2.5.4 添加里程碑描述标签

```vba
' 添加里程碑描述标签
Dim labelShape As Shape
Set labelShape = AddTaskLabelWithCoordinates2(task, milestoneShape, milestoneX, milestoneY, CDbl(milestoneSize), labelDistance)

' 动态调整行高以适应里程碑和标签
If Not labelShape Is Nothing Then
    AdjustRowHeightWithPadding ws, row, milestoneShape, labelShape, rowPadding
End If
```

### 2.6 输出

函数没有直接的返回值，但会在工作表上创建以下图形元素：
1. 里程碑形状（菱形）
2. 里程碑描述标签（文本框）

## 3. 关键算法

### 3.1 日期到坐标的映射算法

两个函数都使用 `CalculateXCoordinate` 函数将日期转换为X坐标：

```vba
Private Function CalculateXCoordinate(targetDate As Date, coords As Dictionary) As Double
    ' 计算日期与起始日期的天数差（并加1进行修正）
    Dim daysDiff As Long
    daysDiff = DateDiff("d", coords("StartDate"), targetDate) + 1
    
    ' 计算总天数(修正后)
    Dim totalDaysAdjusted As Long
    totalDaysAdjusted = coords("TotalDays") + 1
    
    ' 计算X坐标
    Dim xCoord As Double
    xCoord = coords("OriginX") + (coords("Width") * daysDiff / totalDaysAdjusted)
    
    CalculateXCoordinate = xCoord
End Function
```

这个函数使用线性插值算法：
1. 计算目标日期与起始日期的天数差（加1修正）
2. 计算这个天数差在总天数中的比例
3. 根据这个比例计算在总宽度中的偏移量
4. 将原点坐标和偏移量相加，得到最终的X坐标

### 3.2 标签位置算法

两个函数都使用 `AddTaskLabelWithCoordinates2` 函数添加描述标签：

```vba
Private Function AddTaskLabelWithCoordinates2(task As Dictionary, shape As Shape, centerX As Double, centerY As Double, shapeSize As Double, labelDistance As Long) As Shape
    ' 创建临时文本框以计算文本尺寸
    Dim tempTextBox As Shape
    Set tempTextBox = ws.Shapes.AddTextbox(msoTextOrientationHorizontal, 0, 0, 100, 20)
    
    ' 设置临时文本框的文本和格式
    tempTextBox.TextFrame.Characters.Text = task("Description")
    tempTextBox.TextFrame.Characters.Font.Size = 8
    tempTextBox.TextFrame.AutoSize = True
    
    ' 获取文本的实际宽度和高度
    Dim textWidth As Double, textHeight As Double
    textWidth = tempTextBox.Width
    textHeight = tempTextBox.Height
    
    ' 删除临时文本框
    tempTextBox.Delete
    
    ' 确定标签位置和对齐方式
    Dim labelX As Double, labelY As Double
    Dim textAlign As Long
    
    ' 根据TextPosition属性或自动判断放置位置
    Select Case task("TextPosition")
        Case "right"
            ' 放在右侧，左对齐
            labelX = centerX + shapeSize / 2 + labelDistance
            labelY = centerY - textHeight / 2
            textAlign = xlLeft
        Case "left"
            ' 放在左侧，右对齐
            labelX = centerX - shapeSize / 2 - labelDistance - textWidth
            labelY = centerY - textHeight / 2
            textAlign = xlRight
        Case "top"
            ' 放在上方，左对齐
            labelX = centerX - shapeSize / 2
            labelY = centerY - shapeSize / 2 - labelDistance - textHeight
            textAlign = xlLeft
        Case "bottom"
            ' 放在下方，左对齐
            labelX = centerX - shapeSize / 2
            labelY = centerY + shapeSize / 2 + labelDistance
            textAlign = xlLeft
        Case "inside"
            ' 放在内部，居中对齐
            labelX = centerX - textWidth / 2
            labelY = centerY - textHeight / 2
            textAlign = xlCenter
        Case Else
            ' 默认放在右侧，左对齐
            labelX = centerX + shapeSize / 2 + labelDistance
            labelY = centerY - textHeight / 2
            textAlign = xlLeft
    End Select
    
    ' 创建标签文本框
    Dim labelShape As Shape
    Set labelShape = ws.Shapes.AddTextbox(msoTextOrientationHorizontal, labelX, labelY, textWidth, textHeight)
    
    ' 设置标签文本和格式
    labelShape.TextFrame.Characters.Text = task("Description")
    labelShape.TextFrame.Characters.Font.Size = 8
    labelShape.TextFrame.HorizontalAlignment = textAlign
    
    Set AddTaskLabelWithCoordinates2 = labelShape
End Function
```

这个函数根据任务的TextPosition属性或自动判断确定标签位置：
1. 创建临时文本框以计算文本尺寸
2. 根据TextPosition属性确定标签位置和对齐方式
3. 创建标签文本框并设置文本内容和格式

## 4. 注意事项

### 4.1 DrawTask 函数注意事项

1. **必需的任务属性**：
   - StartDate：任务开始日期
   - EndDate：任务结束日期
   - Color：任务颜色
   - Progress：任务进度

2. **错误处理**：
   - 如果任务条创建失败，函数会退出
   - 如果进度条创建失败，函数会继续执行，但记录错误

3. **动态行高调整**：
   - 函数会根据任务条和标签的大小动态调整行高
   - 考虑上下预留空隙，确保所有元素都能完全显示

### 4.2 DrawMilestone 函数注意事项

1. **必需的任务属性**：
   - StartDate：里程碑日期
   - Color：里程碑颜色

2. **错误处理**：
   - 如果里程碑形状创建失败，函数会退出

3. **动态行高调整**：
   - 函数会根据里程碑和标签的大小动态调整行高
   - 考虑上下预留空隙，确保所有元素都能完全显示
