# DPI缩放问题用户指南

## 问题现象

如果您在使用甘特图时遇到以下问题：

1. **在主显示器上正常**：甘特图的里程碑和任务条在主显示器上显示正常，位置居中
2. **在外接显示器上错位**：当Excel窗口移动到外接显示器时，里程碑和任务条超出了所在行的边界

这通常是由于不同显示器的DPI（每英寸点数）设置不同导致的。

## 解决方案

### 方案1：使用自动DPI修正（推荐）

系统已内置DPI自动修正功能，默认启用。如果遇到位置错位问题：

#### 步骤1：测试DPI检测
1. 打开Excel文件
2. 按`Alt + F11`打开VBA编辑器
3. 在立即窗口中输入：`Call modUtilities.TestDPIScaling`
4. 按回车执行

系统会显示当前的DPI信息和配置状态。

#### 步骤2：调整DPI因子（如需要）
如果自动修正效果不理想，可以手动调整：

1. 打开`Config`工作表
2. 找到或添加以下配置项：

| Module | ConfigName | Value | Enabled | Description |
|--------|------------|-------|---------|-------------|
| Gantt | DPIAdjustmentFactor | 0.9 | TRUE | DPI调整因子 |

3. 调整`Value`列的数值：
   - 如果里程碑/任务条位置偏高，减小数值（如0.8、0.7）
   - 如果里程碑/任务条位置偏低，增大数值（如1.1、1.2）

4. 重新生成甘特图测试效果

### 方案2：禁用DPI修正

如果DPI修正导致其他问题，可以禁用：

1. 打开`Config`工作表
2. 找到或添加以下配置项：

| Module | ConfigName | Value | Enabled | Description |
|--------|------------|-------|---------|-------------|
| Gantt | EnableDPICorrection | FALSE | TRUE | 禁用DPI修正 |

3. 重新生成甘特图

### 方案3：使用Excel兼容模式

如果上述方案都无效，可以使用Excel的兼容模式：

1. 右键点击Excel图标
2. 选择"属性"
3. 切换到"兼容性"选项卡
4. 勾选"替代高DPI缩放行为"
5. 在下拉菜单中选择"系统"
6. 点击"确定"
7. 重启Excel

## 常见问题

### Q1：如何知道当前的DPI设置？
**A1：** 运行`Call modUtilities.TestDPIScaling`命令，系统会显示当前检测到的DPI信息。

### Q2：调整因子应该设置为多少？
**A2：** 建议从1.0开始，根据实际效果进行微调：
- 主显示器100% DPI，外接显示器125% DPI：尝试0.8
- 主显示器100% DPI，外接显示器150% DPI：尝试0.7
- 具体数值需要根据实际显示效果调整

### Q3：为什么有时候修正有效，有时候无效？
**A3：** 可能的原因：
1. Excel窗口在不同显示器间移动时，DPI检测可能有延迟
2. 某些显示器驱动可能影响DPI检测
3. Windows显示设置可能影响DPI报告

### Q4：如何恢复默认设置？
**A4：** 删除Config表中的相关配置项，或将其设置为：
- `EnableDPICorrection`: TRUE
- `DPIAdjustmentFactor`: 1.0

## 高级设置

### 针对特定显示器的调整

如果您经常在多个显示器间切换，可以为不同的使用场景创建不同的配置：

1. **主显示器配置**：`DPIAdjustmentFactor = 1.0`
2. **外接显示器配置**：`DPIAdjustmentFactor = 0.8`（根据实际调整）

### 调试信息

如果需要详细的调试信息：

1. 在Config表中设置：
   - `EnableDebug`: TRUE
   - `DebugLevel`: 4

2. 重新生成甘特图，查看调试日志中的DPI相关信息

## 技术支持

如果以上方案都无法解决问题，请提供以下信息：

1. **显示器配置**：
   - 主显示器分辨率和DPI设置
   - 外接显示器分辨率和DPI设置
   - Windows显示缩放设置

2. **测试结果**：
   - `TestDPIScaling`命令的输出结果
   - 尝试过的`DPIAdjustmentFactor`值

3. **Excel版本**：
   - Excel版本号
   - Windows版本

4. **问题截图**：
   - 在主显示器上的正常显示
   - 在外接显示器上的错位显示

## 注意事项

1. **重新生成甘特图**：修改配置后需要重新生成甘特图才能看到效果
2. **保存配置**：确保Config表中的配置已保存
3. **Excel重启**：某些情况下可能需要重启Excel才能生效
4. **备份文件**：在进行配置调整前建议备份Excel文件

## 相关文档

- [DPI缩放技术文档](../design/dpi_scaling_solution.md)
- [配置系统说明](../design/config_worksheet_updated.md)
- [甘特图生成逻辑](../Gantt/甘特图生成逻辑说明.md)
