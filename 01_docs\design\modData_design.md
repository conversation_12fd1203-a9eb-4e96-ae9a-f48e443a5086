# modData 模块设计文档

## 1. 模块概述

modData模块是系统的数据处理核心，负责数据访问、验证和管理。它为其他模块提供数据服务，包括项目信息获取、任务数据获取、配置管理等功能。

> 注意：代码中的注释和用户界面消息已更新为英文，以避免在VBA编辑器中出现编码问题。本设计文档仍保持中文说明，以便于中文用户理解。

## 2. 模块结构

```mermaid
classDiagram
    class modData {
        +ValidateAllData() Boolean
        +GetProjectInfo() Dictionary
        +GetAllTasks(outBaselineCollection) Collection
        +GetModuleConfig(moduleName, defaultValues) Dictionary
        +GetConfigFromDict(dict, configName, defaultValue) Variant
        +GetConfig(configName, defaultValue) Variant
        -ValidateProjectInfo(errorMessages, errorCells, errorCount) Boolean
        -ValidateTasksData(errorMessages, errorCells, errorCount) Boolean
        -ClearAllValidationMarks()
        -ClearValidationMarks(range)
    }
```

## 3. 主要功能

### 3.1 数据验证

```mermaid
flowchart TD
    Start([开始]) --> ValidateAllData[验证所有数据]
    ValidateAllData --> ClearMarks[清除所有验证标记]

    ClearMarks --> ValidateProjectInfo[验证项目信息]
    ValidateProjectInfo --> CollectProjectErrors[收集项目信息错误]

    CollectProjectErrors --> ValidateTasksData[验证任务数据]
    ValidateTasksData --> CollectTaskErrors[收集任务数据错误]

    CollectTaskErrors --> HasErrors{有错误?}
    HasErrors -->|是| MarkErrors[标记错误单元格]
    HasErrors -->|否| ReturnTrue[返回True]

    MarkErrors --> ShowErrorSummary[显示错误摘要]
    ShowErrorSummary --> ReturnFalse[返回False]

    ReturnFalse --> End([结束])
    ReturnTrue --> End
```

### 3.2 数据获取

```mermaid
flowchart TD
    Start([开始]) --> GetData[获取数据]

    GetData --> DataType{数据类型}
    DataType -->|项目信息| GetProjectInfo[获取项目信息]
    DataType -->|任务数据| GetAllTasks[获取所有任务]
    DataType -->|模块配置| GetModuleConfig[获取模块配置]
    DataType -->|单个配置| GetConfig[获取单个配置]

    GetProjectInfo --> ReturnData[返回数据]

    GetAllTasks --> ProcessTasks[处理任务数据]
    ProcessTasks --> CollectBaselines[收集基准线信息]
    CollectBaselines --> ReturnTasks[返回任务集合]
    ReturnTasks --> ReturnData

    GetModuleConfig --> CheckConfigTable{配置表有数据?}
    CheckConfigTable -->|否| UseDefaults1[使用默认值]
    CheckConfigTable -->|是| ReadConfigArray[读取配置数组]
    ReadConfigArray --> FilterByModule[按模块筛选]
    FilterByModule --> MergeDefaults[合并默认值]
    MergeDefaults --> ReturnConfig[返回配置字典]
    UseDefaults1 --> ReturnConfig
    ReturnConfig --> ReturnData

    GetConfig --> CheckConfigTable2{配置表有数据?}
    CheckConfigTable2 -->|否| UseDefaults2[使用默认值]
    CheckConfigTable2 -->|是| ReadConfigArray2[读取配置数组]
    ReadConfigArray2 --> FindByName[按名称查找]
    FindByName --> ReturnValue[返回配置值]
    UseDefaults2 --> ReturnValue
    ReturnValue --> ReturnData

    ReturnData --> End([结束])
```

### 3.3 配置访问

```mermaid
sequenceDiagram
    participant Module as 调用模块
    participant Data as modData
    participant Config as 配置工作表
    participant Defaults as 默认值字典
    participant Array as 数组处理

    Module->>Data: GetModuleConfig(moduleName, defaultValues)
    Data->>Config: 获取配置表引用

    alt 配置表为空
        Config-->>Data: 返回空数据
        Data->>Defaults: 获取默认值
        Defaults-->>Data: 返回默认值
        Data-->>Module: 返回默认值字典
    else 配置表有数据
        Config-->>Data: 返回数据
        Data->>Array: 一次性读取到数组
        Array-->>Data: 返回数组
        Data->>Array: 计算列索引映射
        Array-->>Data: 返回映射关系
        Data->>Array: 按模块筛选配置
        Array-->>Data: 返回筛选结果

        alt 提供了默认值
            Data->>Defaults: 合并缺失的配置项
            Defaults-->>Data: 返回合并结果
        end

        Data-->>Module: 返回配置字典
    end

    Module->>Data: GetConfig(configName, defaultValue)
    Data->>Config: 获取配置表引用

    alt 配置表为空
        Config-->>Data: 返回空数据
        Data-->>Module: 返回默认值
    else 配置表有数据
        Config-->>Data: 返回数据
        Data->>Array: 一次性读取到数组
        Array-->>Data: 返回数组
        Data->>Array: 计算列索引映射
        Array-->>Data: 返回映射关系
        Data->>Array: 按名称查找配置

        alt 找到配置项且已启用
            Array-->>Data: 返回配置值
            Data-->>Module: 返回配置值
        else 未找到配置项或未启用
            Array-->>Data: 未找到或未启用
            Data-->>Module: 返回默认值
        end
    end

    Module->>Data: GetConfigFromDict(dict, configName, defaultValue)

    alt 字典中存在配置项
        Data-->>Module: 返回配置值
    else 字典中不存在配置项
        Data-->>Module: 返回默认值
    end
```

## 4. 函数说明

### 4.1 公共函数

| 函数名 | 返回类型 | 参数 | 说明 |
|--------|----------|------|------|
| ValidateAllData | Boolean | 无 | 验证所有数据，包括项目信息和任务数据，收集并显示错误信息 |
| GetProjectInfo | Dictionary | 无 | 获取项目基本信息，返回包含ProjectName、ProjectManager、StartDate、EndDate、Description的字典对象 |
| GetAllTasks | Collection | [outBaselineCollection As Collection] | 获取所有任务/里程碑数据，返回任务字典集合，可选参数用于收集基准线信息 |
| GetModuleConfig | Dictionary | moduleName As String, [defaultValues As Dictionary] | 获取指定模块的所有配置，使用ConfigName作为键，如未找到或禁用则使用默认值，使用数组优化提高性能 |
| GetConfigFromDict | Variant | dict As Dictionary, configName As String, [defaultValue As Variant] | 从配置字典中获取指定名称的配置值，如未找到则返回默认值 |
| GetConfig | Variant | configName As String, [defaultValue As Variant] | 直接获取配置项的值，不考虑模块，如未找到或禁用则使用默认值，使用数组优化提高性能 |

### 4.2 私有函数

| 函数名 | 返回类型 | 参数 | 说明 |
|--------|----------|------|------|
| ValidateProjectInfo | Boolean | errorMessages As Collection, errorCells As Collection, errorCount As Long | 验证项目信息数据，收集错误信息和单元格位置，返回验证结果 |
| ValidateTasksData | Boolean | errorMessages As Collection, errorCells As Collection, errorCount As Long | 验证任务/里程碑数据，收集错误信息和单元格位置，返回验证结果 |
| ClearAllValidationMarks | 无 | 无 | 清除所有工作表上的验证标记 |
| ClearValidationMarks | 无 | range As Range | 清除指定区域上的验证标记 |
| MarkErrorCell | 无 | cell As Range, errorNumber As Long | 标记错误单元格，添加红色边框和错误编号 |
| CalculateWorkingDays | Long | startDate As Date, endDate As Date, excludeWeekends As Boolean | 计算两个日期之间的工作日数量，可选是否排除周末 |

## 5. 数据结构

### 5.1 项目信息

```mermaid
classDiagram
    class ProjectInfo {
        +String ProjectName
        +String ProjectManager
        +Date StartDate
        +Date EndDate
        +String Description
    }
```

### 5.2 任务数据

```mermaid
classDiagram
    class Task {
        +String ID
        +String Category
        +String Description
        +String Type
        +Date StartDate
        +Date EndDate
        +Integer Duration
        +Double Progress
        +String Position
        +String Color
        +String TextPosition
        +Date Baseline
        +Boolean ShowDateInLabel
    }

    class BaselineInfo {
        +Date Date
        +String TaskID
    }

    Task -- BaselineInfo : 关联
```

### 5.3 配置数据

```mermaid
classDiagram
    class Config {
        +String ConfigID
        +String ConfigName
        +Variant ConfigValue
        +String Description
        +String Module
        +String Category
        +Boolean IsEnabled
    }

    class DefaultConfig {
        +Dictionary UIDefaults
        +Dictionary GanttDefaults
        +Dictionary DataDefaults
        +Dictionary DebugDefaults
        +Dictionary GetAllDefaults()
    }

    class ConfigArray {
        +Variant[][] DataArray
        +Long ModuleColArray
        +Long EnabledColArray
        +Long NameColArray
        +Long ValueColArray
    }

    Config -- DefaultConfig : 使用默认值
    Config -- ConfigArray : 数组优化
```

## 6. 数据访问方式

### 6.1 项目信息访问

使用定义名称访问项目信息：

```vba
' 使用定义名称获取项目信息
projectName = Range("projectName").Value
projectManager = Range("projectManager").Value
startDate = Range("projectStartDate").Value
endDate = Range("projectEndDate").Value
description = Range("projectDescription").Value
```

### 6.2 任务数据访问

使用数组优化访问任务数据：

```vba
' 获取超级表引用
Set tbl = ThisWorkbook.Worksheets("Milestones&WBS").ListObjects("taskTable")

' 一次性读取整个数据区域到数组
Dim dataArray As Variant
dataArray = tbl.DataBodyRange.Value
Dim rowCount As Long
rowCount = UBound(dataArray, 1)

' 遍历数组处理数据
For i = 1 To rowCount
    Set task = New Dictionary

    ' 使用列的相对位置，而不是ListColumns的索引
    task.Add "ID", dataArray(i, 1) ' ID列通常是第1列

    ' 处理可能为null的Category
    If IsNull(dataArray(i, 2)) Then ' Category列通常是第2列
        task.Add "Category", ""
    Else
        task.Add "Category", dataArray(i, 2)
    End If

    task.Add "Description", dataArray(i, 3) ' Description列通常是第3列
    task.Add "Type", dataArray(i, 4) ' Type列通常是第4列
    task.Add "StartDate", dataArray(i, 5) ' Start Date列通常是第5列

    ' ... 其他字段
Next i
```

对于可选列（如Baseline），使用动态检测：

```vba
' 检查Baseline列是否存在
On Error Resume Next
baselineColIndex = tbl.ListColumns("Baseline").Index
hasBaselineCol = (Err.Number = 0)
On Error GoTo ErrorHandler

' 如果列存在，计算其在数组中的相对位置
If hasBaselineCol Then
    baselineColPos = baselineColIndex - idColIndex + 1

    ' 处理基准线数据
    If baselineColPos > 0 And baselineColPos <= UBound(dataArray, 2) Then
        If Not IsEmpty(dataArray(i, baselineColPos)) And IsDate(dataArray(i, baselineColPos)) Then
            baselineDate = dataArray(i, baselineColPos)
            task.Add "Baseline", baselineDate
        End If
    End If
End If
```

### 6.3 配置数据访问

#### 6.3.1 使用GetModuleConfig函数获取模块配置

```vba
' 获取模块配置，使用默认值
Dim uiConfig As Dictionary
Set uiConfig = GetModuleConfig("UI", modConfigDefaults.GetUIDefaults())

' 使用配置
With ws.Cells(1, 2)
    .Font.Name = uiConfig("ProjectNameFont")
    .Font.Size = uiConfig("ProjectNameFontSize")
    .Font.Bold = uiConfig("ProjectNameFontBold")
    .Font.Color = modUtilities.GetRGBColor(uiConfig("ProjectNameFontColor"))
End With
```

#### 6.3.2 使用GetConfig函数直接获取配置项

```vba
' 直接获取配置项的值，不考虑模块
Dim fontName As String
fontName = CStr(GetConfig("ProjectNameFont", "Arial"))

' 获取数值配置项
Dim fontSize As Double
fontSize = CDbl(Val(GetConfig("ProjectNameFontSize", 12)))

' 获取布尔配置项
Dim isBold As Boolean
isBold = CBool(GetConfig("ProjectNameFontBold", True))
```

#### 6.3.3 使用GetConfigFromDict函数从配置字典中获取单个值

```vba
' 从配置字典中获取值
Dim fontName As String
fontName = GetConfigFromDict(uiConfig, "ProjectNameFont", "Arial")
```

#### 6.3.4 在循环中优化配置访问

```vba
' 在循环外获取配置
Dim ganttConfig As Dictionary
Set ganttConfig = GetModuleConfig("Gantt", modConfigDefaults.GetGanttDefaults())
Dim taskHeight As Double
taskHeight = CDbl(ganttConfig("TaskBarHeight"))
Dim taskBarBorderWidth As Single
taskBarBorderWidth = CSng(Val(ganttConfig("TaskBarBorderWidth")))
Dim progressBarColor As String
progressBarColor = CStr(ganttConfig("ProgressBarColor"))

' 在循环中直接使用
For i = 1 To taskCount
    ' 使用预先获取的配置值，避免重复查询
    With taskShape
        .Height = taskHeight
        .Line.Weight = taskBarBorderWidth
        ' ...
    End With
    ' ...
Next i
```

#### 6.3.5 使用数组优化配置访问

```vba
' 获取列索引（相对于ListObject）
moduleCol = tbl.ListColumns("Module").Index
enabledCol = tbl.ListColumns("IsEnabled").Index
nameCol = tbl.ListColumns("ConfigName").Index
valueCol = tbl.ListColumns("ConfigValue").Index

' 一次性读取整个数据区域到数组
dataArray = tbl.DataBodyRange.Value

' 计算数组中的相对列索引
Dim moduleColArray As Long, enabledColArray As Long, nameColArray As Long, valueColArray As Long
Dim firstColIndex As Long

' 获取ListObject第一列的索引
firstColIndex = tbl.ListColumns(1).Index

' 计算数组中的相对列索引
moduleColArray = moduleCol - firstColIndex + 1
enabledColArray = enabledCol - firstColIndex + 1
nameColArray = nameCol - firstColIndex + 1
valueColArray = valueCol - firstColIndex + 1

' 遍历数组处理数据
For i = LBound(dataArray, 1) To UBound(dataArray, 1)
    ' 如果模块名称匹配且配置已启用
    If dataArray(i, moduleColArray) = moduleName And dataArray(i, enabledColArray) = True Then
        configName = dataArray(i, nameColArray)
        configValue = dataArray(i, valueColArray)
        result.Add configName, configValue
    End If
Next i
```

## 7. 错误处理

### 7.1 标准错误处理模式

模块中的所有公共函数都应包含错误处理代码：

```vba
Public Function SomeFunction() As Variant
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modData.SomeFunction"

    ' 函数主体代码
    ' ...

    ' 记录函数退出
    modDebug.LogFunctionExit "modData.SomeFunction", "成功"
    Exit Function

ErrorHandler:
    ' 记录错误
    modDebug.LogError Err.Number, Err.Description, "modData.SomeFunction"

    ' 返回适当的默认值
    SomeFunction = DefaultValue

    ' 记录函数退出（带错误信息）
    modDebug.LogFunctionExit "modData.SomeFunction", "错误: " & Err.Description
End Function
```

### 7.2 特殊错误处理模式

对于需要检查特定列是否存在的情况，使用以下模式：

```vba
' 检查Baseline列是否存在
On Error Resume Next
baselineColIndex = tbl.ListColumns("Baseline").Index
hasBaselineCol = (Err.Number = 0)
On Error GoTo ErrorHandler

' 如果列存在，处理该列数据
If hasBaselineCol Then
    ' 处理Baseline列数据
    ' ...
End If
```

### 7.3 资源清理

确保在错误处理中正确清理资源：

```vba
Public Sub SomeProcedure()
    On Error GoTo ErrorHandler

    ' 临时关闭屏幕更新和警告
    Application.ScreenUpdating = False
    Application.DisplayAlerts = False
    Application.Calculation = xlCalculationManual

    ' 函数主体代码
    ' ...

    ' 恢复设置
    Application.Calculation = xlCalculationAutomatic
    Application.DisplayAlerts = True
    Application.ScreenUpdating = True
    Exit Sub

ErrorHandler:
    ' 确保恢复设置
    Application.Calculation = xlCalculationAutomatic
    Application.DisplayAlerts = True
    Application.ScreenUpdating = True

    ' 记录错误
    modDebug.LogError Err.Number, Err.Description, "modData.SomeProcedure"
End Sub
```

## 8. 依赖关系

modData模块主要依赖：

1. modDebug - 用于日志记录和调试信息输出
2. modConfigDefaults - 用于获取默认配置值
3. modUtilities - 用于辅助函数，如颜色转换和日期计算

### 8.1 依赖关系图

```mermaid
graph TD
    modData --> modDebug[modDebug]
    modData --> modConfigDefaults[modConfigDefaults]
    modData --> modUtilities[modUtilities]
    modData --> Excel[Excel对象模型]
    modData --> VBA[VBA标准库]
    modConfigDefaults --> modDebug
    modUtilities --> modDebug
```

### 8.2 函数调用关系

| 调用方向 | 函数名 | 被调用模块.函数 |
|---------|--------|----------------|
| 调用 | GetModuleConfig | modConfigDefaults.GetUIDefaults |
| 调用 | GetModuleConfig | modConfigDefaults.GetGanttDefaults |
| 调用 | GetModuleConfig | modConfigDefaults.GetDataDefaults |
| 调用 | GetModuleConfig | modConfigDefaults.GetDebugDefaults |
| 调用 | ValidateAllData | modDebug.LogInfo |
| 调用 | ValidateProjectInfo | modDebug.LogWarning |
| 调用 | ValidateTasksData | modDebug.LogError |
| 调用 | GetAllTasks | modUtilities.CalculateWorkingDays |
| 被调用 | GetConfig | 多个模块 |
| 被调用 | GetModuleConfig | 多个模块 |
| 被调用 | GetAllTasks | modGantt.GenerateGanttChart |
| 被调用 | GetProjectInfo | modGantt.GenerateTimeline |
| 被调用 | ValidateAllData | modMain.GenerateGantt |
