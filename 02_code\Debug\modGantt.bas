'Attribute VB_Name = "modGantt"
Option Explicit

' =========================================================
' 模块: modGantt
' 描述: 负责甘特图的生成和管理
' =========================================================

' ---------------------------------------------------------
' 甘特图生成函数
' ---------------------------------------------------------

' 创建甘特图
Public Sub CreateGanttChart()
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modGantt.CreateGanttChart"

    ' 1. 获取项目信息和任务数据
    modDebug.LogInfo "步骤1: 获取项目信息和任务数据", "modGantt.CreateGanttChart"

    Dim projectInfo As Dictionary
    Set projectInfo = GetProjectInfo()

    ' 记录项目信息
    If Not projectInfo Is Nothing Then
        modDebug.LogInfo "项目信息获取成功", "modGantt.CreateGanttChart"

        ' 记录项目日期
        Dim startDateStr As String, endDateStr As String
        If projectInfo.Exists("StartDate") Then
            startDateStr = Format(projectInfo("StartDate"), "yyyy-mm-dd")
            modDebug.LogInfo "项目开始日期: " & startDateStr, "modGantt.CreateGanttChart"
        Else
            modDebug.LogWarning "项目开始日期不存在", "modGantt.CreateGanttChart"
        End If

        If projectInfo.Exists("EndDate") Then
            endDateStr = Format(projectInfo("EndDate"), "yyyy-mm-dd")
            modDebug.LogInfo "项目结束日期: " & endDateStr, "modGantt.CreateGanttChart"
        Else
            modDebug.LogWarning "项目结束日期不存在", "modGantt.CreateGanttChart"
        End If
    Else
        modDebug.LogWarning "项目信息获取失败", "modGantt.CreateGanttChart"
    End If

    Dim tasks As Collection
    Set tasks = GetAllTasks()

    ' 记录任务信息
    If Not tasks Is Nothing Then
        modDebug.LogInfo "任务数据获取成功, 总数: " & tasks.Count, "modGantt.CreateGanttChart"
    Else
        modDebug.LogWarning "任务数据获取失败", "modGantt.CreateGanttChart"
    End If

    ' 2. 创建时间轴（先创建时间轴，确定右边界）
    modDebug.LogInfo "步骤2: 创建时间轴", "modGantt.CreateGanttChart"

    If projectInfo.Exists("StartDate") And projectInfo.Exists("EndDate") Then
        modDebug.LogInfo "开始创建时间轴, 从 " & startDateStr & " 到 " & endDateStr, "modGantt.CreateGanttChart"
        CreateTimeline projectInfo("StartDate"), projectInfo("EndDate")
        modDebug.LogInfo "时间轴创建完成", "modGantt.CreateGanttChart"
    Else
        modDebug.LogError 0, "无法创建时间轴: 项目日期不完整", "modGantt.CreateGanttChart"
        Err.Raise 5, "modGantt.CreateGanttChart", "无法创建时间轴: 项目日期不完整"
    End If

    ' 注意：表头创建已在CreateTimeline函数中完成
    modDebug.LogInfo "步骤3: 甘特图表头已在时间轴创建过程中完成", "modGantt.CreateGanttChart"

    ' 4. 绘制任务和里程碑
    modDebug.LogInfo "步骤4: 绘制任务和里程碑", "modGantt.CreateGanttChart"
    DrawTasksAndMilestones tasks
    modDebug.LogInfo "任务和里程碑绘制完成", "modGantt.CreateGanttChart"

    ' 记录函数退出
    modDebug.LogFunctionExit "modGantt.CreateGanttChart", "成功"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modGantt.CreateGanttChart"
    Err.Raise Err.Number, "modGantt.CreateGanttChart", Err.description
End Sub



' 创建时间轴
Private Sub CreateTimeline(startDate As Date, endDate As Date)
    On Error GoTo ErrorHandler

    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("GanttChart")

    Dim switchDate As Date
    Dim headerCol As Long
    Dim lastMonth As Integer, lastYear As Integer
    Dim monthStart As range, yearStart As range
    Dim col_Width_factor As Single
    Dim headerRange As range


    ' Set all cells to have white background
    ws.Cells.Interior.Color = RGB(255, 255, 255)
    ' 设置时间轴区域的文字居中对齐（从C列开始，不包括A、B列）
    ws.Range("C:XFD").HorizontalAlignment = xlCenter

    ' Set the header origin point at B1
    Set headerRange = ws.range("B1")

    ' 获取CellWidthFactor参数
    Dim cellWidthFactor As Double
    cellWidthFactor = CDbl(GetConfig("CellWidthFactor", 1.2))

    ' 设置列宽因子
    col_Width_factor = cellWidthFactor

    ' 设置起始列
    headerCol = 3 ' 从 C 列开始

  ' 计算生成日历表头的起始日期（项目开始日所在月份的第一天）
    startDate = DateSerial(Year(startDate), Month(startDate), 1)
    ' 计算生成日历表头的结束日期（项目结束日所在月份的最后一天）
    endDate = DateSerial(Year(endDate), Month(endDate) + 1, 0)

    ' 初始化循环日期为日历范围起始日期 (startDate) 所在日历周（周一为一周开始）的星期一
    switchDate = startDate + 1 - Weekday(startDate, vbMonday)


   ' 循环遍历，以周为单位生成表头列，直到超过结束日期
    Do While switchDate <= endDate
        ' 在当前的 headerRange 位置进行操作
        With headerRange
            ' 判断当前周 (switchDate 到 switchDate + 6) 是否跨越月份边界
            If Month(switchDate) <> Month(switchDate + 6) Then
                ' 处理跨月情况下的第一个周（如果项目开始日在该跨月周的后半段，且 switchDate 是周一，但 startDate 在那周中间）
                If (Month(switchDate) Mod 12) + 1 = Month(startDate) And switchDate < startDate Then
                    ' 计算并写入跨月周后半段（下一月部分）的 ISO 周数到相对于 headerRange 向下偏移4行、向右偏移1列的单元格 (即表头第5行，当前列右边一列)
                    .Offset(4, 1).value = WorksheetFunction.IsoWeekNum(switchDate + 6)
                    .Offset(4, 1).NumberFormat = "00" ' 格式化周数为两位数字

                    ' 写入跨月周后半段的月份到表头第4行，当前列右边一列
                    .Offset(3, 1).value = Month(switchDate + 6)
                    .Offset(3, 1).NumberFormat = "00" ' 格式化月份为两位数字

                    ' 写入跨月周后半段的年份到表头第3行，当前列右边一列
                    .Offset(2, 1).value = Year(switchDate + 6)
                    ' 设置该列的宽度，根据项目开始日到周日的天数乘以系数
                    .Offset(4, 1).EntireColumn.columnWidth = (7 - Weekday(startDate, vbMonday) + 1) * col_Width_factor
                    ' 将 headerRange 向右移动一列，准备写入下一列数据
                    Set headerRange = headerRange.Offset(0, 1)
                    ' Debug输出信息
                    'Debug.Print "the first blood (split, part B) in CW " & WorksheetFunction.IsoWeekNum(switchDate + 6) & ", DAYS: " & (7 - Weekday(startDate, vbMonday) + 1)
                    'Debug.Print "headerRange is " & headerRange.Address


                ' 处理跨月情况下的最后一个周（如果项目结束日在一个不完整的周内，并且该周跨月）
                ElseIf switchDate = endDate - Weekday(endDate, vbMonday) + 1 Then
                    ' 计算并写入跨月周前半段（当前月部分）的 ISO 周数到相对于 headerRange 向下偏移4行、当前列的单元格 (即表头第5行)
                    .Offset(4, 0).value = WorksheetFunction.IsoWeekNum(switchDate)
                    .Offset(4, 0).NumberFormat = "00"

                    ' 写入跨月周前半段的月份到表头第4行，当前列
                    .Offset(3, 0).value = Month(switchDate)
                    .Offset(3, 0).NumberFormat = "00"

                    ' 写入跨月周前半段的年份到表头第3行，当前列
                    .Offset(2, 0).value = Year(switchDate)
                    ' 设置该列的宽度，根据项目结束日在周内是第几天乘以系数
                    .EntireColumn.columnWidth = Weekday(endDate, vbMonday) * col_Width_factor
                    ' Debug输出信息
                    'Debug.Print "the last blood in CW " & WorksheetFunction.IsoWeekNum(switchDate) & ", DAYS: " & Weekday(endDate, vbMonday)

                ' 处理中间跨月的完整周（将该周拆分为两列显示，一列显示当前月部分，一列显示下一月部分）
                ElseIf switchDate > startDate And switchDate < endDate - Weekday(endDate, vbMonday) + 1 Then
                    ' 计算并写入当前月剩余天数所在的周的 ISO 周数到当前列的表头第5行 (周数)
                    ' 注意：此处使用下个月第一天计算周数，逻辑特殊，可能意图是该跨月周的整体周数
                    .Offset(4, 0).value = WorksheetFunction.IsoWeekNum(DateSerial(Year(switchDate), Month(switchDate) + 1, 0))
                    .Offset(4, 0).NumberFormat = "00"

                    ' 写入当前月剩余天数所在周的月份到当前列的表头第4行 (月份)
                    .Offset(3, 0).value = Month(switchDate)
                    .Offset(3, 0).NumberFormat = "00"
                    ' 设置当前列的宽度，可能基于当前月在该周的天数乘以系数
                    .Offset(4, 0).EntireColumn.columnWidth = Weekday(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), vbMonday) * col_Width_factor
                    ' 写入当前月剩余天数所在周的年份到当前列的表头第3行 (年份)
                    .Offset(2, 0).value = Year(switchDate)

            'Debug.Print "Mix--------------------killing_A in CW " & WorksheetFunction.IsoWeekNum(switchDate) & ", DAYS: " & Weekday(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), vbMonday)
                    ' 计算并写入下一个月开始天数所在的周的 ISO 周数到右边一列的表头第5行 (周数)
                    ' 注意：此处使用周日 (switchDate + 6) 计算周数
                    .Offset(4, 1).value = WorksheetFunction.IsoWeekNum(switchDate + 6)
                    .Offset(4, 1).NumberFormat = "00"

                    ' 写入下一个月开始天数所在的周的月份到右边一列的表头第4行 (月份)
                    .Offset(3, 1).value = Month(switchDate + 6)
                    .Offset(3, 1).NumberFormat = "00"

                    ' 写入下一个月开始天数所在的周的年份到右边一列的表头第3行 (年份)
                    .Offset(2, 1).value = Year(switchDate + 6)
                    ' 设置右边一列的宽度，可能基于下个月在该周的天数乘以系数
                    .Offset(4, 1).EntireColumn.columnWidth = (7 - Weekday(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), vbMonday)) * col_Width_factor
                    ' 将 headerRange 向右移动一列（处理完跨月周的第二部分后移动）
                    Set headerRange = headerRange.Offset(0, 1)
                    ' Debug输出信息
                    'Debug.Print "headerRange is " & headerRange.Address
                    'Debug.Print "Mix----------------------------killing_B in CW " & WorksheetFunction.IsoWeekNum(switchDate + 6) & ", DAYS: " & (7 - Weekday(DateSerial(Year(switchDate), Month(switchDate) + 1, 0), vbMonday))

                End If
            ' 处理不跨月情况下的第一个周（项目开始日期落在当前 switchDate 所在的周内，且该周不跨月）
            ElseIf switchDate <= startDate And startDate <= switchDate + 6 Then
                 ' 确保项目开始日与当前周在同一个月份
                 If Month(switchDate) = Month(startDate) Then
                    ' 计算并写入第一周（从项目开始日到周日的不完整周）的 ISO 周数到当前列的表头第5行
                    ' 使用项目开始日计算周数、月份和年份
                    .Offset(4, 1).value = WorksheetFunction.IsoWeekNum(startDate)
                    .Offset(4, 1).NumberFormat = "00"

                    .Offset(3, 1).value = Month(startDate)
                    .Offset(3, 1).NumberFormat = "00"

                    .Offset(2, 1).value = Year(startDate)
                    ' 设置该列的宽度，根据项目开始日到该周末的天数乘以系数
                    .Offset(4, 1).EntireColumn.columnWidth = (7 - Weekday(startDate, vbMonday) + 1) * col_Width_factor
                    ' Debug输出信息
                    'Debug.Print "the first blood (not split) in CW " & WorksheetFunction.IsoWeekNum(startDate) & ", DAYS: " & (7 - Weekday(startDate, vbMonday) + 1)
                                        Set headerRange = headerRange.Offset(0, 1)
                    ' Debug输出信息
                    'Debug.Print "Perfect-shot (at beginning, year crossover) in CW " & WorksheetFunction.IsoWeekNum(switchDate) & ", DAYS: 7"
                 Else ' 如果是跨年但不跨月的第一周（例如年底最后一周跨年到下一年1月）
                     ' 写入完整周的周、月、年信息到当前列
                    .Offset(4, 1).value = WorksheetFunction.IsoWeekNum(switchDate)
                    .Offset(4, 1).NumberFormat = "00"

                    .Offset(3, 1).value = Month(switchDate)
                    .Offset(3, 1).NumberFormat = "00"

                    .Offset(2, 1).value = Year(switchDate)
                    ' 设置列宽为标准宽度（7天乘以系数）
                    .EntireColumn.columnWidth = 7 * col_Width_factor
                    Set headerRange = headerRange.Offset(0, 1)
                    ' Debug输出信息
                    'Debug.Print "Perfect-shot (at beginning, year crossover) in CW " & WorksheetFunction.IsoWeekNum(switchDate) & ", DAYS: 7"

                    'Debug.Print "2nd headerRange is " & headerRange.Address
                 End If

            ' 处理不跨月且不是第一周或最后一-周的完整周
            Else
                ' 计算并写入完整周的 ISO 周数到当前列的表头第5行
                .Offset(4, 0).value = WorksheetFunction.IsoWeekNum(switchDate)
                .Offset(4, 0).NumberFormat = "00"

                ' 写入完整周的月份到当前列的表头第4行
                .Offset(3, 0).value = Month(switchDate)
                .Offset(3, 0).NumberFormat = "00"

                ' 写入完整周的年份到当前列的表头第3行
                .Offset(2, 0).value = Year(switchDate)
                ' 设置列宽为标准宽度（7天乘以系数）
                .EntireColumn.columnWidth = 7 * col_Width_factor
                ' Debug输出信息
                'Debug.Print "Perfect-shot in CW " & WorksheetFunction.IsoWeekNum(switchDate) & ", DAYS: 7"
            End If

        End With


        Set headerRange = headerRange.Offset(0, 1)
   'Debug.Print "next loop headerRange is " & headerRange.Address
        ' 将 switchDate 推进到下一个星期的星期一，准备处理下一周
        switchDate = switchDate + 7

    Loop ' 循环结束


    ' 声明合并相关的变量
    Dim lastCol As Long
    Dim i As Integer, j As Integer
    Dim mergeRange As range

    ' 确定表头数据写入的最右边的列（以表头第3行，即 B1 偏移 2 行的行作为参考）
    lastCol = ws.Cells(3, ws.Columns.Count).End(xlToLeft).Column

    ' 关闭警告消息，以便进行单元格合并操作
    Application.DisplayAlerts = False

    ' 循环遍历需要合并的表头行：表头第3行（年份）、第4行（月份）、第5行（周数）
    ' 这些行对应于 headerRange (B1) 向下偏移 2, 3, 4 的行，即工作表的第 3, 4, 5 行
    For i = 3 To 5
        ' 重置合并范围
        Set mergeRange = Nothing
        ' 从第3列 (C列，因为 headerRange 是 B1，第一个数据列从 B 列开始，合并从下一列 C 列开始) 开始遍历到最后一列
        For j = 3 To lastCol
            ' 如果当前合并范围不为空，并且当前单元格的值与前一个单元格的值相同，并且当前单元格没有被合并
            If Not mergeRange Is Nothing And ws.Cells(i, j).value = ws.Cells(i, j - 1).value And Not ws.Cells(i, j).MergeCells Then
                ' 将当前单元格添加到当前的合并范围中
                Set mergeRange = ws.range(mergeRange, ws.Cells(i, j))
            Else
                ' 如果当前单元格与前一个单元格的值不同，或者开始一个新的合并范围
                ' 如果之前的合并范围不为空，则进行合并
                If Not mergeRange Is Nothing Then
                    ' 如果合并范围包含多于一个单元格，则执行合并
                    If mergeRange.Columns.Count > 1 Then
                        mergeRange.Merge
                        ' 可选：为合并后的单元格添加格式（如边框）
                        ' mergeRange.Borders.LineStyle = xlThin
                    End If
                End If
                ' 将当前的单元格设置为新的合并范围的起始
                Set mergeRange = ws.Cells(i, j)
            End If
        Next j
        ' 循环结束后，检查并合并最后一个累积的合并范围
        If Not mergeRange Is Nothing And mergeRange.Columns.Count > 1 Then
            mergeRange.Merge
            ' 可选：为合并后的单元格添加格式
            ' mergeRange.Borders.LineStyle = xlThin
        End If
    Next i

    ' 重新开启警告消息
    Application.DisplayAlerts = True

    ' 可选：为表头区域添加边框并设置字体样式
    ' 边框范围从 C3 单元格到第5行的最后一列
     ws.range("C3", ws.Cells(5, lastCol)).Borders.lineStyle = xlContinuous
     ' 设置字体不加粗 (根据您提供的代码修改，原始代码有 Bold = True 的注释)
     ws.range("C3", ws.Cells(5, lastCol)).Font.Bold = False


    ' 合并项目信息单元格
    MergeProjectInfoCells ws, lastCol

    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modGantt.CreateTimeline"
    Err.Raise Err.Number, "modGantt.CreateTimeline", Err.description
End Sub

' 获取时间轴结束列
Private Function GetTimelineEndColumn(ws As Worksheet) As Long
    On Error GoTo ErrorHandler

    Dim endCol As Long
    endCol = 3 ' 从 C 列开始

    ' 查找最后一列（周数行上有值的最后一列）
    Do While ws.Cells(5, endCol).value <> ""
        endCol = endCol + 1
        ' 防止无限循环
        If endCol > 1000 Then Exit Do
    Loop

    GetTimelineEndColumn = endCol - 1
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modGantt.GetTimelineEndColumn"
    GetTimelineEndColumn = 3 ' 返回默认值
End Function

' 合并项目信息单元格
Private Sub MergeProjectInfoCells(ws As Worksheet, endCol As Long)
    On Error GoTo ErrorHandler

    ' 关闭警告消息
    Application.DisplayAlerts = False

    ' 获取项目信息
    Dim projectInfo As Dictionary
    Set projectInfo = GetProjectInfo()

    ' 合并项目名称单元格
    ws.range(ws.Cells(1, 2), ws.Cells(1, endCol)).Merge
    ws.Cells(1, 2).value = projectInfo("ProjectName")
    ws.Cells(1, 2).HorizontalAlignment = xlLeft
    ws.Cells(1, 2).Font.size = 16
    ws.Cells(1, 2).Font.Bold = True
    ws.Cells(1, 2).Font.Name = "Barlow"
    ws.Cells(1, 2).Font.Color = RGB(0, 0, 0)

    ' 合并项目经理单元格
    ws.range(ws.Cells(2, 2), ws.Cells(2, endCol)).Merge
    ws.Cells(2, 2).value = "Project Manager: " & projectInfo("ProjectManager")
    ws.Cells(2, 2).HorizontalAlignment = xlLeft
    ws.Cells(2, 2).Font.size = 10
    ws.Cells(2, 2).Font.Name = "Barlow"
    ws.Cells(2, 2).Font.Color = RGB(0, 0, 0)

    ' 重新开启警告消息
    Application.DisplayAlerts = True

    Exit Sub

ErrorHandler:
    Application.DisplayAlerts = True
    modDebug.LogError Err.Number, Err.description, "modGantt.MergeProjectInfoCells"
    Err.Raise Err.Number, "modGantt.MergeProjectInfoCells", Err.description
End Sub



' 绘制任务和里程碑
Private Sub DrawTasksAndMilestones(tasks As Collection)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modGantt.DrawTasksAndMilestones", "任务数量: " & tasks.Count

    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("GanttChart")
    modDebug.LogVerbose "获取GanttChart工作表引用成功", "modGantt.DrawTasksAndMilestones"

    ' 如果没有任务，直接返回
    If tasks.Count = 0 Then
        modDebug.LogWarning "任务集合为空，无需绘制", "modGantt.DrawTasksAndMilestones"
        Exit Sub
    End If

    ' 获取项目信息
    Dim projectInfo As Dictionary
    Set projectInfo = GetProjectInfo()
    modDebug.LogVerbose "获取项目信息成功，项目开始日期: " & Format(projectInfo("StartDate"), "yyyy-mm-dd") & _
                       "，结束日期: " & Format(projectInfo("EndDate"), "yyyy-mm-dd"), "modGantt.DrawTasksAndMilestones"

    ' 建立时间轴坐标系（提前创建，避免重复创建）
    modDebug.LogInfo "开始建立时间轴坐标系（优化：只创建一次）", "modGantt.DrawTasksAndMilestones"
    Dim timelineCoords As Dictionary
    Set timelineCoords = EstablishTimelineCoordinateSystem(ws, projectInfo)
    modDebug.LogInfo "时间轴坐标系创建成功 - 原点X: " & timelineCoords("OriginX") & _
                    ", 宽度: " & timelineCoords("Width") & _
                    ", 起始日期: " & Format(timelineCoords("StartDate"), "yyyy-mm-dd") & _
                    ", 结束日期: " & Format(timelineCoords("EndDate"), "yyyy-mm-dd") & _
                    ", 总天数: " & timelineCoords("TotalDays"), "modGantt.DrawTasksAndMilestones"

    ' 初始化行位置
    Dim currentRow As Long
    currentRow = 6  ' 从第6行开始（前5行是时间轴表头）

    ' 当前类别
    Dim currentCategory As String
    currentCategory = ""

    ' 当前行的类别是否已设置
    Dim isCurrentRowCategorySet As Boolean
    isCurrentRowCategorySet = False

    ' 上一个任务的行位置
    Dim lastTaskRow As Long
    lastTaskRow = currentRow


    ' 初始化基准线收集变量（将在任务遍历中同时收集基准线信息）
    Dim baselineCollection As New Collection
    Dim baselineDates As New Dictionary ' 用于跟踪已处理的基准线日期
    Dim i As Long, task As Dictionary
    Dim taskId As String, taskDesc As String, taskType As String
    Dim baselineDate As Date, baselineDateStr As String, baselineInfo As Dictionary

    modDebug.LogInfo "将在任务遍历过程中同时收集基准线信息", "modGantt.DrawTasksAndMilestones"

    modDebug.LogVerbose "初始化变量完成 - 初始行位置: " & currentRow & ", 初始类别: " & currentCategory, "modGantt.DrawTasksAndMilestones"
    modDebug.LogInfo "开始遍历任务集合（优化流程：收集基准线 -> 行位置确定和类别处理 -> 绘制任务/里程碑）", "modGantt.DrawTasksAndMilestones"

    ' 遍历每个任务
    For i = 1 To tasks.Count
        Set task = tasks(i)

        ' 记录任务基本信息
        taskId = IIf(task.Exists("ID"), task("ID"), "未知ID")
        taskDesc = IIf(task.Exists("Description"), task("Description"), "未知描述")
        taskType = IIf(task.Exists("Type"), task("Type"), "未知类型")

        modDebug.LogInfo "开始处理第 " & i & "/" & tasks.Count & " 个任务 - ID: " & taskId & ", 描述: " & taskDesc & ", 类型: " & taskType, "modGantt.DrawTasksAndMilestones"

        ' 收集基准线信息（在同一次遍历中完成）
        If task.Exists("Baseline") Then
            baselineDate = task("Baseline")
            baselineDateStr = Format(baselineDate, "yyyy-mm-dd")

            ' 检查是否已经处理过相同日期的基准线
            If Not baselineDates.Exists(baselineDateStr) Then
                baselineDates.Add baselineDateStr, True

                ' 创建基准线信息字典
                Set baselineInfo = New Dictionary
                baselineInfo.Add "Date", baselineDate
                baselineInfo.Add "TaskID", taskId

                ' 添加到基准线集合
                baselineCollection.Add baselineInfo

                modDebug.LogInfo "提取基准线信息 - 任务ID: " & taskId & ", 日期: " & baselineDateStr, "modGantt.DrawTasksAndMilestones"
            Else
                modDebug.LogVerbose "跳过重复的基准线日期: " & baselineDateStr, "modGantt.DrawTasksAndMilestones"
            End If
        End If

        ' 第一步：确定任务行位置并处理类别信息（整合了两个步骤）
        modDebug.LogVerbose "第一步：确定任务 " & taskId & " 的行位置并处理类别信息", "modGantt.DrawTasksAndMilestones"
        Dim taskRow As Long
        taskRow = DetermineTaskRowAndCategory(task, ws, currentRow, lastTaskRow, i, currentCategory, isCurrentRowCategorySet)

        ' 检查是否发生错误（返回-1）
        If taskRow = -1 Then
            modDebug.LogError 1001, "任务 " & taskId & " 的行位置确定失败，中断甘特图生成", "modGantt.DrawTasksAndMilestones"
            modDebug.LogFunctionExit "modGantt.DrawTasksAndMilestones", "失败 - 任务行位置确定失败"
            Exit Sub
        End If

        modDebug.LogVerbose "任务 " & taskId & " 的行位置确定为: " & taskRow, "modGantt.DrawTasksAndMilestones"

        ' 记录旧的行位置值（用于日志）
        Dim oldLastTaskRow As Long, oldCurrentRow As Long
        oldLastTaskRow = lastTaskRow
        oldCurrentRow = currentRow

        ' 直接使用任务行作为当前行和上一任务行
        ' 类别标题和任务可以共存于同一行，不会冲突
        currentRow = taskRow
        lastTaskRow = taskRow

        modDebug.LogVerbose "更新行位置 - 上一任务行: " & oldLastTaskRow & " -> " & lastTaskRow & ", 当前行: " & oldCurrentRow & " -> " & currentRow, "modGantt.DrawTasksAndMilestones"

        ' 第三步：根据任务类型绘制任务或里程碑
        modDebug.LogVerbose "第三步：绘制任务 " & taskId & " 的图形元素", "modGantt.DrawTasksAndMilestones"
        If task.Exists("Type") Then
            If task("Type") = "A" Then
                ' 绘制任务条
                modDebug.LogInfo "绘制任务条 - ID: " & taskId & ", 行: " & taskRow, "modGantt.DrawTasksAndMilestones"
                DrawTask task, taskRow, timelineCoords
            ElseIf task("Type") = "M" Then
                ' 绘制里程碑
                modDebug.LogInfo "绘制里程碑 - ID: " & taskId & ", 行: " & taskRow, "modGantt.DrawTasksAndMilestones"
                DrawMilestone task, taskRow, timelineCoords
            Else
                modDebug.LogWarning "任务 " & taskId & " 类型无效: " & task("Type") & "，跳过绘制", "modGantt.DrawTasksAndMilestones"
            End If
        Else
            modDebug.LogWarning "任务 " & taskId & " 缺少Type属性，跳过绘制", "modGantt.DrawTasksAndMilestones"
        End If

        ' 基准线信息已在任务处理过程中同时收集
        modDebug.LogVerbose "任务 " & taskId & " 处理完成（基准线信息已在任务处理过程中同时收集）", "modGantt.DrawTasksAndMilestones"

        modDebug.LogInfo "任务 " & taskId & " 处理完成", "modGantt.DrawTasksAndMilestones"
    Next i

    modDebug.LogInfo "所有任务绘制完成，共 " & tasks.Count & " 个任务，收集到 " & baselineCollection.Count & " 个唯一基准线", "modGantt.DrawTasksAndMilestones"

    ' 记录最后一行位置（用于基准线绘制）
    Dim lastRowPosition As Long

    ' 使用UsedRange属性获取实际使用的最后一行
    Dim usedLastRow As Long
    usedLastRow = ws.UsedRange.Rows(ws.UsedRange.Rows.Count).Row

    ' 确保最后一行不小于当前行和第6行（甘特图内容起始行）
    lastRowPosition = Application.WorksheetFunction.Max(usedLastRow, currentRow, 6)

    modDebug.LogInfo "记录最后一行位置: " & lastRowPosition & "（使用UsedRange计算，实际使用的最后一行: " & usedLastRow & "，当前行: " & currentRow & "）", "modGantt.DrawTasksAndMilestones"

    ' 统一处理所有基准线
    modDebug.LogInfo "开始统一处理所有基准线，共 " & baselineCollection.Count & " 个基准线", "modGantt.DrawTasksAndMilestones"
    If baselineCollection.Count > 0 Then
        Dim j As Long, baseline As Dictionary
        For j = 1 To baselineCollection.Count
            Set baseline = baselineCollection(j)

            modDebug.LogInfo "处理基准线 " & j & "/" & baselineCollection.Count & " - 任务ID: " & baseline("TaskID") & ", 日期: " & Format(baseline("Date"), "yyyy-mm-dd"), "modGantt.DrawTasksAndMilestones"
            DrawBaselineWithBounds baseline("Date"), ws, timelineCoords, 6, lastRowPosition
        Next j
        modDebug.LogInfo "所有基准线处理完成", "modGantt.DrawTasksAndMilestones"
    Else
        modDebug.LogInfo "没有基准线需要处理", "modGantt.DrawTasksAndMilestones"
    End If

    ' 检查是否需要绘制当前日期线
    Dim enableCurrentDateLine As Boolean
    enableCurrentDateLine = CBool(GetConfig("EnableCurrentDateLine", False))

    If enableCurrentDateLine Then
        modDebug.LogInfo "当前日期线功能已启用，开始绘制当前日期线", "modGantt.DrawTasksAndMilestones"
        ' 获取当前日期
        Dim currentDate As Date
        currentDate = Date

        ' 检查当前日期是否在时间轴范围内
        If currentDate >= timelineCoords("StartDate") And currentDate <= timelineCoords("EndDate") Then
            modDebug.LogInfo "当前日期 " & Format(currentDate, "yyyy-mm-dd") & " 在时间轴范围内，绘制当前日期线", "modGantt.DrawTasksAndMilestones"
            DrawCurrentDateLine currentDate, ws, timelineCoords, 6, lastRowPosition
        Else
            modDebug.LogInfo "当前日期 " & Format(currentDate, "yyyy-mm-dd") & " 不在时间轴范围内，跳过绘制当前日期线", "modGantt.DrawTasksAndMilestones"
        End If
    Else
        modDebug.LogInfo "当前日期线功能已禁用，跳过绘制当前日期线", "modGantt.DrawTasksAndMilestones"
    End If

    ' 合并类别标题区域
    modDebug.LogInfo "开始合并类别标题区域", "modGantt.DrawTasksAndMilestones"
    MergeCategoryTitles ws, 6, lastRowPosition
    modDebug.LogInfo "类别标题区域合并完成", "modGantt.DrawTasksAndMilestones"

    modDebug.LogFunctionExit "modGantt.DrawTasksAndMilestones", "成功 - 优化流程完成"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modGantt.DrawTasksAndMilestones"
    modDebug.LogFunctionExit "modGantt.DrawTasksAndMilestones", "失败 - " & Err.description
    Err.Raise Err.Number, "modGantt.DrawTasksAndMilestones", Err.description
End Sub

' 注意：DetermineTaskRow函数已被移除，其功能已整合到DetermineTaskRowAndCategory函数中

' 确定任务的行位置并处理类别信息（整合了DetermineTaskRow和ProcessTaskCategory）
Private Function DetermineTaskRowAndCategory(task As Dictionary, ws As Worksheet, currentRow As Long, lastTaskRow As Long, _
                                           taskIndex As Long, ByRef currentCategory As String, _
                                           ByRef isCurrentRowCategorySet As Boolean) As Long
    On Error GoTo ErrorHandler

    ' 获取任务ID和描述（用于日志）
    Dim taskId As String, taskDesc As String
    taskId = IIf(task.Exists("ID"), task("ID"), "未知ID")
    taskDesc = IIf(task.Exists("Description"), task("Description"), "未知描述")

    modDebug.LogFunctionEntry "modGantt.DetermineTaskRowAndCategory", "任务ID: " & taskId & ", 描述: " & taskDesc & ", 当前行: " & currentRow & ", 上一任务行: " & lastTaskRow & ", 任务索引: " & taskIndex

    ' 获取任务的Position属性
    Dim taskPosition As Variant
    Dim resultRow As Long
    Dim errorMsg As String ' 提前声明，避免重复声明

    If task.Exists("Position") Then
        taskPosition = task("Position")
        modDebug.LogVerbose "任务 " & taskId & " 的Position属性: " & taskPosition & " (类型: " & TypeName(taskPosition) & ")", "modGantt.DetermineTaskRowAndCategory"
    Else
        taskPosition = "next" ' 默认为next
        modDebug.LogVerbose "任务 " & taskId & " 无Position属性，使用默认值: next", "modGantt.DetermineTaskRowAndCategory"
    End If

    ' 获取任务类别
    Dim taskCategory As String
    taskCategory = ""
    If task.Exists("Category") Then
        taskCategory = task("Category")
        modDebug.LogVerbose "任务 " & taskId & " 的Category属性: " & taskCategory, "modGantt.DetermineTaskRowAndCategory"
    Else
        modDebug.LogVerbose "任务 " & taskId & " 无Category属性", "modGantt.DetermineTaskRowAndCategory"
    End If

    ' 根据taskIndex分为两大类处理
    If taskIndex = 1 Then
        ' ===== 处理第一个任务 =====
        modDebug.LogVerbose "处理首个任务 " & taskId, "modGantt.DetermineTaskRowAndCategory"

        ' 根据Position类型确定行位置
        If (TypeName(taskPosition) = "String" And taskPosition = "same") Or _
           ((TypeName(taskPosition) = "Double" Or TypeName(taskPosition) = "Integer") And taskPosition = 0) Then
            ' same或0，对于第一个任务使用currentRow
            resultRow = currentRow
            modDebug.LogVerbose "首个任务，Position='same'或0，使用当前行: " & resultRow, "modGantt.DetermineTaskRowAndCategory"
        ElseIf TypeName(taskPosition) = "String" And taskPosition = "next" Then
            ' next，使用currentRow + 1
            resultRow = currentRow + 1
            modDebug.LogVerbose "首个任务，Position='next'，使用下一行: " & resultRow, "modGantt.DetermineTaskRowAndCategory"
        ElseIf TypeName(taskPosition) = "Double" Or TypeName(taskPosition) = "Integer" Then
            ' 数字类型，使用currentRow + taskPosition
            resultRow = currentRow + CInt(taskPosition)
            modDebug.LogVerbose "首个任务，Position=" & taskPosition & "，计算行位置: " & currentRow & " + " & CInt(taskPosition) & " = " & resultRow, "modGantt.DetermineTaskRowAndCategory"

            ' 检查行位置是否小于最小行位置
            If resultRow < 6 Then
                errorMsg = "任务 " & taskId & " (" & taskDesc & ") 的行位置计算结果(" & resultRow & ")小于最小行位置(6)。" & vbCrLf & _
                          "请检查任务的Position属性值: " & taskPosition

                modDebug.LogError 1000, errorMsg, "modGantt.DetermineTaskRowAndCategory"
                MsgBox errorMsg, vbCritical, "行位置错误"

                ' 记录函数退出信息
                modDebug.LogFunctionExit "modGantt.DetermineTaskRowAndCategory", "失败 - 行位置小于最小值"

                ' 直接退出函数
                DetermineTaskRowAndCategory = -1 ' 返回-1表示错误
                Exit Function
            End If
        Else
            ' 其他情况，默认使用currentRow + 1
            resultRow = currentRow + 1
            modDebug.LogWarning "首个任务，Position类型无效: " & TypeName(taskPosition) & "，默认使用next，行位置: " & resultRow, "modGantt.DetermineTaskRowAndCategory"
        End If

        ' 重置行类别设置状态
        isCurrentRowCategorySet = False

        ' 处理类别信息
        If taskCategory <> "" Then
            ' 如果有非空类别，设置当前类别并添加类别标题
            currentCategory = taskCategory
            isCurrentRowCategorySet = True

            modDebug.LogInfo "首个任务，添加类别标题: " & currentCategory & " 在行 " & resultRow, "modGantt.DetermineTaskRowAndCategory"
            ws.Cells(resultRow, 2).value = currentCategory
            ws.Cells(resultRow, 2).Font.Bold = True
            ws.Cells(resultRow, 2).Font.size = 11
            ws.Cells(resultRow, 2).HorizontalAlignment = xlLeft
            ws.Cells(resultRow, 2).VerticalAlignment = xlCenter
        End If
    Else
        ' ===== 处理后续任务 =====
        modDebug.LogVerbose "处理后续任务 " & taskId & " (索引: " & taskIndex & ")", "modGantt.DetermineTaskRowAndCategory"

        ' 根据Position类型确定行位置
        If (TypeName(taskPosition) = "String" And taskPosition = "same") Or _
           ((TypeName(taskPosition) = "Double" Or TypeName(taskPosition) = "Integer") And taskPosition = 0) Then
            ' same或0，使用lastTaskRow
            resultRow = lastTaskRow
            modDebug.LogVerbose "后续任务，Position='same'或0，使用上一任务行: " & resultRow, "modGantt.DetermineTaskRowAndCategory"

            ' 如果当前行的类别尚未设置且任务有非空类别
            If Not isCurrentRowCategorySet And taskCategory <> "" Then
                currentCategory = taskCategory
                isCurrentRowCategorySet = True

                modDebug.LogInfo "后续任务，same位置，添加类别标题: " & currentCategory & " 在行 " & resultRow, "modGantt.DetermineTaskRowAndCategory"
                ws.Cells(resultRow, 2).value = currentCategory
                ws.Cells(resultRow, 2).Font.Bold = True
                ws.Cells(resultRow, 2).Font.size = 11
                ws.Cells(resultRow, 2).HorizontalAlignment = xlLeft
                ws.Cells(resultRow, 2).VerticalAlignment = xlCenter
            End If
        ElseIf TypeName(taskPosition) = "String" And taskPosition = "next" Then
            ' next，使用currentRow + 1
            resultRow = currentRow + 1
            modDebug.LogVerbose "后续任务，Position='next'，使用下一行: " & resultRow, "modGantt.DetermineTaskRowAndCategory"

            ' 重置行类别设置状态
            isCurrentRowCategorySet = False

            ' 如果任务有非空类别
            If taskCategory <> "" Then
                currentCategory = taskCategory
                isCurrentRowCategorySet = True

                modDebug.LogInfo "后续任务，next位置，添加类别标题: " & currentCategory & " 在行 " & resultRow, "modGantt.DetermineTaskRowAndCategory"
                ws.Cells(resultRow, 2).value = currentCategory
                ws.Cells(resultRow, 2).Font.Bold = True
                ws.Cells(resultRow, 2).Font.size = 11
                ws.Cells(resultRow, 2).HorizontalAlignment = xlLeft
                ws.Cells(resultRow, 2).VerticalAlignment = xlCenter
            End If
        ElseIf TypeName(taskPosition) = "Double" Or TypeName(taskPosition) = "Integer" Then
            ' 数字类型，使用currentRow + taskPosition
            resultRow = currentRow + CInt(taskPosition)
            modDebug.LogVerbose "后续任务，Position=" & taskPosition & "，计算行位置: " & currentRow & " + " & CInt(taskPosition) & " = " & resultRow, "modGantt.DetermineTaskRowAndCategory"

            ' 检查行位置是否小于最小行位置
            If resultRow < 6 Then
                errorMsg = "任务 " & taskId & " (" & taskDesc & ") 的行位置计算结果(" & resultRow & ")小于最小行位置(6)。" & vbCrLf & _
                          "请检查任务的Position属性值: " & taskPosition

                modDebug.LogError 1000, errorMsg, "modGantt.DetermineTaskRowAndCategory"
                MsgBox errorMsg, vbCritical, "行位置错误"

                ' 记录函数退出信息
                modDebug.LogFunctionExit "modGantt.DetermineTaskRowAndCategory", "失败 - 行位置小于最小值"

                ' 直接退出函数
                DetermineTaskRowAndCategory = -1 ' 返回-1表示错误
                Exit Function
            End If

            ' 对于数字类型的Position，不处理Category（根据您的建议）
            ' 但需要重置行类别设置状态
            isCurrentRowCategorySet = False
        Else
            ' 其他情况，默认使用currentRow + 1
            resultRow = currentRow + 1
            modDebug.LogWarning "后续任务，Position类型无效: " & TypeName(taskPosition) & "，默认使用next，行位置: " & resultRow, "modGantt.DetermineTaskRowAndCategory"

            ' 重置行类别设置状态
            isCurrentRowCategorySet = False

            ' 如果任务有非空类别
            If taskCategory <> "" Then
                currentCategory = taskCategory
                isCurrentRowCategorySet = True

                modDebug.LogInfo "后续任务，默认next位置，添加类别标题: " & currentCategory & " 在行 " & resultRow, "modGantt.DetermineTaskRowAndCategory"
                ws.Cells(resultRow, 2).value = currentCategory
                ws.Cells(resultRow, 2).Font.Bold = True
                ws.Cells(resultRow, 2).Font.size = 11
                ws.Cells(resultRow, 2).HorizontalAlignment = xlLeft
                ws.Cells(resultRow, 2).VerticalAlignment = xlCenter
            End If
        End If
    End If

    modDebug.LogInfo "任务 " & taskId & " (" & taskDesc & ") 的行位置确定为: " & resultRow & ", 当前类别: " & currentCategory, "modGantt.DetermineTaskRowAndCategory"
    modDebug.LogFunctionExit "modGantt.DetermineTaskRowAndCategory", "成功 - 返回行位置: " & resultRow
    DetermineTaskRowAndCategory = resultRow
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, "处理任务 " & taskId & " 的行位置和类别信息时出错 - " & Err.description, "modGantt.DetermineTaskRowAndCategory"
    modDebug.LogFunctionExit "modGantt.DetermineTaskRowAndCategory", "失败 - " & Err.description
    DetermineTaskRowAndCategory = -1 ' 出错时返回-1表示错误
End Function

' 注意：ProcessTaskCategory函数已被移除，其功能已整合到DetermineTaskRowAndCategory函数中

' 绘制任务条
Private Sub DrawTask(task As Dictionary, row As Long, timelineCoords As Dictionary)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    Dim taskId As String, taskDesc As String
    taskId = IIf(task.Exists("ID"), task("ID"), "未知ID")
    taskDesc = IIf(task.Exists("Description"), task("Description"), "未知描述")
    modDebug.LogFunctionEntry "modGantt.DrawTask", "任务ID: " & taskId & ", 描述: " & taskDesc & ", 行: " & row

    ' 直接获取配置参数
    Dim taskBarHeight As Long
    Dim taskBarBorderWidth As Single
    Dim progressBarColor As String
    Dim labelDistance As Long
    Dim rowPadding As Long
    Dim taskBarShapeStyle As Long

    ' 获取任务条高度
    taskBarHeight = CLng(Val(GetConfig("TaskBarHeight", 11)))

    ' 获取任务条边框宽度
    taskBarBorderWidth = CSng(Val(GetConfig("TaskBarBorderWidth", 0)))

    ' 获取进度条颜色
    progressBarColor = CStr(GetConfig("ProgressBarColor", "#66CC66"))

    ' 获取标签距离
    labelDistance = CLng(Val(GetConfig("LabelDistance", 5)))

    ' 获取行高预留空隙
    rowPadding = CLng(Val(GetConfig("RowPadding", 3)))

    ' 获取任务条图形样式
    taskBarShapeStyle = CLng(Val(GetConfig("TaskBarShapeStyle", 1)))

    modDebug.LogVerbose "配置参数 - 任务条高度: " & taskBarHeight & ", 边框宽度: " & taskBarBorderWidth & _
                       ", 进度条颜色: " & progressBarColor & ", 标签距离: " & labelDistance & _
                       ", 行高预留: " & rowPadding & ", 图形样式: " & taskBarShapeStyle, "modGantt.DrawTask"

    ' 记录任务日期信息
    Dim startDateStr As String, endDateStr As String
    startDateStr = Format(task("StartDate"), "yyyy-mm-dd")
    endDateStr = Format(task("EndDate"), "yyyy-mm-dd")
    modDebug.LogVerbose "任务日期范围 - 开始: " & startDateStr & ", 结束: " & endDateStr, "modGantt.DrawTask"

    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("GanttChart")
    modDebug.LogVerbose "获取GanttChart工作表引用成功", "modGantt.DrawTask"

    ' 记录坐标系信息
    modDebug.LogVerbose "使用传入的时间轴坐标系 - 原点X: " & timelineCoords("OriginX") & _
                       ", 宽度: " & timelineCoords("Width") & _
                       ", 起始日期: " & Format(timelineCoords("StartDate"), "yyyy-mm-dd") & _
                       ", 结束日期: " & Format(timelineCoords("EndDate"), "yyyy-mm-dd") & _
                       ", 总天数: " & timelineCoords("TotalDays"), "modGantt.DrawTask"

    ' 计算任务条的位置和大小
    Dim left As Double, top As Double, width As Double, height As Double

    ' 计算任务开始和结束的X坐标
    Dim startX As Double, endX As Double
    modDebug.LogVerbose "调用CalculateXCoordinate计算开始X坐标，参数: 任务开始日期=" & startDateStr, "modGantt.DrawTask"
    startX = CalculateXCoordinate(task("StartDate"), timelineCoords)
    modDebug.LogVerbose "调用CalculateXCoordinate计算结束X坐标，参数: 任务结束日期=" & endDateStr, "modGantt.DrawTask"
    endX = CalculateXCoordinate(task("EndDate"), timelineCoords)

    ' 计算任务条的左边缘、宽度和高度
    left = startX
    width = endX - startX
    height = taskBarHeight ' 使用配置的任务条高度

    ' 计算任务条的垂直居中位置
    Dim rowHeight As Double
    rowHeight = ws.Cells(row, 1).height

    ' 计算任务条垂直居中位置
    top = ws.Cells(row, 1).top + (rowHeight - height) / 2 ' 垂直居中

    modDebug.LogInfo "计算任务条位置和大小 - Left: " & left & ", Top: " & top & ", Width: " & width & ", Height: " & height, "modGantt.DrawTask"

    ' 创建任务条形状
    Dim taskShape As shape
    Dim shapeType As Long

    ' 根据配置选择形状类型
    Select Case taskBarShapeStyle
        Case 1 ' 圆角矩形
            shapeType = 5 ' msoShapeRoundedRectangle
        Case 2 ' 矩形
            shapeType = 1 ' msoShapeRectangle
        Case 3 ' 流程图处理
            shapeType = 109 ' msoShapeFlowchartProcess
        Case Else ' 默认使用圆角矩形
            shapeType = 5 ' msoShapeRoundedRectangle
    End Select

    ' 添加调试信息
    modDebug.LogInfo "开始创建任务条形状, 类型=" & shapeType & ", left=" & left & ", top=" & top & ", width=" & width & ", height=" & height, "modGantt.DrawTask"

    On Error Resume Next
    Set taskShape = ws.Shapes.AddShape(shapeType, left, top, width, height)

    If Err.Number <> 0 Then
        modDebug.LogError Err.Number, "创建任务条形状失败 - " & Err.description, "modGantt.DrawTask"
        modDebug.LogFunctionExit "modGantt.DrawTask", "失败 - 无法创建任务条形状"
        Exit Sub
    End If

    On Error GoTo ErrorHandler
    modDebug.LogVerbose "任务条形状创建成功，形状ID: " & taskShape.ID, "modGantt.DrawTask"

    ' 设置Placement属性为xlMove，避免形状在调整行高时被拉伸
    taskShape.Placement = xlMove
    modDebug.LogVerbose "设置任务条形状Placement属性为xlMove，避免在调整行高时被拉伸", "modGantt.DrawTask"

    ' 设置任务条格式
    Dim taskColor As String
    taskColor = task("Color")
    modDebug.LogVerbose "任务颜色: " & taskColor, "modGantt.DrawTask"

    ' 转换颜色代码（处理G/Y/R/S等预定义代码）
    taskColor = ConvertColorCode(taskColor, "A")
    modDebug.LogVerbose "转换后的任务颜色: " & taskColor, "modGantt.DrawTask"

    taskShape.Fill.ForeColor.RGB = GetRGBColor(taskColor)

    ' 设置任务条边框
    If taskBarBorderWidth = 0 Then
        taskShape.Line.Visible = msoFalse ' 无边框
    Else
        taskShape.Line.Visible = msoTrue
        taskShape.Line.Weight = taskBarBorderWidth
        taskShape.Line.ForeColor.RGB = GetRGBColor(taskColor)
    End If

    modDebug.LogVerbose "任务条格式设置完成", "modGantt.DrawTask"

    ' 如果有进度，绘制进度条
    Dim progressShape As shape
    Set progressShape = Nothing

    If task("Progress") > 0 Then
        Dim progressWidth As Double
        progressWidth = width * task("Progress")
        modDebug.LogVerbose "任务进度: " & task("Progress") * 100 & "%, 进度条宽度: " & progressWidth, "modGantt.DrawTask"

        On Error Resume Next
        ' 使用与任务条相同的形状类型
        Set progressShape = ws.Shapes.AddShape(shapeType, left, top, progressWidth, height)

        If Err.Number <> 0 Then
            modDebug.LogError Err.Number, "创建进度条形状失败 - " & Err.description, "modGantt.DrawTask"
        Else
            ' 设置Placement属性为xlMove，避免形状在调整行高时被拉伸
            progressShape.Placement = xlMove
            modDebug.LogVerbose "设置进度条形状Placement属性为xlMove，避免在调整行高时被拉伸", "modGantt.DrawTask"

            progressShape.Fill.ForeColor.RGB = GetRGBColor(progressBarColor)

            ' 设置进度条边框与任务条一致
            If taskBarBorderWidth = 0 Then
                progressShape.Line.Visible = msoFalse ' 无边框
            Else
                progressShape.Line.Visible = msoTrue
                progressShape.Line.Weight = taskBarBorderWidth
                progressShape.Line.ForeColor.RGB = GetRGBColor(taskColor)
            End If

            ' 将进度条置于最顶层
            progressShape.ZOrder msoBringToFront
            modDebug.LogVerbose "进度条创建成功，形状ID: " & progressShape.ID, "modGantt.DrawTask"
        End If

        On Error GoTo ErrorHandler
    End If

    ' 添加任务描述标签（使用坐标系）
    modDebug.LogInfo "开始添加任务描述标签", "modGantt.DrawTask"
    Dim labelShape As shape
    Set labelShape = AddTaskLabelWithCoordinates2(task, taskShape, startX + width / 2, top + height / 2, CDbl(height), labelDistance)

    If Not labelShape Is Nothing Then
        modDebug.LogVerbose "任务标签创建成功，形状ID: " & labelShape.ID, "modGantt.DrawTask"

        ' 组合任务条、进度条和标签
        Dim shapeGroup As shape
        On Error Resume Next

        ' 创建要组合的形状数组
        Dim shapesToGroup As New Collection
        shapesToGroup.Add taskShape.Name

        ' 如果有进度条，也加入组合
        If Not progressShape Is Nothing Then
            shapesToGroup.Add progressShape.Name
            modDebug.LogVerbose "进度条将被包含在组合中，形状ID: " & progressShape.ID, "modGantt.DrawTask"
        End If

        shapesToGroup.Add labelShape.Name

        ' 将集合转换为数组
        Dim shapesArray() As String
        ReDim shapesArray(1 To shapesToGroup.Count)

        Dim i As Long
        For i = 1 To shapesToGroup.Count
            shapesArray(i) = shapesToGroup(i)
        Next i

        ' 组合形状
        Set shapeGroup = ws.Shapes.range(shapesArray).Group

        If Err.Number <> 0 Then
            modDebug.LogWarning "无法组合形状: " & Err.Number & " - " & Err.description, "modGantt.DrawTask"
            Err.Clear

            ' 如果组合失败，仍然调整行高
            modDebug.LogInfo "开始调整行高以适应任务和标签", "modGantt.DrawTask"
            AdjustRowHeightWithPadding ws, row, taskShape, labelShape, rowPadding
        Else
            modDebug.LogVerbose "成功组合形状，组ID: " & shapeGroup.ID, "modGantt.DrawTask"

            ' 设置组的Placement属性为xlMove，避免在调整行高时被拉伸
            shapeGroup.Placement = xlMove
            modDebug.LogVerbose "设置形状组Placement属性为xlMove，避免在调整行高时被拉伸", "modGantt.DrawTask"

            ' 动态调整行高以适应组合形状
            modDebug.LogInfo "开始调整行高以适应组合形状", "modGantt.DrawTask"
            AdjustRowHeightWithPadding ws, row, taskShape, labelShape, rowPadding
        End If

        modDebug.LogInfo "行高调整完成", "modGantt.DrawTask"
        On Error GoTo ErrorHandler
    Else
        modDebug.LogWarning "任务标签创建失败或返回为Nothing", "modGantt.DrawTask"
    End If

    modDebug.LogInfo "任务描述标签添加完成", "modGantt.DrawTask"

    ' 记录函数退出
    modDebug.LogFunctionExit "modGantt.DrawTask", "成功"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modGantt.DrawTask"
    modDebug.LogFunctionExit "modGantt.DrawTask", "失败 - " & Err.description
    Err.Raise Err.Number, "modGantt.DrawTask", Err.description
End Sub

' 绘制里程碑
Private Sub DrawMilestone(task As Dictionary, row As Long, timelineCoords As Dictionary)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    Dim taskId As String, taskDesc As String
    taskId = IIf(task.Exists("ID"), task("ID"), "未知ID")
    taskDesc = IIf(task.Exists("Description"), task("Description"), "未知描述")
    modDebug.LogFunctionEntry "modGantt.DrawMilestone", "任务ID: " & taskId & ", 描述: " & taskDesc & ", 行: " & row

    ' 直接获取配置参数
    Dim milestoneSize As Long
    Dim milestoneBorderWidth As Single
    Dim labelDistance As Long
    Dim rowPadding As Long
    Dim milestoneShapeStyle As Long

    ' 获取里程碑大小
    milestoneSize = CLng(Val(GetConfig("TaskBarHeight", 11)))

    ' 获取里程碑边框宽度
    milestoneBorderWidth = CSng(Val(GetConfig("TaskBarBorderWidth", 0)))

    ' 获取标签距离
    labelDistance = CLng(Val(GetConfig("LabelDistance", 5)))

    ' 获取行高预留空隙
    rowPadding = CLng(Val(GetConfig("RowPadding", 3)))

    ' 获取里程碑图形样式
    milestoneShapeStyle = CLng(Val(GetConfig("MilestoneShapeStyle", 1)))

    modDebug.LogVerbose "配置参数 - 里程碑大小: " & milestoneSize & ", 边框宽度: " & milestoneBorderWidth & _
                       ", 标签距离: " & labelDistance & ", 行高预留: " & rowPadding & _
                       ", 图形样式: " & milestoneShapeStyle, "modGantt.DrawMilestone"

    ' 记录任务日期信息
    Dim startDateStr As String
    startDateStr = Format(task("StartDate"), "yyyy-mm-dd")
    modDebug.LogVerbose "里程碑日期: " & startDateStr, "modGantt.DrawMilestone"

    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("GanttChart")
    modDebug.LogVerbose "获取GanttChart工作表引用成功", "modGantt.DrawMilestone"

    ' 记录坐标系信息
    modDebug.LogVerbose "使用传入的时间轴坐标系 - 原点X: " & timelineCoords("OriginX") & _
                       ", 宽度: " & timelineCoords("Width") & _
                       ", 起始日期: " & Format(timelineCoords("StartDate"), "yyyy-mm-dd") & _
                       ", 结束日期: " & Format(timelineCoords("EndDate"), "yyyy-mm-dd") & _
                       ", 总天数: " & timelineCoords("TotalDays"), "modGantt.DrawMilestone"

    ' 计算里程碑在坐标系中的精确位置
    modDebug.LogInfo "开始计算里程碑在坐标系中的精确位置", "modGantt.DrawMilestone"
    Dim milestoneX As Double, milestoneY As Double

    modDebug.LogVerbose "调用CalculateXCoordinate计算X坐标，参数: 里程碑日期=" & startDateStr, "modGantt.DrawMilestone"
    milestoneX = CalculateXCoordinate(task("StartDate"), timelineCoords)

    ' 计算Y坐标 - 使用行的中心点
    Dim rowHeight As Double
    rowHeight = ws.Cells(row, 1).height

    ' 计算里程碑中心点Y坐标（行的中心点）
    milestoneY = ws.Cells(row, 1).top + rowHeight / 2
    modDebug.LogVerbose "计算Y坐标: 行 " & row & " 的中心点 = " & ws.Cells(row, 1).top & " + " & rowHeight & " / 2 = " & milestoneY, "modGantt.DrawMilestone"

    modDebug.LogInfo "里程碑坐标计算结果 - X: " & milestoneX & ", Y: " & milestoneY, "modGantt.DrawMilestone"

    ' 计算里程碑形状的左上角坐标
    Dim shapeLeft As Double, shapeTop As Double
    shapeLeft = milestoneX - milestoneSize / 2
    shapeTop = milestoneY - milestoneSize / 2
    modDebug.LogVerbose "里程碑形状左上角坐标 - Left: " & shapeLeft & ", Top: " & shapeTop, "modGantt.DrawMilestone"

    ' 创建里程碑形状（根据配置选择形状）
    Dim milestoneShape As shape
    Dim shapeType As Long

    ' 根据配置选择形状类型
    Select Case milestoneShapeStyle
        Case 1 ' 菱形
            shapeType = 4 ' msoShapeDiamond
        Case 2 ' 旗子
            shapeType = 55 ' msoShapeFlag
        Case 3 ' 折角
            shapeType = 65 ' msoShapeFoldedCorner
        Case 4 ' 等腰三角形
            shapeType = 5 ' msoShapeIsoscelesTriangle
        Case 5 ' 椭圆形
            shapeType = 9 ' msoShapeOval
        Case 6 ' 五角星
            shapeType = 12 ' msoShape5pointStar
        Case 7 ' 太阳
            shapeType = 23 ' msoShapeSun
        Case 8 ' 水滴
            shapeType = 160 ' msoShapeTear
        Case 9 ' 笑脸
            shapeType = 17 ' msoShapeSmileyFace
        Case 10 ' 月亮
            shapeType = 24 ' msoShapeMoon
        Case 11 ' 雪佛龙
            shapeType = 52 ' msoShapeChevron
        Case 12 ' 闪电
            shapeType = 22 ' msoShapeLightningBolt
        Case 13 ' 等边三角形
            shapeType = 7 ' msoShapeIsoscelesTriangle
        Case Else ' 默认使用菱形
            shapeType = 4 ' msoShapeDiamond
    End Select

    modDebug.LogInfo "开始创建基于坐标系的里程碑形状, 类型=" & shapeType & ", X=" & milestoneX & ", Y=" & milestoneY & ", size=" & milestoneSize, "modGantt.DrawMilestone"

    On Error Resume Next
    Set milestoneShape = ws.Shapes.AddShape(shapeType, shapeLeft, shapeTop, milestoneSize, milestoneSize)

    If Err.Number <> 0 Then
        modDebug.LogError Err.Number, "创建里程碑形状失败 - " & Err.description, "modGantt.DrawMilestone"
        modDebug.LogFunctionExit "modGantt.DrawMilestone", "失败 - 无法创建里程碑形状"
        Exit Sub
    End If

    ' 设置Placement属性为xlMove，避免形状在调整行高时被拉伸
    milestoneShape.Placement = xlMove
    modDebug.LogVerbose "设置里程碑形状Placement属性为xlMove，避免在调整行高时被拉伸", "modGantt.DrawMilestone"

    On Error GoTo ErrorHandler
    modDebug.LogVerbose "里程碑形状创建成功，形状ID: " & milestoneShape.ID, "modGantt.DrawMilestone"

    ' 设置里程碑格式
    Dim milestoneColor As String
    milestoneColor = task("Color")
    modDebug.LogVerbose "里程碑颜色: " & milestoneColor, "modGantt.DrawMilestone"

    ' 转换颜色代码（处理G/Y/R/S等预定义代码）
    milestoneColor = ConvertColorCode(milestoneColor, "M")
    modDebug.LogVerbose "转换后的里程碑颜色: " & milestoneColor, "modGantt.DrawMilestone"

    milestoneShape.Fill.ForeColor.RGB = GetRGBColor(milestoneColor)

    ' 设置里程碑边框
    If milestoneBorderWidth = 0 Then
        milestoneShape.Line.Visible = msoFalse ' 无边框
    Else
        milestoneShape.Line.Visible = msoTrue
        milestoneShape.Line.Weight = milestoneBorderWidth
        milestoneShape.Line.ForeColor.RGB = GetRGBColor(milestoneColor)
    End If

    modDebug.LogVerbose "里程碑格式设置完成", "modGantt.DrawMilestone"

    ' 添加里程碑描述标签（使用坐标系）
    modDebug.LogInfo "开始添加里程碑描述标签", "modGantt.DrawMilestone"
    Dim labelShape As shape
    Set labelShape = AddTaskLabelWithCoordinates2(task, milestoneShape, milestoneX, milestoneY, CDbl(milestoneSize), labelDistance)

    If Not labelShape Is Nothing Then
        modDebug.LogVerbose "里程碑标签创建成功，形状ID: " & labelShape.ID, "modGantt.DrawMilestone"

        ' 组合里程碑和标签
        Dim shapeGroup As shape
        On Error Resume Next

        ' 组合形状
        Set shapeGroup = ws.Shapes.range(Array(milestoneShape.Name, labelShape.Name)).Group

        If Err.Number <> 0 Then
            modDebug.LogWarning "无法组合形状: " & Err.Number & " - " & Err.description, "modGantt.DrawMilestone"
            Err.Clear

            ' 如果组合失败，仍然调整行高
            modDebug.LogInfo "开始调整行高以适应里程碑和标签", "modGantt.DrawMilestone"
            AdjustRowHeightWithPadding ws, row, milestoneShape, labelShape, rowPadding
        Else
            modDebug.LogVerbose "成功组合形状，组ID: " & shapeGroup.ID, "modGantt.DrawMilestone"

            ' 设置组的Placement属性为xlMove，避免在调整行高时被拉伸
            shapeGroup.Placement = xlMove
            modDebug.LogVerbose "设置形状组Placement属性为xlMove，避免在调整行高时被拉伸", "modGantt.DrawMilestone"

            ' 动态调整行高以适应组合形状
            modDebug.LogInfo "开始调整行高以适应组合形状", "modGantt.DrawMilestone"
            AdjustRowHeightWithPadding ws, row, milestoneShape, labelShape, rowPadding
        End If

        modDebug.LogInfo "行高调整完成", "modGantt.DrawMilestone"
        On Error GoTo ErrorHandler
    Else
        modDebug.LogWarning "里程碑标签创建失败或返回为Nothing", "modGantt.DrawMilestone"
    End If

    ' 记录函数退出
    modDebug.LogFunctionExit "modGantt.DrawMilestone", "成功"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modGantt.DrawMilestone"
    modDebug.LogFunctionExit "modGantt.DrawMilestone", "失败 - " & Err.description
    Err.Raise Err.Number, "modGantt.DrawMilestone", Err.description
End Sub

' 注意：AddTaskLabel函数已被移除，使用AddTaskLabelWithCoordinates代替

' 建立时间轴坐标系
Private Function EstablishTimelineCoordinateSystem(ws As Worksheet, projectInfo As Dictionary) As Dictionary
    On Error GoTo ErrorHandler

    modDebug.LogFunctionEntry "modGantt.EstablishTimelineCoordinateSystem"

    Dim coords As New Dictionary

    ' 获取时间轴的起始和结束日期
    Dim timelineStartDate As Date, timelineEndDate As Date

    ' 计算时间轴起始日期（项目开始日期所在月份的第一天）
    timelineStartDate = DateSerial(Year(projectInfo("StartDate")), Month(projectInfo("StartDate")), 1)
    modDebug.LogVerbose "计算时间轴起始日期: " & Format(timelineStartDate, "yyyy-mm-dd") & _
                       " (项目开始日期: " & Format(projectInfo("StartDate"), "yyyy-mm-dd") & " 所在月份的第一天)", _
                       "modGantt.EstablishTimelineCoordinateSystem"

    ' 计算时间轴结束日期（项目结束日期所在月份的最后一天）
    timelineEndDate = DateSerial(Year(projectInfo("EndDate")), Month(projectInfo("EndDate")) + 1, 0)
    modDebug.LogVerbose "计算时间轴结束日期: " & Format(timelineEndDate, "yyyy-mm-dd") & _
                       " (项目结束日期: " & Format(projectInfo("EndDate"), "yyyy-mm-dd") & " 所在月份的最后一天)", _
                       "modGantt.EstablishTimelineCoordinateSystem"

    ' 获取时间轴的起始和结束列
    Dim startCol As Long, endCol As Long
    startCol = 3 ' 从C列开始
    modDebug.LogVerbose "时间轴起始列: " & startCol & " (C列)", "modGantt.EstablishTimelineCoordinateSystem"

    endCol = GetLastColumnWithData(ws, 5)
    modDebug.LogVerbose "时间轴结束列: " & endCol & " (通过GetLastColumnWithData获取)", "modGantt.EstablishTimelineCoordinateSystem"

    ' 计算坐标系的原点和宽度
    Dim originX As Double, width As Double

    ' 使用C5单元格（第一个周起始的地方）的左下角作为坐标原点
    originX = ws.Cells(5, startCol).left

    ' 计算从原点到结束列的右边缘的距离作为坐标系宽度
    width = ws.Cells(5, endCol).left + ws.Cells(5, endCol).width - originX

    modDebug.LogVerbose "坐标系原点X: " & originX & " (单元格(" & 5 & "," & startCol & ")的Left属性)", "modGantt.EstablishTimelineCoordinateSystem"
    modDebug.LogVerbose "坐标系宽度: " & width & " (从原点到单元格(" & 5 & "," & endCol & ")右边缘的距离)", "modGantt.EstablishTimelineCoordinateSystem"

    ' 计算总天数（注意：实际天数应该是DateDiff结果+1，因为包含起始和结束日）
    Dim totalDays As Long
    totalDays = DateDiff("d", timelineStartDate, timelineEndDate)
    modDebug.LogVerbose "时间轴总天数(未修正): " & totalDays & " (从 " & Format(timelineStartDate, "yyyy-mm-dd") & _
                       " 到 " & Format(timelineEndDate, "yyyy-mm-dd") & ")", "modGantt.EstablishTimelineCoordinateSystem"

    ' 注意：实际使用时，坐标计算会加1进行修正，这里保持原始计算结果以保持与现有代码兼容

    ' 存储坐标系信息
    coords.Add "StartDate", timelineStartDate
    coords.Add "EndDate", timelineEndDate
    coords.Add "OriginX", originX
    coords.Add "Width", width
    coords.Add "TotalDays", totalDays

    modDebug.LogInfo "时间轴坐标系建立完成 - 原点X: " & originX & ", 宽度: " & width & ", 日期范围: " & _
                    Format(timelineStartDate, "yyyy-mm-dd") & " 到 " & Format(timelineEndDate, "yyyy-mm-dd") & _
                    ", 总天数: " & totalDays, "modGantt.EstablishTimelineCoordinateSystem"

    modDebug.LogFunctionExit "modGantt.EstablishTimelineCoordinateSystem", "成功"
    Set EstablishTimelineCoordinateSystem = coords
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modGantt.EstablishTimelineCoordinateSystem"
    modDebug.LogFunctionExit "modGantt.EstablishTimelineCoordinateSystem", "失败 - " & Err.description
    Set EstablishTimelineCoordinateSystem = Nothing
End Function

' 计算日期在坐标系中的X坐标
Private Function CalculateXCoordinate(targetDate As Date, coords As Dictionary) As Double
    On Error GoTo ErrorHandler

    modDebug.LogFunctionEntry "modGantt.CalculateXCoordinate", "目标日期: " & Format(targetDate, "yyyy-mm-dd")

    ' 计算日期与起始日期的天数差
    ' 注意：坐标轴原点实际上是上个月的最后一天，所以需要加1天
    Dim daysDiff As Long
    daysDiff = DateDiff("d", coords("StartDate"), targetDate) + 1
    modDebug.LogVerbose "目标日期与起始日期的天数差(修正后): " & daysDiff & " 天", "modGantt.CalculateXCoordinate"

    ' 注意：不再进行日期范围检查，因为数据验证已在前期实现

    ' 计算总天数(修正后)
    Dim totalDaysAdjusted As Long
    totalDaysAdjusted = coords("TotalDays") + 1
    modDebug.LogVerbose "修正后的总天数: " & totalDaysAdjusted & " 天", "modGantt.CalculateXCoordinate"

    ' 计算X坐标
    Dim xCoord As Double
    xCoord = coords("OriginX") + (coords("Width") * daysDiff / totalDaysAdjusted)

    modDebug.LogVerbose "计算X坐标(修正后): " & coords("OriginX") & " + (" & coords("Width") & " * " & _
                       daysDiff & " / " & totalDaysAdjusted & ") = " & xCoord, "modGantt.CalculateXCoordinate"

    modDebug.LogInfo "日期 " & Format(targetDate, "yyyy-mm-dd") & " 的X坐标计算结果: " & xCoord, "modGantt.CalculateXCoordinate"

    modDebug.LogFunctionExit "modGantt.CalculateXCoordinate", "成功"
    CalculateXCoordinate = xCoord
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modGantt.CalculateXCoordinate"
    modDebug.LogFunctionExit "modGantt.CalculateXCoordinate", "失败 - " & Err.description

    ' 返回一个默认值
    CalculateXCoordinate = coords("OriginX")
End Function



' 使用坐标系添加任务标签（改进版，支持可配置的标签距离和智能对齐）
Private Function AddTaskLabelWithCoordinates2(task As Dictionary, shape As shape, centerX As Double, centerY As Double, shapeSize As Double, labelDistance As Long) As shape
    On Error GoTo ErrorHandler

    ' 记录函数进入
    Dim taskId As String, taskDesc As String
    taskId = IIf(task.Exists("ID"), task("ID"), "未知ID")
    taskDesc = IIf(task.Exists("Description"), task("Description"), "未知描述")
    modDebug.LogFunctionEntry "modGantt.AddTaskLabelWithCoordinates2", "任务ID: " & taskId & ", 中心坐标: (" & centerX & "," & centerY & "), 形状大小: " & shapeSize & ", 标签距离: " & labelDistance

    ' 判断是否为里程碑
    Dim isMilestone As Boolean
    ' 通过任务字典中的Type属性判断，而不是通过形状类型
    isMilestone = (task.Exists("Type") And UCase(Trim(CStr(task("Type")))) = "M")
    modDebug.LogVerbose "任务类型: " & IIf(isMilestone, "里程碑(M)", "任务条(A)") & ", 形状AutoShapeType: " & shape.AutoShapeType, "modGantt.AddTaskLabelWithCoordinates2"

    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("GanttChart")
    modDebug.LogVerbose "获取GanttChart工作表引用成功", "modGantt.AddTaskLabelWithCoordinates2"

    ' 获取任务描述
    Dim description As String
    If task.Exists("Description") Then
        description = task("Description")
    Else
        description = ""
    End If

    If description = "" Then
        modDebug.LogVerbose "任务描述为空，不创建标签", "modGantt.AddTaskLabelWithCoordinates2"
        Set AddTaskLabelWithCoordinates2 = Nothing
        Exit Function
    End If

    modDebug.LogVerbose "任务描述: " & description, "modGantt.AddTaskLabelWithCoordinates2"

    ' 检查是否需要在标签中显示日期
    Dim labelText As String
    labelText = description
    modDebug.LogInfo "原始标签文本: """ & labelText & """", "modGantt.AddTaskLabelWithCoordinates2"

    ' 检查是否有ShowDateInLabel属性，且值为"Y"
    If task.Exists("ShowDateInLabel") And UCase(Trim(CStr(task("ShowDateInLabel")))) = "Y" Then
        Dim dateToShow As Date
        modDebug.LogInfo "ShowDateInLabel=Y，将添加日期到标签", "modGantt.AddTaskLabelWithCoordinates2"

        ' 对于里程碑显示开始日期，对于任务条显示结束日期
        If isMilestone And task.Exists("StartDate") Then
            dateToShow = task("StartDate")
            labelText = labelText & " " & Format(dateToShow, "mm/dd")
            modDebug.LogInfo "为里程碑添加开始日期: " & Format(dateToShow, "mm/dd") & "，最终标签文本: """ & labelText & """", "modGantt.AddTaskLabelWithCoordinates2"
        ElseIf Not isMilestone And task.Exists("EndDate") Then
            dateToShow = task("EndDate")
            labelText = labelText & " " & Format(dateToShow, "mm/dd")
            modDebug.LogInfo "为任务条添加结束日期: " & Format(dateToShow, "mm/dd") & "，最终标签文本: """ & labelText & """", "modGantt.AddTaskLabelWithCoordinates2"
        End If
    Else
        modDebug.LogInfo "不需要添加日期到标签", "modGantt.AddTaskLabelWithCoordinates2"
    End If

    ' 创建临时文本框以计算文本尺寸
    Dim tempTextBox As shape
    On Error Resume Next
    Set tempTextBox = ws.Shapes.AddTextbox(msoTextOrientationHorizontal, 0, 0, 100, 20)

    If Err.Number <> 0 Then
        modDebug.LogError Err.Number, "创建临时文本框失败 - " & Err.description, "modGantt.AddTaskLabelWithCoordinates2"
        modDebug.LogFunctionExit "modGantt.AddTaskLabelWithCoordinates2", "失败 - 无法创建临时文本框"
        Set AddTaskLabelWithCoordinates2 = Nothing
        Exit Function
    End If

    On Error GoTo ErrorHandler

    ' 获取标签字体和大小配置
    Dim labelFont As String, labelFontSize As Single

    ' 根据任务类型选择不同的配置项
    If isMilestone Then
        labelFont = CStr(GetConfig("MilestoneLabelFont", "Arial"))
        labelFontSize = CSng(Val(GetConfig("MilestoneLabelFontSize", 8)))
        modDebug.LogVerbose "使用里程碑标签字体配置: " & labelFont & ", 大小: " & labelFontSize, "modGantt.AddTaskLabelWithCoordinates2"
    Else
        labelFont = CStr(GetConfig("TaskLabelFont", "Arial"))
        labelFontSize = CSng(Val(GetConfig("TaskLabelFontSize", 8)))
        modDebug.LogVerbose "使用任务标签字体配置: " & labelFont & ", 大小: " & labelFontSize, "modGantt.AddTaskLabelWithCoordinates2"
    End If

    ' 设置临时文本框的文本和格式
    tempTextBox.TextFrame.Characters.text = labelText
    tempTextBox.TextFrame.Characters.Font.size = labelFontSize
    tempTextBox.TextFrame.Characters.Font.Name = labelFont
    tempTextBox.TextFrame.MarginLeft = 1
    tempTextBox.TextFrame.MarginRight = 1
    tempTextBox.TextFrame.MarginTop = 1
    tempTextBox.TextFrame.MarginBottom = 1
    tempTextBox.TextFrame.AutoSize = True

    ' 获取文本的实际宽度和高度
    Dim textWidth As Double, textHeight As Double
    textWidth = tempTextBox.width
    textHeight = tempTextBox.height

    ' 删除临时文本框
    tempTextBox.Delete
    modDebug.LogInfo "计算标签文本 """ & labelText & """ 的尺寸 - 宽度: " & textWidth & ", 高度: " & textHeight, "modGantt.AddTaskLabelWithCoordinates2"

    ' 计算任务条的宽度和高度
    Dim taskWidth As Double, taskHeight As Double
    taskWidth = shape.width
    taskHeight = shape.height
    modDebug.LogVerbose "任务条尺寸 - 宽度: " & taskWidth & ", 高度: " & taskHeight, "modGantt.AddTaskLabelWithCoordinates2"

    ' 确定标签位置和对齐方式
    Dim labelX As Double, labelY As Double
    Dim textAlign As Long

    ' 用于inside位置的对比色文本
    Dim useContrastColor As Boolean
    Dim contrastTextColor As Long
    useContrastColor = False ' 默认不使用对比色
    contrastTextColor = 0 ' 默认黑色

    ' 获取任务条或里程碑的左边界
    Dim taskLeft As Double
    taskLeft = centerX - taskWidth / 2

    ' 检查是否有指定的Text Position
    Dim textPosition As String
    textPosition = ""

    ' 尝试多种可能的列名
    If task.Exists("Text Position") Then
        textPosition = CStr(task("Text Position"))
        modDebug.LogVerbose "任务指定了Text Position: " & textPosition, "modGantt.AddTaskLabelWithCoordinates2"
    ElseIf task.Exists("TextPosition") Then
        textPosition = CStr(task("TextPosition"))
        modDebug.LogVerbose "任务指定了TextPosition: " & textPosition, "modGantt.AddTaskLabelWithCoordinates2"
    ElseIf task.Exists("Text_Position") Then
        textPosition = CStr(task("Text_Position"))
        modDebug.LogVerbose "任务指定了Text_Position: " & textPosition, "modGantt.AddTaskLabelWithCoordinates2"
    End If

    ' 转换为小写并去除空格
    textPosition = LCase(Trim(textPosition))

    ' 输出调试信息
    modDebug.LogInfo "任务ID: " & IIf(task.Exists("ID"), task("ID"), "未知") & ", 描述: " & _
                    IIf(task.Exists("Description"), task("Description"), "未知") & ", Text Position: " & _
                    IIf(textPosition = "", "未指定", textPosition), "modGantt.AddTaskLabelWithCoordinates2"

    ' 打印任务字典中的所有键，帮助调试
    Dim keysList As String, key As Variant
    keysList = "任务字典中的所有键: "
    For Each key In task.Keys
        keysList = keysList & key & ", "
    Next key
    modDebug.LogInfo keysList, "modGantt.AddTaskLabelWithCoordinates2"

    ' 根据形状类型和TextPosition确定标签位置
    If isMilestone Then
        ' 里程碑（菱形）标签位置处理
        ' 里程碑不支持inside位置，如果未指定或无效，默认使用right
        If textPosition = "" Or (textPosition <> "left" And textPosition <> "right" And textPosition <> "top" And textPosition <> "bottom") Then
            textPosition = "right"
            modDebug.LogVerbose "里程碑未指定有效的Text Position，使用默认值: right", "modGantt.AddTaskLabelWithCoordinates2"
        End If

        Select Case textPosition
            Case "right"
                ' 放在右侧，左对齐
                labelX = centerX + shape.width / 2 + labelDistance
                labelY = centerY - textHeight / 2  ' 水平中心与里程碑中心水平对齐
                textAlign = xlLeft
                modDebug.LogInfo "里程碑标签放置在右侧，左对齐，水平中心对齐 - 标签X: " & labelX & ", 标签Y: " & labelY & ", 文本宽度: " & textWidth, "modGantt.AddTaskLabelWithCoordinates2"

            Case "left"
                ' 放在左侧，右对齐
                labelX = centerX - shape.width / 2 - labelDistance - textWidth
                labelY = centerY - textHeight / 2  ' 水平中心与里程碑中心水平对齐
                textAlign = xlRight
                modDebug.LogInfo "里程碑标签放置在左侧，右对齐，水平中心对齐 - 标签X: " & labelX & ", 标签Y: " & labelY & ", 文本宽度: " & textWidth, "modGantt.AddTaskLabelWithCoordinates2"

            Case "top"
                ' 放在上方，居中对齐
                ' 确保标签文本框中心与里程碑中心X坐标一致
                labelX = centerX - textWidth / 2  ' 水平居中于里程碑
                labelY = centerY - shape.height / 2 - labelDistance - textHeight
                textAlign = xlCenter  ' 设置为居中对齐
                modDebug.LogInfo "里程碑标签放置在上方，居中对齐 - 标签X: " & labelX & ", 标签Y: " & labelY & ", 文本宽度: " & textWidth & ", 里程碑中心X: " & centerX, "modGantt.AddTaskLabelWithCoordinates2"

                ' 确保标签不会太远离里程碑
                If (centerY - shape.height / 2) - (labelY + textHeight) > 10 Then
                    labelY = centerY - shape.height / 2 - textHeight - 5
                End If

            Case "bottom"
                ' 放在下方，居中对齐
                ' 确保标签文本框中心与里程碑中心X坐标一致
                labelX = centerX - textWidth / 2  ' 水平居中于里程碑
                labelY = centerY + shape.height / 2 + labelDistance
                textAlign = xlCenter  ' 设置为居中对齐
                modDebug.LogInfo "里程碑标签放置在下方，居中对齐 - 标签X: " & labelX & ", 标签Y: " & labelY & ", 文本宽度: " & textWidth & ", 里程碑中心X: " & centerX, "modGantt.AddTaskLabelWithCoordinates2"

                ' 确保标签不会太远离里程碑
                If labelY - (centerY + shape.height / 2) > 10 Then
                    labelY = centerY + shape.height / 2 + 5
                End If
        End Select
    Else
        ' 任务条（矩形）标签位置处理
        ' 检查是否可以放在内部（仅适用于任务条）
        Dim canFitInside As Boolean
        canFitInside = (textWidth <= taskWidth * 0.9) And textPosition = "inside"

        ' 如果未指定或无效，默认使用right
        If textPosition = "" Or (textPosition <> "left" And textPosition <> "right" And textPosition <> "top" And textPosition <> "bottom" And textPosition <> "inside") Then
            textPosition = "right"
            modDebug.LogVerbose "任务条未指定有效的Text Position，使用默认值: right", "modGantt.AddTaskLabelWithCoordinates2"
        End If

        ' 如果指定为inside但放不下，改为right
        If textPosition = "inside" And Not canFitInside Then
            textPosition = "right"
            modDebug.LogVerbose "任务条标签放不下inside位置，改为right", "modGantt.AddTaskLabelWithCoordinates2"
        End If

        Select Case textPosition
            Case "inside"
                ' 放在任务条内部，居中对齐
                labelX = centerX - textWidth / 2  ' 水平居中对齐
                labelY = centerY - textHeight / 2
                textAlign = xlCenter

                ' 获取任务条颜色，并设置对比色文本颜色
                Dim taskFillColor As Long

                ' 获取任务条的填充颜色
                taskFillColor = shape.Fill.ForeColor.RGB

                ' 获取与背景色对比的文本颜色
                contrastTextColor = GetContrastTextColor(taskFillColor)

                ' 记录颜色信息
                modDebug.LogInfo "任务条内部标签 - 任务条颜色: " & taskFillColor & ", 对比文本颜色: " & contrastTextColor, "modGantt.AddTaskLabelWithCoordinates2"

                ' 保存对比色，稍后应用到文本框
                useContrastColor = True

                modDebug.LogInfo "任务条标签放置在内部，居中对齐 - 标签X: " & labelX & ", 标签Y: " & labelY & ", 文本宽度: " & textWidth & ", 任务条中心X: " & centerX, "modGantt.AddTaskLabelWithCoordinates2"

            Case "right"
                ' 放在右侧，左对齐
                labelX = centerX + taskWidth / 2 + labelDistance
                labelY = centerY - textHeight / 2  ' 水平中心与任务条中心水平对齐
                textAlign = xlLeft
                modDebug.LogVerbose "任务条标签放置在右侧，左对齐，水平中心对齐", "modGantt.AddTaskLabelWithCoordinates2"

            Case "left"
                ' 放在左侧，右对齐
                labelX = centerX - taskWidth / 2 - labelDistance - textWidth
                labelY = centerY - textHeight / 2  ' 水平中心与任务条中心水平对齐
                textAlign = xlRight
                modDebug.LogVerbose "任务条标签放置在左侧，右对齐，水平中心对齐", "modGantt.AddTaskLabelWithCoordinates2"

            Case "top"
                ' 放在上方，左对齐
                labelX = taskLeft  ' 左对齐于任务条左边缘
                labelY = centerY - taskHeight / 2 - labelDistance - textHeight
                textAlign = xlLeft
                modDebug.LogVerbose "任务条标签放置在上方，左对齐", "modGantt.AddTaskLabelWithCoordinates2"

                ' 确保标签不会太远离任务条
                If (centerY - taskHeight / 2) - (labelY + textHeight) > 10 Then
                    labelY = centerY - taskHeight / 2 - textHeight - 5
                End If

            Case "bottom"
                ' 放在下方，左对齐
                labelX = taskLeft  ' 左对齐于任务条左边缘
                labelY = centerY + taskHeight / 2 + labelDistance
                textAlign = xlLeft
                modDebug.LogVerbose "任务条标签放置在下方，左对齐", "modGantt.AddTaskLabelWithCoordinates2"

                ' 确保标签不会太远离任务条
                If labelY - (centerY + taskHeight / 2) > 10 Then
                    labelY = centerY + taskHeight / 2 + 5
                End If
        End Select
    End If

    ' 创建标签文本框
    Dim labelShape As shape
    modDebug.LogInfo "开始创建标签文本框, X=" & labelX & ", Y=" & labelY & ", width=" & textWidth & ", height=" & textHeight, "modGantt.AddTaskLabelWithCoordinates2"

    On Error Resume Next
    Set labelShape = ws.Shapes.AddTextbox(msoTextOrientationHorizontal, labelX, labelY, textWidth, textHeight)

    If Err.Number <> 0 Then
        modDebug.LogError Err.Number, "创建标签文本框失败 - " & Err.description, "modGantt.AddTaskLabelWithCoordinates2"
        modDebug.LogFunctionExit "modGantt.AddTaskLabelWithCoordinates2", "失败 - 无法创建标签文本框"
        Set AddTaskLabelWithCoordinates2 = Nothing
        Exit Function
    End If

    On Error GoTo ErrorHandler
    modDebug.LogVerbose "标签文本框创建成功，形状ID: " & labelShape.ID, "modGantt.AddTaskLabelWithCoordinates2"

    ' 设置Placement属性为xlMove，避免形状在调整行高时被拉伸
    labelShape.Placement = xlMove
    modDebug.LogVerbose "设置标签形状Placement属性为xlMove，避免在调整行高时被拉伸", "modGantt.AddTaskLabelWithCoordinates2"

    ' 设置标签文本和格式
    labelShape.TextFrame.Characters.text = labelText
    modDebug.LogInfo "设置最终标签文本: """ & labelText & """", "modGantt.AddTaskLabelWithCoordinates2"

    ' 使用之前获取的字体配置
    labelShape.TextFrame.Characters.Font.size = labelFontSize
    labelShape.TextFrame.Characters.Font.Name = labelFont

    ' 如果是inside位置，应用对比色文本
    If useContrastColor Then
        labelShape.TextFrame.Characters.Font.Color = contrastTextColor
        modDebug.LogInfo "应用标签字体: " & labelFont & ", 大小: " & labelFontSize & ", 对比色: " & contrastTextColor, "modGantt.AddTaskLabelWithCoordinates2"
    Else
        modDebug.LogInfo "应用标签字体: " & labelFont & ", 大小: " & labelFontSize, "modGantt.AddTaskLabelWithCoordinates2"
    End If

    labelShape.TextFrame.HorizontalAlignment = textAlign
    labelShape.TextFrame.VerticalAlignment = xlCenter

    ' 设置文本框边距，使标签框尺寸更小
    labelShape.TextFrame.MarginLeft = 1
    labelShape.TextFrame.MarginRight = 1
    labelShape.TextFrame.MarginTop = 1
    labelShape.TextFrame.MarginBottom = 1
    labelShape.TextFrame.AutoSize = True

    ' 记录最终标签尺寸
    modDebug.LogInfo "最终标签尺寸 - 宽度: " & labelShape.width & ", 高度: " & labelShape.height, "modGantt.AddTaskLabelWithCoordinates2"

    ' 设置标签透明
    labelShape.Fill.Visible = msoFalse
    labelShape.Line.Visible = msoFalse

    modDebug.LogVerbose "标签格式设置完成", "modGantt.AddTaskLabelWithCoordinates2"
    modDebug.LogInfo "最终标签位置 - X: " & labelShape.left & ", Y: " & labelShape.top & ", 宽度: " & labelShape.width & ", 高度: " & labelShape.height, "modGantt.AddTaskLabelWithCoordinates2"

    ' 注意：不在这里组合形状和标签
    ' 组合操作将在DrawTask和DrawMilestone函数中进行
    ' 这样可以确保如果有进度条，也能被包含在组合中

    ' 记录信息，表明标签创建成功，但组合将在调用函数中处理
    modDebug.LogVerbose "标签创建成功，组合操作将在调用函数中处理", "modGantt.AddTaskLabelWithCoordinates2"

    On Error GoTo ErrorHandler

    ' 记录函数退出
    modDebug.LogFunctionExit "modGantt.AddTaskLabelWithCoordinates2", "成功"

    ' 返回创建的标签
    Set AddTaskLabelWithCoordinates2 = labelShape
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modGantt.AddTaskLabelWithCoordinates2"
    modDebug.LogFunctionExit "modGantt.AddTaskLabelWithCoordinates2", "失败 - " & Err.description
    Set AddTaskLabelWithCoordinates2 = Nothing
End Function

' 动态调整行高以适应里程碑和标签
Private Sub AdjustRowHeight(ws As Worksheet, row As Long, shapeObj As shape, labelObj As shape)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modGantt.AdjustRowHeight", "行: " & row & ", 形状ID: " & shapeObj.ID & ", 标签ID: " & labelObj.ID

    ' 获取当前行高
    Dim currentHeight As Double
    currentHeight = ws.Rows(row).height
    modDebug.LogVerbose "当前行高: " & currentHeight, "modGantt.AdjustRowHeight"

    ' 计算里程碑和标签占用的总高度
    Dim requiredHeight As Double
    Dim shapeBottom As Double, labelBottom As Double
    Dim shapeTop As Double, labelTop As Double

    ' 获取形状的上下边界
    shapeTop = shapeObj.top
    shapeBottom = shapeObj.top + shapeObj.height
    modDebug.LogVerbose "形状边界 - 上: " & shapeTop & ", 下: " & shapeBottom & ", 高度: " & shapeObj.height, "modGantt.AdjustRowHeight"

    ' 获取标签的上下边界
    labelTop = labelObj.top
    labelBottom = labelObj.top + labelObj.height
    modDebug.LogVerbose "标签边界 - 上: " & labelTop & ", 下: " & labelBottom & ", 高度: " & labelObj.height, "modGantt.AdjustRowHeight"

    ' 找出最高和最低点
    Dim topMost As Double, bottomMost As Double
    topMost = Application.WorksheetFunction.Min(shapeTop, labelTop)
    bottomMost = Application.WorksheetFunction.Max(shapeBottom, labelBottom)
    modDebug.LogVerbose "计算边界 - 最高点: " & topMost & ", 最低点: " & bottomMost, "modGantt.AdjustRowHeight"

    ' 计算所需高度（添加一些边距）
    requiredHeight = bottomMost - topMost + 4
    modDebug.LogVerbose "计算所需高度: " & requiredHeight & " (最低点 - 最高点 + 4)", "modGantt.AdjustRowHeight"

    ' 如果所需高度大于当前行高，则调整行高
    If requiredHeight > currentHeight Then
        modDebug.LogInfo "所需高度 " & requiredHeight & " 大于当前行高 " & currentHeight & "，需要调整", "modGantt.AdjustRowHeight"

        ' 尝试调整行高，使用错误处理以防失败
        On Error Resume Next

        ' 检查工作表是否被保护
        Dim isProtected As Boolean
        isProtected = ws.ProtectContents

        ' 如果工作表被保护，临时取消保护
        Dim tempPassword As String
        tempPassword = ""  ' 如果有密码，需要提供

        If isProtected Then
            modDebug.LogVerbose "工作表被保护，临时取消保护", "modGantt.AdjustRowHeight"
            ws.Unprotect Password:=tempPassword
        End If

        ' 尝试设置行高
        ws.Rows(row).rowHeight = requiredHeight

        ' 检查是否成功设置
        If Err.Number <> 0 Then
            modDebug.LogWarning "无法设置行高: " & Err.Number & " - " & Err.description, "modGantt.AdjustRowHeight"
            Err.Clear

            ' 尝试使用另一种方法
            ws.Cells(row, 1).EntireRow.rowHeight = requiredHeight

            If Err.Number <> 0 Then
                modDebug.LogWarning "第二次尝试设置行高也失败: " & Err.Number & " - " & Err.description, "modGantt.AdjustRowHeight"
                Err.Clear
            Else
                modDebug.LogInfo "使用替代方法成功调整第 " & row & " 行高度为 " & requiredHeight, "modGantt.AdjustRowHeight"
            End If
        Else
            modDebug.LogInfo "已调整第 " & row & " 行高度为 " & requiredHeight, "modGantt.AdjustRowHeight"
        End If

        ' 如果之前工作表被保护，恢复保护
        If isProtected Then
            modDebug.LogVerbose "恢复工作表保护", "modGantt.AdjustRowHeight"
            ws.Protect Password:=tempPassword
        End If

        On Error GoTo ErrorHandler
    Else
        modDebug.LogVerbose "所需高度 " & requiredHeight & " 不大于当前行高 " & currentHeight & "，无需调整", "modGantt.AdjustRowHeight"
    End If

    ' 记录函数退出
    modDebug.LogFunctionExit "modGantt.AdjustRowHeight", "成功"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modGantt.AdjustRowHeight"
    modDebug.LogFunctionExit "modGantt.AdjustRowHeight", "失败 - " & Err.description
    ' 不抛出错误，因为这只是一个辅助功能
End Sub

' 动态调整行高以适应任务和标签（带预留空隙）
Private Sub AdjustRowHeightWithPadding(ws As Worksheet, row As Long, shapeObj As shape, labelObj As shape, padding As Long)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modGantt.AdjustRowHeightWithPadding", "行: " & row & ", 形状ID: " & shapeObj.ID & ", 标签ID: " & labelObj.ID & ", 预留空隙: " & padding

    ' 获取当前行高![1746204307965](image/modGantt/1746204307965.png)
    Dim currentHeight As Double
    currentHeight = ws.Rows(row).height
    modDebug.LogVerbose "当前行高: " & currentHeight, "modGantt.AdjustRowHeightWithPadding"

    ' 计算任务和标签占用的总高度
    Dim requiredHeight As Double
    Dim topMost As Double, bottomMost As Double

    ' 检查形状是否已经组合
    Dim parentGroup As shape
    On Error Resume Next
    Set parentGroup = shapeObj.parentGroup
    On Error GoTo ErrorHandler

    If Not parentGroup Is Nothing Then
        ' 形状已经在组中，直接使用组的尺寸
        modDebug.LogVerbose "形状已在组中，使用组的尺寸计算", "modGantt.AdjustRowHeightWithPadding"

        ' 获取组的上下边界
        topMost = parentGroup.top
        bottomMost = parentGroup.top + parentGroup.height
        modDebug.LogVerbose "组边界 - 上: " & topMost & ", 下: " & bottomMost & ", 高度: " & parentGroup.height, "modGantt.AdjustRowHeightWithPadding"
    Else
        ' 形状未组合，分别计算形状和标签的边界
        Dim shapeBottom As Double, labelBottom As Double
        Dim shapeTop As Double, labelTop As Double

        ' 获取形状的上下边界
        shapeTop = shapeObj.top
        shapeBottom = shapeObj.top + shapeObj.height
        modDebug.LogVerbose "形状边界 - 上: " & shapeTop & ", 下: " & shapeBottom & ", 高度: " & shapeObj.height, "modGantt.AdjustRowHeightWithPadding"

        ' 获取标签的上下边界
        labelTop = labelObj.top
        labelBottom = labelObj.top + labelObj.height
        modDebug.LogVerbose "标签边界 - 上: " & labelTop & ", 下: " & labelBottom & ", 高度: " & labelObj.height, "modGantt.AdjustRowHeightWithPadding"

        ' 找出最高和最低点
        topMost = Application.WorksheetFunction.Min(shapeTop, labelTop)
        bottomMost = Application.WorksheetFunction.Max(shapeBottom, labelBottom)
        modDebug.LogVerbose "计算边界 - 最高点: " & topMost & ", 最低点: " & bottomMost, "modGantt.AdjustRowHeightWithPadding"
    End If

    ' 计算所需高度（添加上下预留空隙）
    requiredHeight = bottomMost - topMost + (padding * 2)
    modDebug.LogVerbose "计算所需高度: " & requiredHeight & " (最低点 - 最高点 + " & padding * 2 & ")", "modGantt.AdjustRowHeightWithPadding"

    ' 如果所需高度大于当前行高，则调整行高
    If requiredHeight > currentHeight Then
        modDebug.LogInfo "所需高度 " & requiredHeight & " 大于当前行高 " & currentHeight & "，需要调整", "modGantt.AdjustRowHeightWithPadding"

        ' 尝试调整行高，使用错误处理以防失败
        On Error Resume Next

        ' 检查工作表是否被保护
        Dim isProtected As Boolean
        isProtected = ws.ProtectContents

        ' 如果工作表被保护，临时取消保护
        Dim tempPassword As String
        tempPassword = ""  ' 如果有密码，需要提供

        If isProtected Then
            modDebug.LogVerbose "工作表被保护，临时取消保护", "modGantt.AdjustRowHeightWithPadding"
            ws.Unprotect Password:=tempPassword
        End If

        ' 尝试设置行高
        ws.Rows(row).rowHeight = requiredHeight

        ' 立即读取实际行高
        Dim actualHeight As Double
        actualHeight = ws.Rows(row).height
        modDebug.LogVerbose "设置行高为 " & requiredHeight & "，实际行高为 " & actualHeight, "modGantt.AdjustRowHeightWithPadding"

        ' 检查是否成功设置
        If Err.Number <> 0 Then
            modDebug.LogWarning "无法设置行高: " & Err.Number & " - " & Err.description, "modGantt.AdjustRowHeightWithPadding"
            Err.Clear

            ' 尝试使用另一种方法
            ws.Cells(row, 1).EntireRow.rowHeight = requiredHeight

            If Err.Number <> 0 Then
                modDebug.LogWarning "第二次尝试设置行高也失败: " & Err.Number & " - " & Err.description, "modGantt.AdjustRowHeightWithPadding"
                Err.Clear
            Else
                modDebug.LogInfo "使用替代方法成功调整第 " & row & " 行高度为 " & requiredHeight, "modGantt.AdjustRowHeightWithPadding"
            End If
        Else
            modDebug.LogInfo "已调整第 " & row & " 行高度为 " & requiredHeight, "modGantt.AdjustRowHeightWithPadding"
        End If

        ' 调整形状和标签的垂直位置，使其居中于行
        ' 计算行的中心Y坐标
        Dim rowCenterY As Double
        rowCenterY = ws.Cells(row, 1).top + ws.Cells(row, 1).height / 2

        ' 计算形状组合的中心Y坐标
        Dim shapeCenterY As Double
        shapeCenterY = topMost + (bottomMost - topMost) / 2

        ' 计算需要移动的距离
        Dim moveDistance As Double
        moveDistance = rowCenterY - shapeCenterY

        ' 如果需要移动的距离超过一定阈值，则移动形状和标签
        If Abs(moveDistance) > 0.5 Then ' 0.5像素的阈值，确保同行任务条精确对齐
            modDebug.LogVerbose "需要调整形状和标签的垂直位置，移动距离: " & moveDistance, "modGantt.AdjustRowHeightWithPadding"

            ' 尝试移动形状和标签
            On Error Resume Next

            ' 使用之前检查到的parentGroup
            If Not parentGroup Is Nothing Then
                ' 形状已经在组中，移动整个组
                parentGroup.IncrementTop moveDistance
                modDebug.LogVerbose "移动形状组，ID: " & parentGroup.ID & ", 距离: " & moveDistance, "modGantt.AdjustRowHeightWithPadding"
            Else
                ' 单独移动形状和标签
                shapeObj.IncrementTop moveDistance
                labelObj.IncrementTop moveDistance
                modDebug.LogVerbose "分别移动形状和标签，距离: " & moveDistance, "modGantt.AdjustRowHeightWithPadding"
            End If

            If Err.Number <> 0 Then
                modDebug.LogWarning "调整形状位置失败: " & Err.Number & " - " & Err.description, "modGantt.AdjustRowHeightWithPadding"
                Err.Clear
            End If

            On Error GoTo ErrorHandler
        Else
            modDebug.LogVerbose "形状和标签已经精确居中于行（偏差 " & moveDistance & " 像素），无需调整位置", "modGantt.AdjustRowHeightWithPadding"
        End If

        ' 如果之前工作表被保护，恢复保护
        If isProtected Then
            modDebug.LogVerbose "恢复工作表保护", "modGantt.AdjustRowHeightWithPadding"
            ws.Protect Password:=tempPassword
        End If

        On Error GoTo ErrorHandler
    Else
        modDebug.LogVerbose "所需高度 " & requiredHeight & " 不大于当前行高 " & currentHeight & "，无需调整", "modGantt.AdjustRowHeightWithPadding"
    End If

    ' 记录函数退出
    modDebug.LogFunctionExit "modGantt.AdjustRowHeightWithPadding", "成功"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modGantt.AdjustRowHeightWithPadding"
    modDebug.LogFunctionExit "modGantt.AdjustRowHeightWithPadding", "失败 - " & Err.description
    ' 不抛出错误，因为这只是一个辅助功能
End Sub

' ---------------------------------------------------------
' 辅助函数
' ---------------------------------------------------------

' 获取ISO 8601标准的周数
Private Function GetWeekNumber(dateValue As Date) As String
    On Error GoTo ErrorHandler

    Dim weekNum As Integer
    weekNum = WorksheetFunction.weekNum(dateValue, 2) ' 使用ISO 8601标准（参数2）
    GetWeekNumber = Format(weekNum, "00")
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modGantt.GetWeekNumber"
    GetWeekNumber = "01" ' 返回默认值
End Function

' 从十六进制颜色代码获取RGB颜色值
Private Function GetRGBColor(hexColor As String) As Long
    On Error GoTo ErrorHandler

    ' 如果颜色代码为空或不是字符串，返回默认颜色
    If hexColor = "" Or Not IsString(hexColor) Then
        modDebug.LogWarning "无效的颜色代码", "modGantt.GetRGBColor"
        GetRGBColor = RGB(0, 0, 0) ' 黑色
        Exit Function
    End If

    ' 移除可能的#前缀
    If left(hexColor, 1) = "#" Then
        hexColor = Mid(hexColor, 2)
    End If

    ' 确保是6位十六进制颜色代码
    If Len(hexColor) <> 6 Then
        modDebug.LogWarning "颜色代码长度不是6位: " & hexColor, "modGantt.GetRGBColor"
        GetRGBColor = RGB(0, 0, 0) ' 黑色
        Exit Function
    End If

    ' 检查是否全是十六进制字符
    Dim i As Long
    For i = 1 To 6
        Dim c As String
        c = Mid(hexColor, i, 1)
        If InStr("0123456789ABCDEFabcdef", c) = 0 Then
            modDebug.LogWarning "颜色代码包含非十六进制字符: " & hexColor, "modGantt.GetRGBColor"
            GetRGBColor = RGB(0, 0, 0) ' 黑色
            Exit Function
        End If
    Next i

    ' 提取RGB分量
    Dim r As Long, g As Long, b As Long
    r = CLng("&H" & Mid(hexColor, 1, 2))
    g = CLng("&H" & Mid(hexColor, 3, 2))
    b = CLng("&H" & Mid(hexColor, 5, 2))

    ' 返回RGB颜色值
    GetRGBColor = RGB(r, g, b)
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modGantt.GetRGBColor"
    GetRGBColor = RGB(0, 0, 0) ' 黑色
End Function

' 检查变量是否为字符串
Private Function IsString(var As Variant) As Boolean
    On Error GoTo ErrorHandler

    IsString = (TypeName(var) = "String")
    Exit Function

ErrorHandler:
    IsString = False
End Function

' 判断颜色是否为深色
Private Function IsColorDark(rgbColor As Long) As Boolean
    ' 提取RGB分量
    Dim r As Integer, g As Integer, b As Integer
    r = rgbColor Mod 256
    g = (rgbColor \ 256) Mod 256
    b = (rgbColor \ 65536) Mod 256

    ' 计算亮度（使用感知亮度公式）
    ' 亮度 = 0.299*R + 0.587*G + 0.114*B
    Dim brightness As Double
    brightness = (0.299 * r + 0.587 * g + 0.114 * b)

    ' 亮度小于128认为是深色
    IsColorDark = (brightness < 128)

    modDebug.LogVerbose "颜色亮度分析: R=" & r & ", G=" & g & ", B=" & b & _
        ", 亮度=" & brightness & ", 是深色=" & IsColorDark, _
        "modGantt.IsColorDark"
End Function

' 获取与背景色对比的文本颜色
Private Function GetContrastTextColor(backgroundColor As Long) As Long
    ' 根据背景色亮度自动设置文字颜色
    If IsColorDark(backgroundColor) Then
        GetContrastTextColor = RGB(255, 255, 255) ' 深色背景使用白色文字
    Else
        GetContrastTextColor = RGB(0, 0, 0) ' 浅色背景使用黑色文字
    End If

    modDebug.LogVerbose "为背景色生成对比文本颜色: 背景色=" & backgroundColor & ", 文本色=" & GetContrastTextColor, _
        "modGantt.GetContrastTextColor"
End Function

' 获取指定行中有数据的最后一列
Private Function GetLastColumnWithData(ws As Worksheet, rowNum As Long) As Long
    On Error GoTo ErrorHandler

    ' 使用End(xlToRight)获取最后一列
    Dim lastCol As Long
    lastCol = ws.Cells(rowNum, ws.Columns.Count).End(xlToLeft).Column

    ' 如果没有找到数据，返回默认值
    If lastCol < 3 Then
        lastCol = 3 ' 默认返回 C 列
    End If

    GetLastColumnWithData = lastCol
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modGantt.GetLastColumnWithData"
    GetLastColumnWithData = 3 ' 出错时返回 C 列
End Function

' 注意：GetColumnFromDate函数已被移除，使用CalculateXCoordinate代替

' 绘制基准线（使用明确的上下边界）
Private Sub DrawBaselineWithBounds(baselineDate As Date, ws As Worksheet, timelineCoords As Dictionary, topRow As Long, bottomRow As Long)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modGantt.DrawBaselineWithBounds", "基准线日期: " & Format(baselineDate, "yyyy-mm-dd") & ", 上边界行: " & topRow & ", 下边界行: " & bottomRow

    ' 检查基准线日期是否在时间轴范围内
    If baselineDate < timelineCoords("StartDate") Or baselineDate > timelineCoords("EndDate") Then
        modDebug.LogWarning "基准线日期 " & Format(baselineDate, "yyyy-mm-dd") & " 不在时间轴范围内，跳过绘制", "modGantt.DrawBaselineWithBounds"
        Exit Sub
    End If

    ' 计算基准线的X坐标
    Dim baselineX As Double
    baselineX = CalculateXCoordinate(baselineDate, timelineCoords)
    modDebug.LogVerbose "基准线X坐标: " & baselineX, "modGantt.DrawBaselineWithBounds"

    ' 确保边界有效
    If bottomRow < topRow Then bottomRow = topRow ' 确保至少有一行
    modDebug.LogVerbose "甘特图区域 - 顶部行: " & topRow & ", 底部行: " & bottomRow, "modGantt.DrawBaselineWithBounds"

    ' 计算线的起点和终点坐标
    Dim startY As Double, endY As Double
    startY = ws.Cells(topRow, 1).top
    endY = ws.Cells(bottomRow, 1).top + ws.Cells(bottomRow, 1).height
    modDebug.LogVerbose "基准线坐标 - X: " & baselineX & ", 起点Y: " & startY & ", 终点Y: " & endY, "modGantt.DrawBaselineWithBounds"

    ' 创建基准线形状
    Dim baselineShape As shape
    On Error Resume Next
    Set baselineShape = ws.Shapes.AddLine(baselineX, startY, baselineX, endY)

    If Err.Number <> 0 Then
        modDebug.LogError Err.Number, "创建基准线形状失败 - " & Err.description, "modGantt.DrawBaselineWithBounds"
        modDebug.LogFunctionExit "modGantt.DrawBaselineWithBounds", "失败 - 无法创建基准线形状"
        Exit Sub
    End If

    On Error GoTo ErrorHandler
    modDebug.LogVerbose "基准线形状创建成功，形状ID: " & baselineShape.ID, "modGantt.DrawBaselineWithBounds"

    ' 直接获取配置参数
    Dim baselineColor As String
    Dim lineStyle As Long
    Dim lineWeight As Single

    ' 获取基准线颜色
    baselineColor = CStr(GetConfig("BaselineColor", "#FF0000"))

    ' 获取基准线样式
    lineStyle = CLng(Val(GetConfig("BaselineStyle", 2)))

    ' 获取基准线宽度
    lineWeight = CSng(Val(GetConfig("BaselineWeight", 1.5)))

    ' 设置基准线格式
    With baselineShape.Line
        ' 设置颜色
        .ForeColor.RGB = GetRGBColor(baselineColor)

        ' 设置线型
        .DashStyle = lineStyle

        ' 设置线宽
        .Weight = lineWeight
    End With
    modDebug.LogVerbose "基准线格式设置完成", "modGantt.DrawBaselineWithBounds"

    ' 记录函数退出
    modDebug.LogFunctionExit "modGantt.DrawBaselineWithBounds", "成功"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modGantt.DrawBaselineWithBounds"
    modDebug.LogFunctionExit "modGantt.DrawBaselineWithBounds", "失败 - " & Err.description
End Sub

' 绘制当前日期线
Private Sub DrawCurrentDateLine(currentDate As Date, ws As Worksheet, timelineCoords As Dictionary, topRow As Long, bottomRow As Long)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modGantt.DrawCurrentDateLine", "当前日期: " & Format(currentDate, "yyyy-mm-dd") & ", 上边界行: " & topRow & ", 下边界行: " & bottomRow

    ' 检查当前日期是否在时间轴范围内
    If currentDate < timelineCoords("StartDate") Or currentDate > timelineCoords("EndDate") Then
        modDebug.LogWarning "当前日期 " & Format(currentDate, "yyyy-mm-dd") & " 不在时间轴范围内，跳过绘制", "modGantt.DrawCurrentDateLine"
        Exit Sub
    End If

    ' 计算当前日期线的X坐标
    Dim currentDateX As Double
    currentDateX = CalculateXCoordinate(currentDate, timelineCoords)
    modDebug.LogVerbose "当前日期线X坐标: " & currentDateX, "modGantt.DrawCurrentDateLine"

    ' 确保边界有效
    If bottomRow < topRow Then bottomRow = topRow ' 确保至少有一行
    modDebug.LogVerbose "甘特图区域 - 顶部行: " & topRow & ", 底部行: " & bottomRow, "modGantt.DrawCurrentDateLine"

    ' 计算线的起点和终点坐标
    Dim startY As Double, endY As Double
    startY = ws.Cells(topRow, 1).top
    endY = ws.Cells(bottomRow, 1).top + ws.Cells(bottomRow, 1).height
    modDebug.LogVerbose "当前日期线坐标 - X: " & currentDateX & ", 起点Y: " & startY & ", 终点Y: " & endY, "modGantt.DrawCurrentDateLine"

    ' 创建当前日期线形状
    Dim currentDateShape As shape
    On Error Resume Next
    Set currentDateShape = ws.Shapes.AddLine(currentDateX, startY, currentDateX, endY)

    If Err.Number <> 0 Then
        modDebug.LogError Err.Number, "创建当前日期线形状失败 - " & Err.description, "modGantt.DrawCurrentDateLine"
        modDebug.LogFunctionExit "modGantt.DrawCurrentDateLine", "失败 - 无法创建当前日期线形状"
        Exit Sub
    End If

    On Error GoTo ErrorHandler
    modDebug.LogVerbose "当前日期线形状创建成功，形状ID: " & currentDateShape.ID, "modGantt.DrawCurrentDateLine"

    ' 直接获取配置参数
    Dim currentDateLineColor As String
    Dim lineStyle As Long
    Dim lineWeight As Single

    ' 获取当前日期线颜色
    currentDateLineColor = CStr(GetConfig("CurrentDateLineColor", "#41B7AC"))

    ' 获取当前日期线样式
    lineStyle = CLng(Val(GetConfig("CurrentDateLineStyle", 2)))

    ' 获取当前日期线宽度
    lineWeight = CSng(Val(GetConfig("CurrentDateLineWeight", 0.8)))

    ' 设置当前日期线格式
    With currentDateShape.Line
        ' 设置颜色
        .ForeColor.RGB = GetRGBColor(currentDateLineColor)

        ' 设置线型
        .DashStyle = lineStyle

        ' 设置线宽
        .Weight = lineWeight
    End With
    modDebug.LogVerbose "当前日期线格式设置完成", "modGantt.DrawCurrentDateLine"

    ' 记录函数退出
    modDebug.LogFunctionExit "modGantt.DrawCurrentDateLine", "成功"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modGantt.DrawCurrentDateLine"
    modDebug.LogFunctionExit "modGantt.DrawCurrentDateLine", "失败 - " & Err.description
End Sub

' 保留原始函数以保持兼容性
Private Sub DrawBaseline(baselineDate As Date, ws As Worksheet, timelineCoords As Dictionary)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modGantt.DrawBaseline", "基准线日期: " & Format(baselineDate, "yyyy-mm-dd")

    ' 检查基准线日期是否在时间轴范围内
    If baselineDate < timelineCoords("StartDate") Or baselineDate > timelineCoords("EndDate") Then
        modDebug.LogWarning "基准线日期 " & Format(baselineDate, "yyyy-mm-dd") & " 不在时间轴范围内，跳过绘制", "modGantt.DrawBaseline"
        Exit Sub
    End If

    ' 计算基准线的X坐标
    Dim baselineX As Double
    baselineX = CalculateXCoordinate(baselineDate, timelineCoords)
    modDebug.LogVerbose "基准线X坐标: " & baselineX, "modGantt.DrawBaseline"

    ' 获取甘特图区域的上下边界
    Dim topRow As Long, bottomRow As Long
    topRow = 6 ' 甘特图内容从第6行开始

    ' 找到最后一行（有内容的最后一行）
    bottomRow = ws.Cells(ws.Rows.Count, 2).End(xlUp).row
    If bottomRow < topRow Then bottomRow = topRow ' 确保至少有一行
    modDebug.LogVerbose "甘特图区域 - 顶部行: " & topRow & ", 底部行: " & bottomRow, "modGantt.DrawBaseline"

    ' 调用新的DrawBaselineWithBounds函数
    modDebug.LogVerbose "调用DrawBaselineWithBounds函数，传递参数: topRow=" & topRow & ", bottomRow=" & bottomRow, "modGantt.DrawBaseline"
    DrawBaselineWithBounds baselineDate, ws, timelineCoords, topRow, bottomRow

    ' 记录函数退出
    modDebug.LogFunctionExit "modGantt.DrawBaseline", "成功 - 通过DrawBaselineWithBounds实现"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modGantt.DrawBaseline"
    modDebug.LogFunctionExit "modGantt.DrawBaseline", "失败 - " & Err.description
End Sub

' 合并类别标题区域（高效版）
Private Sub MergeCategoryTitles(ws As Worksheet, topRow As Long, bottomRow As Long)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modGantt.MergeCategoryTitles", "顶部行: " & topRow & ", 底部行: " & bottomRow

    ' 直接获取配置参数
    Dim fontName As String, fontSize As Single, isBold As Boolean, columnWidth As Double

    ' 获取字体名称
    fontName = CStr(GetConfig("CategoryFont", "Barlow"))

    ' 获取字体大小
    fontSize = CSng(Val(GetConfig("CategoryFontSize", 11)))

    ' 获取是否粗体
    isBold = CBool(Val(GetConfig("CategoryFontBold", True)))

    ' 获取B列宽度
    columnWidth = CDbl(Val(GetConfig("CategoryColumnWidth", 30)))

    ' 关闭屏幕更新和警告，提高性能
    Application.ScreenUpdating = False
    Application.DisplayAlerts = False
    Application.Calculation = xlCalculationManual

    ' 设置B列宽度
    ws.Columns("B").columnWidth = columnWidth
    modDebug.LogVerbose "设置B列宽度为: " & columnWidth, "modGantt.MergeCategoryTitles"

    ' 一次性读取B列数据到数组
    Dim dataRange As range
    Set dataRange = ws.range("B" & topRow & ":B" & bottomRow)

    Dim dataArray As Variant
    dataArray = dataRange.value

    ' 创建集合存储需要合并的区域
    Dim mergeRanges As New Collection

    Dim i As Long
    Dim mergeStartRow As Long
    Dim inMergeRegion As Boolean
    Dim lastNonEmptyIndex As Long

    inMergeRegion = False
    lastNonEmptyIndex = -1

    ' 在数组中分析需要合并的区域
    For i = 1 To UBound(dataArray, 1)
        ' 检查当前单元格是否有内容
        If Trim(CStr(dataArray(i, 1))) <> "" Then
            ' 记录非空单元格索引
            lastNonEmptyIndex = i

            ' 如果已经在合并区域中，记录之前的区域
            If inMergeRegion Then
                ' 确保合并区域至少有两行
                If i - 1 > mergeStartRow Then
                    ' 添加合并区域到集合
                    mergeRanges.Add Array(mergeStartRow + topRow - 1, i + topRow - 2)
                    modDebug.LogVerbose "添加合并区域: B" & (mergeStartRow + topRow - 1) & ":B" & (i + topRow - 2), "modGantt.MergeCategoryTitles"
                End If
            End If

            ' 记录新的合并区域起始行
            mergeStartRow = i
            inMergeRegion = True
        End If
    Next i

    ' 处理最后一个合并区域（如果存在）
    If inMergeRegion And UBound(dataArray, 1) > mergeStartRow Then
        mergeRanges.Add Array(mergeStartRow + topRow - 1, bottomRow)
        modDebug.LogVerbose "添加最后合并区域: B" & (mergeStartRow + topRow - 1) & ":B" & bottomRow, "modGantt.MergeCategoryTitles"
    End If

    ' 批量执行所有合并操作
    If mergeRanges.Count > 0 Then
        Dim j As Long
        Dim mergeRange As Variant

        ' 执行所有合并操作
        For j = 1 To mergeRanges.Count
            mergeRange = mergeRanges(j)

            ' 合并单元格
            ws.range("B" & mergeRange(0) & ":B" & mergeRange(1)).Merge

            ' 设置合并后单元格的格式
            With ws.Cells(mergeRange(0), 2)
                .HorizontalAlignment = xlCenter ' 水平居中
                .VerticalAlignment = xlCenter   ' 垂直居中
                .WrapText = True                ' 文字自动换行
                .Font.Name = fontName
                .Font.size = fontSize
                .Font.Bold = isBold
            End With

            modDebug.LogVerbose "已合并并格式化区域: B" & mergeRange(0) & ":B" & mergeRange(1), "modGantt.MergeCategoryTitles"
        Next j
    End If

    ' 恢复计算模式和屏幕更新
    Application.Calculation = xlCalculationAutomatic
    Application.DisplayAlerts = True
    Application.ScreenUpdating = True

    modDebug.LogFunctionExit "modGantt.MergeCategoryTitles", "成功 - 合并了 " & mergeRanges.Count & " 个区域"
    Exit Sub

ErrorHandler:
    ' 确保恢复计算模式和屏幕更新
    Application.Calculation = xlCalculationAutomatic
    Application.DisplayAlerts = True
    Application.ScreenUpdating = True

    modDebug.LogError Err.Number, Err.description, "modGantt.MergeCategoryTitles"
    modDebug.LogFunctionExit "modGantt.MergeCategoryTitles", "失败 - " & Err.description
End Sub

' 转换颜色代码（处理G/Y/R/S等预定义代码）
Private Function ConvertColorCode(colorValue As String, taskType As String) As String
    On Error GoTo ErrorHandler

    ' 如果颜色值为空，使用默认颜色
    If Trim(colorValue) = "" Then
        If taskType = "A" Then
            ConvertColorCode = GetConfig("DefaultTaskColor", "#3366CC")
        Else
            ConvertColorCode = GetConfig("DefaultMilestoneColor", "#FF9900")
        End If
        Exit Function
    End If

    ' 转换预定义颜色代码
    Select Case UCase(Trim(colorValue))
        Case "G"
            ConvertColorCode = GetConfig("DefaultColorG", "#00FF00")
        Case "Y"
            ConvertColorCode = GetConfig("DefaultColorY", "#FFFF00")
        Case "R"
            ConvertColorCode = GetConfig("DefaultColorR", "#FF0000")
        Case "S"
            ConvertColorCode = GetConfig("DefaultColorS", "#800080")
        Case Else
            ' 假设是十六进制格式，直接返回
            ConvertColorCode = colorValue
    End Select
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modGantt.ConvertColorCode"
    ' 返回默认颜色
    If taskType = "A" Then
        ConvertColorCode = GetConfig("DefaultTaskColor", "#3366CC")
    Else
        ConvertColorCode = GetConfig("DefaultMilestoneColor", "#FF9900")
    End If
End Function















