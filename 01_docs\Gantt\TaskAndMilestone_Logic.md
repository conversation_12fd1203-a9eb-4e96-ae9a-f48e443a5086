# 甘特图任务条和里程碑处理逻辑说明文档

本文档详细说明了甘特图系统中任务条和里程碑的输入、输出以及处理逻辑。

## 目录

1. [概述](#概述)
2. [坐标系统](#坐标系统)
3. [任务条处理逻辑](#任务条处理逻辑)
4. [里程碑处理逻辑](#里程碑处理逻辑)
5. [标签处理逻辑](#标签处理逻辑)
6. [配置参数](#配置参数)
7. [输入输出说明](#输入输出说明)
8. [处理流程图](#处理流程图)

## 概述

甘特图系统中的图形元素主要包括两种类型：
1. **任务条**：表示有持续时间的任务，使用矩形表示
2. **里程碑**：表示关键时间点，使用菱形表示

这两种元素都有相应的标签，用于显示任务或里程碑的描述信息。标签的位置可以通过 `TextPosition` 属性进行配置。

## 坐标系统

甘特图使用基于时间轴的坐标系统，将日期映射到画布上的X坐标。

### 坐标系建立

坐标系通过 `EstablishTimelineCoordinateSystem` 函数建立，主要包含以下信息：

- **原点X坐标**：时间轴起始位置的X坐标（C5单元格的左边缘）
- **宽度**：时间轴的总宽度
- **起始日期**：项目开始日期所在月份的第一天
- **结束日期**：项目结束日期所在月份的最后一天
- **总天数**：时间轴覆盖的总天数

### 日期到坐标的转换

通过 `CalculateXCoordinate` 函数将日期转换为X坐标：

```vba
xCoord = coords("OriginX") + (coords("Width") * daysDiff / totalDaysAdjusted)
```

其中：
- `daysDiff` 是目标日期与起始日期的天数差（加1修正）
- `totalDaysAdjusted` 是总天数（加1修正）

## 任务条处理逻辑

任务条通过 `DrawTask` 函数绘制，表示有开始日期和结束日期的任务。

### 输入参数

- **task**：包含任务信息的字典对象
- **row**：任务在甘特图上的行位置
- **timelineCoords**：时间轴坐标系信息

### 处理步骤

1. **获取配置参数**
   - 任务条高度（默认11像素）
   - 任务条边框宽度（默认0，表示无边框）
   - 进度条颜色（默认绿色 #66CC66）
   - 标签距离（默认5像素）
   - 行高预留空隙（默认3像素）

2. **计算任务条位置和大小**
   - 使用 `CalculateXCoordinate` 函数将任务的开始日期和结束日期转换为X坐标
   - 计算任务条的左边缘、宽度和高度
   - 计算任务条的垂直位置，使其在行内垂直居中

3. **创建任务条形状**
   - 使用 `AddShape` 方法创建矩形形状
   - 设置任务条的填充颜色
   - 根据配置设置任务条边框

4. **绘制进度条（如果有进度）**
   - 如果任务进度大于0，计算进度条宽度（任务条宽度 * 进度）
   - 创建进度条形状（矩形）
   - 设置进度条颜色和边框
   - 将进度条置于最顶层

5. **添加任务描述标签**
   - 调用 `AddTaskLabelWithCoordinates2` 函数创建任务描述标签
   - 传递任务条中心坐标、任务条高度和标签距离参数

6. **调整行高**
   - 调用 `AdjustRowHeightWithPadding` 函数动态调整行高
   - 确保任务条和标签能够完全显示，并考虑上下预留空隙

### 输出

函数没有直接的返回值，但会在工作表上创建以下图形元素：
1. 任务条形状（矩形）
2. 进度条形状（如果有进度）
3. 任务描述标签（文本框）

## 里程碑处理逻辑

里程碑通过 `DrawMilestone` 函数绘制，表示关键时间点。

### 输入参数

- **task**：包含里程碑信息的字典对象
- **row**：里程碑在甘特图上的行位置
- **timelineCoords**：时间轴坐标系信息

### 处理步骤

1. **获取配置参数**
   - 里程碑大小（默认11像素）
   - 里程碑边框宽度（默认0，表示无边框）
   - 标签距离（默认5像素）
   - 行高预留空隙（默认3像素）

2. **计算里程碑位置**
   - 使用 `CalculateXCoordinate` 函数将里程碑日期转换为X坐标
   - 计算Y坐标，使里程碑在行内垂直居中
   - 计算里程碑形状的左上角坐标，考虑里程碑大小

3. **创建里程碑形状**
   - 使用 `AddShape` 方法创建菱形形状
   - 设置里程碑的填充颜色
   - 根据配置设置里程碑边框

4. **添加里程碑描述标签**
   - 调用 `AddTaskLabelWithCoordinates2` 函数创建里程碑描述标签
   - 传递里程碑中心坐标、里程碑大小和标签距离参数

5. **调整行高**
   - 调用 `AdjustRowHeightWithPadding` 函数动态调整行高
   - 确保里程碑和标签能够完全显示，并考虑上下预留空隙

### 输出

函数没有直接的返回值，但会在工作表上创建以下图形元素：
1. 里程碑形状（菱形）
2. 里程碑描述标签（文本框）

## 标签处理逻辑

标签通过 `AddTaskLabelWithCoordinates2` 函数创建，用于显示任务或里程碑的描述信息。

### 输入参数

- **task**：包含任务或里程碑信息的字典对象
- **shape**：任务条或里程碑形状对象
- **centerX**：形状中心的X坐标
- **centerY**：形状中心的Y坐标
- **shapeSize**：形状的大小（高度）
- **labelDistance**：标签与形状的距离

### 处理步骤

1. **判断形状类型**
   - 检测形状是里程碑（菱形）还是任务条（矩形）
   - 根据形状类型调整后续处理逻辑

2. **获取任务描述**
   - 从任务字典中获取描述文本

3. **计算文本尺寸**
   - 创建临时文本框以计算文本的实际尺寸
   - 设置文本框边距，使标签框尺寸更小

4. **确定标签位置和对齐方式**
   - 检查是否有指定的 `TextPosition` 属性
   - 检查标签是否能放在形状内部
   - 根据 `TextPosition` 或自动判断最佳位置：
     - **right**：放在形状右侧，左对齐，水平中心与形状中心水平对齐
     - **left**：放在形状左侧，右对齐，水平中心与形状中心水平对齐
     - **top**：放在形状上方，左对齐（任务条）或居中（里程碑）
     - **bottom**：放在形状下方，左对齐（任务条）或居中（里程碑）
   - 对于里程碑，标签位置会进行特殊调整：
     - 左/右位置：使用 0.7 的系数调整水平距离
     - 上/下位置：标签水平居中于菱形

5. **创建标签文本框**
   - 使用计算好的位置和尺寸创建文本框
   - 设置文本内容、字体、大小和对齐方式
   - 设置文本框透明（无填充和边框）

### 输出

函数返回创建的标签形状对象（Shape）。

## 配置参数

甘特图系统使用以下配置参数来控制任务条和里程碑的外观：

| 配置ID | 配置名称 | 默认值 | 说明 |
|--------|----------|--------|------|
| GT026 | TaskBarHeight | 11 | 任务条高度/里程碑大小（像素） |
| GT027 | TaskBarBorderWidth | 0 | 任务条/里程碑边框宽度（0=无边框） |
| GT028 | ProgressBarColor | #66CC66 | 进度条颜色（绿色） |
| GT029 | LabelDistance | 5 | 标签与任务条/里程碑的距离（像素） |
| GT030 | RowPadding | 3 | 行高上下预留空隙（像素） |

这些参数可以在Config工作表中设置，也可以通过代码动态修改。

## 输入输出说明

### 任务条（DrawTask）

#### 输入

任务字典必须包含以下键：
- **StartDate**：任务开始日期
- **EndDate**：任务结束日期
- **Color**：任务颜色（十六进制格式，如"#FF0000"）
- **Description**：任务描述（用于标签）
- **Progress**：任务进度（0-1之间的小数）
- **TextPosition**（可选）：标签位置（"left"、"right"、"top"、"bottom"或"inside"）
- **ID**（可选）：任务ID（用于日志记录）

#### 输出

在工作表上创建的图形元素：
1. 任务条形状（矩形）
2. 进度条形状（如果有进度）
3. 任务描述标签（文本框）

### 里程碑（DrawMilestone）

#### 输入

任务字典必须包含以下键：
- **StartDate**：里程碑日期
- **Color**：里程碑颜色（十六进制格式，如"#FF0000"）
- **Description**：里程碑描述（用于标签）
- **TextPosition**（可选）：标签位置（"left"、"right"、"top"、"bottom"）
- **ID**（可选）：里程碑ID（用于日志记录）

#### 输出

在工作表上创建的图形元素：
1. 里程碑形状（菱形）
2. 里程碑描述标签（文本框）

## 处理流程图

### 任务条处理流程

```mermaid
flowchart TD
    A[开始] --> B[获取配置参数]
    B --> C[获取GanttChart工作表引用]
    C --> D[计算任务条位置和大小]
    D --> E[创建任务条形状]
    E --> F{创建成功?}
    F -->|否| G[记录错误并退出]
    F -->|是| H[设置任务条填充颜色]
    H --> I{边框宽度为0?}
    I -->|是| J[设置无边框]
    I -->|否| K[设置边框宽度和颜色]
    J --> L
    K --> L{任务有进度?}
    L -->|是| M[计算进度条宽度]
    M --> N[创建进度条形状]
    N --> O{创建成功?}
    O -->|否| P[记录错误]
    O -->|是| Q[设置进度条填充颜色]
    Q --> R{边框宽度为0?}
    R -->|是| S[设置无边框]
    R -->|否| T[设置边框宽度和颜色]
    S --> U[将进度条置于最顶层]
    T --> U
    U --> V
    L -->|否| V[添加任务描述标签]
    P --> V
    V --> W{标签创建成功?}
    W -->|否| X[记录警告]
    W -->|是| Y[动态调整行高，考虑预留空隙]
    X --> Z
    Y --> Z[记录函数退出]
    Z --> AA[结束]
    G --> AA
```

### 里程碑处理流程

```mermaid
flowchart TD
    A[开始] --> B[获取配置参数]
    B --> C[获取GanttChart工作表引用]
    C --> D[计算里程碑位置]
    D --> E[创建里程碑形状]
    E --> F{创建成功?}
    F -->|否| G[记录错误并退出]
    F -->|是| H[设置里程碑填充颜色]
    H --> I{边框宽度为0?}
    I -->|是| J[设置无边框]
    I -->|否| K[设置边框宽度和颜色]
    J --> L[添加里程碑描述标签]
    K --> L
    L --> M{标签创建成功?}
    M -->|否| N[记录警告]
    M -->|是| O[动态调整行高，考虑预留空隙]
    N --> P
    O --> P[记录函数退出]
    P --> Q[结束]
    G --> Q
```

### 标签处理流程

```mermaid
flowchart TD
    A[开始] --> B[判断形状类型]
    B --> C[获取任务描述]
    C --> D[计算文本尺寸]
    D --> E{标签能放在形状内部?}
    E -->|是| F[放在形状内部，居中对齐]
    E -->|否| G{有指定TextPosition?}
    G -->|是| H{TextPosition=?}
    G -->|否| I[自动判断最佳位置]
    H -->|right| J[放在右侧]
    H -->|left| K[放在左侧]
    H -->|top| L[放在上方]
    H -->|bottom| M[放在下方]
    J --> N{是里程碑?}
    K --> N
    L --> N
    M --> N
    N -->|是| O[调整标签位置适应里程碑]
    N -->|否| P[使用标准任务条标签位置]
    O --> Q
    P --> Q
    F --> Q
    I --> Q[创建标签文本框]
    Q --> R{创建成功?}
    R -->|否| S[记录错误并退出]
    R -->|是| T[设置标签文本和格式]
    T --> U[设置标签透明]
    U --> V[返回标签形状]
    S --> W[返回Nothing]
    V --> X[结束]
    W --> X
```

## 注意事项

1. **坐标系统**：
   - 坐标系的原点是时间轴起始位置的X坐标（C5单元格的左边缘）
   - 日期到坐标的转换考虑了天数差和总天数的修正（+1）

2. **任务条和里程碑的区别**：
   - 任务条表示有持续时间的任务，使用矩形表示
   - 里程碑表示关键时间点，使用菱形表示
   - 任务条可以有进度，里程碑没有进度

3. **标签位置**：
   - 任务条和里程碑的标签位置处理逻辑不同
   - 里程碑标签在上/下位置时会水平居中于菱形
   - 任务条标签在上/下位置时会左对齐于任务条左边缘

4. **行高调整**：
   - 行高会根据任务条/里程碑和标签的大小动态调整
   - 考虑了上下预留空隙，确保元素完全显示
   - 行高有最大限制，避免过度调整

5. **错误处理**：
   - 所有函数都有详细的错误处理和日志记录
   - 形状创建失败会记录错误并尝试继续执行
   - 标签创建失败不会中断整个流程
