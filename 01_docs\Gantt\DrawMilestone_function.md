# DrawMilestone 函数说明文档

## 功能概述

`DrawMilestone` 函数是甘特图系统中的核心绘图函数之一，负责在甘特图工作表上绘制里程碑图形。该函数根据里程碑的日期，在指定行位置创建菱形形状表示里程碑，并添加里程碑描述标签。

## 函数签名

```vba
Private Sub DrawMilestone(task As Dictionary, row As Long, timelineCoords As Dictionary)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| task | Dictionary | 包含里程碑信息的字典对象，必须包含以下键：<br>- StartDate：里程碑日期<br>- Color：里程碑颜色（十六进制格式，如"#FF0000"）<br>- Description：里程碑描述（可选）<br>- ID：里程碑ID（可选，用于日志记录） |
| row | Long | 里程碑在甘特图上的行位置（行号） |
| timelineCoords | Dictionary | 时间轴坐标系信息，包含以下键：<br>- OriginX：坐标系原点的X坐标<br>- Width：坐标系的总宽度<br>- StartDate：坐标系的起始日期<br>- EndDate：坐标系的结束日期<br>- TotalDays：坐标系覆盖的总天数 |

## 配置参数

函数使用以下配置参数，可以在Config工作表中设置：

| 配置ID | 配置名称 | 默认值 | 说明 |
|--------|----------|--------|------|
| GT026 | TaskBarHeight | 11 | 里程碑大小（像素） |
| GT027 | TaskBarBorderWidth | 0 | 里程碑边框宽度（0=无边框） |
| GT029 | LabelDistance | 5 | 标签与里程碑的距离（像素） |
| GT030 | RowPadding | 3 | 行高上下预留空隙（像素） |

## 输出

函数没有直接的返回值，但会在工作表上创建以下图形元素：
1. 里程碑形状（菱形）
2. 里程碑描述标签（文本框）

## 处理流程

### 1. 初始化和获取配置参数

```vba
' 记录函数进入
Dim taskId As String, taskDesc As String
taskId = IIf(task.Exists("ID"), task("ID"), "未知ID")
taskDesc = IIf(task.Exists("Description"), task("Description"), "未知描述")
modDebug.LogFunctionEntry "modGantt.DrawMilestone", "任务ID: " & taskId & ", 描述: " & taskDesc & ", 行: " & row

' 获取配置参数
Dim milestoneSize As Long
milestoneSize = Val(GetConfigValue("GT026", "11")) ' 默认里程碑大小为11

Dim milestoneBorderWidth As Single
milestoneBorderWidth = Val(GetConfigValue("GT027", "0")) ' 默认无边框

Dim labelDistance As Long
labelDistance = Val(GetConfigValue("GT029", "5")) ' 默认标签距离为5像素

Dim rowPadding As Long
rowPadding = Val(GetConfigValue("GT030", "3")) ' 默认行高上下预留3像素
```

这部分代码初始化函数并获取配置参数：
1. 记录详细的日志信息，包括里程碑ID、描述和行位置
2. 获取里程碑大小（默认11像素）
3. 获取里程碑边框宽度（默认0，表示无边框）
4. 获取标签与里程碑的距离（默认5像素）
5. 获取行高上下预留空隙（默认3像素）

### 2. 计算里程碑位置

```vba
' 计算里程碑在坐标系中的精确位置
Dim milestoneX As Double, milestoneY As Double

milestoneX = CalculateXCoordinate(task("StartDate"), timelineCoords)

' 计算Y坐标 - 使用行的中心点
Dim rowHeight As Double
rowHeight = ws.Cells(row, 1).Height
milestoneY = ws.Cells(row, 1).Top + rowHeight / 2

' 计算里程碑形状的左上角坐标
Dim shapeLeft As Double, shapeTop As Double
shapeLeft = milestoneX - milestoneSize / 2
shapeTop = milestoneY - milestoneSize / 2
```

这部分代码计算里程碑的位置：
1. 使用 `CalculateXCoordinate` 函数将里程碑日期转换为X坐标
2. 计算Y坐标，使里程碑在行内垂直居中
3. 计算里程碑形状的左上角坐标，考虑里程碑大小

### 3. 创建里程碑形状

```vba
' 创建里程碑形状（菱形）
Dim milestoneShape As Shape

On Error Resume Next
Set milestoneShape = ws.Shapes.AddShape(msoShapeDiamond, shapeLeft, shapeTop, milestoneSize, milestoneSize)

If Err.Number <> 0 Then
    modDebug.LogError Err.Number, "创建里程碑形状失败 - " & Err.Description, "modGantt.DrawMilestone"
    modDebug.LogFunctionExit "modGantt.DrawMilestone", "失败 - 无法创建里程碑形状"
    Exit Sub
End If

On Error GoTo ErrorHandler
```

这部分代码创建里程碑形状（菱形），并处理可能的错误：
1. 使用 `AddShape` 方法创建菱形形状
2. 如果创建失败，记录错误并退出函数

### 4. 设置里程碑格式

```vba
' 设置里程碑格式
Dim milestoneColor As String
milestoneColor = task("Color")

milestoneShape.Fill.ForeColor.RGB = GetRGBColor(milestoneColor)

' 设置里程碑边框
If milestoneBorderWidth = 0 Then
    milestoneShape.Line.Visible = msoFalse ' 无边框
Else
    milestoneShape.Line.Visible = msoTrue
    milestoneShape.Line.Weight = milestoneBorderWidth
    milestoneShape.Line.ForeColor.RGB = GetRGBColor(milestoneColor)
End If
```

这部分代码设置里程碑的颜色和边框：
1. 获取里程碑的颜色（十六进制格式）
2. 使用 `GetRGBColor` 函数将十六进制颜色转换为RGB值
3. 设置里程碑的填充颜色
4. 根据配置参数设置里程碑边框：
   - 如果边框宽度为0，则不显示边框
   - 如果边框宽度大于0，则设置边框宽度和颜色

### 5. 添加里程碑描述标签

```vba
' 添加里程碑描述标签（使用坐标系）
Dim labelShape As Shape
Set labelShape = AddTaskLabelWithCoordinates2(task, milestoneShape, milestoneX, milestoneY, milestoneSize, labelDistance)

If Not labelShape Is Nothing Then
    ' 动态调整行高以适应里程碑和标签，考虑上下预留空隙
    AdjustRowHeightWithPadding ws, row, milestoneShape, labelShape, rowPadding
Else
    modDebug.LogWarning "里程碑标签创建失败或返回为Nothing", "modGantt.DrawMilestone"
End If
```

这部分代码添加里程碑描述标签并调整行高：
1. 调用 `AddTaskLabelWithCoordinates2` 函数创建里程碑描述标签，传递标签距离参数
2. 如果标签创建成功，调用 `AdjustRowHeightWithPadding` 函数动态调整行高，确保里程碑和标签能够完全显示，并考虑上下预留空隙

### 6. 错误处理

```vba
' 记录函数退出
modDebug.LogFunctionExit "modGantt.DrawMilestone", "成功"
Exit Sub

ErrorHandler:
modDebug.LogError Err.Number, Err.Description, "modGantt.DrawMilestone"
modDebug.LogFunctionExit "modGantt.DrawMilestone", "失败 - " & Err.Description
Err.Raise Err.Number, "modGantt.DrawMilestone", Err.Description
```

这部分代码处理函数执行过程中可能发生的错误：
1. 如果函数正常执行完成，记录成功退出
2. 如果发生错误，记录错误信息并重新引发错误

## 流程图

```mermaid
flowchart TD
    A[开始] --> B[初始化和获取配置参数]
    B --> C[获取GanttChart工作表引用]
    C --> D[计算里程碑位置]
    D --> E[创建里程碑形状]
    E --> F{创建成功?}
    F -->|否| G[记录错误并退出]
    F -->|是| H[设置里程碑填充颜色]
    H --> I{边框宽度为0?}
    I -->|是| J[设置无边框]
    I -->|否| K[设置边框宽度和颜色]
    J --> L[添加里程碑描述标签]
    K --> L
    L --> M{标签创建成功?}
    M -->|否| N[记录警告]
    M -->|是| O[动态调整行高，考虑预留空隙]
    O --> P[记录函数退出]
    N --> P
    P --> Q[结束]
    G --> Q
```

## 相关函数

### CalculateXCoordinate 函数

```vba
Private Function CalculateXCoordinate(targetDate As Date, coords As Dictionary) As Double
    ' 计算日期与起始日期的天数差
    Dim daysDiff As Long
    daysDiff = DateDiff("d", coords("StartDate"), targetDate)

    ' 计算X坐标
    CalculateXCoordinate = coords("OriginX") + (coords("Width") * daysDiff / coords("TotalDays"))
End Function
```

这个函数将日期转换为X坐标：
1. 计算目标日期与坐标系起始日期的天数差
2. 根据天数差在总天数中的比例计算X坐标

### AddTaskLabelWithCoordinates2 函数

这个函数负责创建里程碑描述标签，并将其放置在里程碑附近。主要功能：
1. 创建临时文本框以计算文本尺寸
2. 根据文本尺寸和里程碑尺寸确定标签位置和对齐方式：
   - 如果标签放在里程碑右侧，则左对齐
   - 如果标签放在里程碑左侧，则右对齐
   - 如果标签放在里程碑上方或下方，则左对齐
3. 创建文本框形状并设置文本内容和格式
4. 根据配置的标签距离参数调整标签位置

### AdjustRowHeightWithPadding 函数

这个函数根据里程碑和标签的大小动态调整行高，确保所有元素都能完全显示。主要功能：
1. 计算里程碑和标签占用的总高度
2. 添加上下预留空隙（使用配置参数）
3. 如果总高度大于当前行高，调整行高以适应所有元素
4. 确保行高不小于最小值

## 注意事项

1. **必需的任务属性**：
   - StartDate：里程碑日期
   - Color：里程碑颜色（十六进制格式）

2. **可选的任务属性**：
   - Description：里程碑描述（用于标签）
   - ID：里程碑ID（用于日志记录）

3. **坐标系要求**：
   - timelineCoords 字典必须包含所有必需的键（OriginX, Width, StartDate, EndDate, TotalDays）
   - 坐标系的日期范围应该包含里程碑日期

4. **错误处理**：
   - 函数使用 On Error Resume Next 单独处理形状创建错误
   - 如果里程碑形状创建失败，函数会退出

5. **配置参数**：
   - 里程碑大小、边框宽度、标签距离和行高预留空隙都是可配置的
   - 这些参数可以在Config工作表中设置，也可以通过代码动态修改

6. **性能考虑**：
   - 创建形状是一个相对耗时的操作，特别是在里程碑数量较多时
   - 函数使用详细的日志记录，有助于调试但可能影响性能

7. **动态行高调整**：
   - 函数会根据里程碑和标签的大小动态调整行高，并考虑上下预留空隙
   - 这确保了所有元素都能完全显示，但可能导致行高不一致

8. **标签位置和对齐**：
   - 标签位置会根据里程碑大小和文本长度智能调整
   - 标签对齐方式会根据位置自动设置：右侧左对齐，左侧右对齐

## 使用示例

在 `DrawTasksAndMilestones` 函数中的调用示例：

```vba
' 第三步：根据任务类型绘制任务或里程碑
If task.Exists("Type") Then
    If task("Type") = "A" Then
        ' 绘制任务条
        DrawTask task, taskRow, timelineCoords
    ElseIf task("Type") = "M" Then
        ' 绘制里程碑
        DrawMilestone task, taskRow, timelineCoords
    Else
        modDebug.LogWarning "任务类型无效: " & task("Type") & "，跳过绘制", "modGantt.DrawTasksAndMilestones"
    End If
Else
    modDebug.LogWarning "任务缺少Type属性，跳过绘制", "modGantt.DrawTasksAndMilestones"
End If
```

## 与其他函数的关系

- **与DrawTasksAndMilestones的关系**：
  - `DrawTasksAndMilestones`调用`DrawMilestone`绘制类型为"M"的里程碑
  - 在调用前，`DrawTasksAndMilestones`已经确定了里程碑的行位置
  - 传递时间轴坐标系信息，避免重复计算

- **与DrawTask的关系**：
  - `DrawMilestone`和`DrawTask`是并列的函数，分别处理不同类型的任务
  - 两个函数都使用相同的坐标系统和标签添加机制
  - `DrawMilestone`处理单一日期的里程碑，而`DrawTask`处理有持续时间的任务（开始日期到结束日期）

- **与坐标系统的关系**：
  - `DrawMilestone`依赖于预先建立的时间轴坐标系统
  - 使用`CalculateXCoordinate`函数将日期转换为X坐标
  - 坐标系统确保了里程碑在时间轴上的准确定位
