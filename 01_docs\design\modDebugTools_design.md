# modDebugTools 模块设计文档

## 1. 模块概述

modDebugTools模块提供调试工具菜单和功能，用于辅助开发和测试过程。它包含一系列调试工具，如查看日志、测试时间轴生成、显示项目信息和任务数据等，帮助开发人员诊断和解决问题。

> 注意：代码中的注释和用户界面消息已更新为英文，以避免在VBA编辑器中出现编码问题。本设计文档仍保持中文说明，以便于中文用户理解。

## 2. 模块结构

```mermaid
classDiagram
    class modDebugTools {
        +ShowDebugTools()
        -ChangeDebugLevel()
        -TestTimelineGeneration()
        -ShowProjectInfo()
        -ShowTasksData()
    }
```

## 3. 主要功能

### 3.1 调试工具菜单流程

```mermaid
flowchart TD
    Start([开始]) --> ShowDebugTools[显示调试工具菜单]
    
    ShowDebugTools --> UserChoice{用户选择}
    UserChoice -->|查看日志文件| OpenLogFile[打开日志文件]
    UserChoice -->|查看日志工作表| ShowLog[显示日志工作表]
    UserChoice -->|清除日志| ClearLog[清除日志]
    UserChoice -->|导出日志| ExportLog[导出日志]
    UserChoice -->|切换调试级别| ChangeLevel[切换调试级别]
    UserChoice -->|测试时间轴生成| TestTimeline[测试时间轴生成]
    UserChoice -->|显示项目信息| ShowProject[显示项目信息]
    UserChoice -->|显示任务数据| ShowTasks[显示任务数据]
    
    OpenLogFile --> End([结束])
    ShowLog --> End
    ClearLog --> End
    ExportLog --> End
    ChangeLevel --> End
    TestTimeline --> End
    ShowProject --> End
    ShowTasks --> End
```

### 3.2 测试时间轴生成流程

```mermaid
flowchart TD
    Start([开始]) --> TestTimelineGeneration[测试时间轴生成]
    
    TestTimelineGeneration --> GetProjectInfo[获取项目信息]
    GetProjectInfo --> CheckDates[检查项目日期]
    
    CheckDates --> CreateWorksheet[创建测试工作表]
    CreateWorksheet --> CalculateTimeRange[计算时间范围]
    
    CalculateTimeRange --> DisplayWeekInfo[显示周信息]
    DisplayWeekInfo --> FormatWorksheet[格式化工作表]
    
    FormatWorksheet --> End([结束])
```

### 3.3 显示任务数据流程

```mermaid
flowchart TD
    Start([开始]) --> ShowTasksData[显示任务数据]
    
    ShowTasksData --> GetAllTasks[获取所有任务]
    GetAllTasks --> CreateWorksheet[创建测试工作表]
    
    CreateWorksheet --> SetupHeaders[设置表头]
    SetupHeaders --> DisplayTaskData[显示任务数据]
    
    DisplayTaskData --> FormatWorksheet[格式化工作表]
    FormatWorksheet --> End([结束])
```

## 4. 函数说明

### 4.1 公共函数

| 函数名 | 返回类型 | 参数 | 说明 |
|--------|----------|------|------|
| ShowDebugTools | 无 | 无 | 显示调试工具菜单，提供各种调试功能选项 |

### 4.2 私有函数

| 函数名 | 返回类型 | 参数 | 说明 |
|--------|----------|------|------|
| ChangeDebugLevel | 无 | 无 | 切换调试级别，允许用户选择不同的日志详细程度 |
| TestTimelineGeneration | 无 | 无 | 测试时间轴生成功能，创建工作表显示时间轴计算结果 |
| ShowProjectInfo | 无 | 无 | 显示项目信息，包括项目名称、经理、日期等 |
| ShowTasksData | 无 | 无 | 显示任务数据，创建工作表列出所有任务的详细信息 |

## 5. 实现示例

### 5.1 调试工具菜单

```vba
' 显示调试工具菜单
Public Sub ShowDebugTools()
    On Error Resume Next

    ' 创建用户表单（如果有表单功能，可以使用表单）
    ' 这里使用简单的消息框菜单
    Dim choice As Integer

    choice = MsgBox("调试工具菜单" & vbCrLf & vbCrLf & _
                    "1. 查看日志文件" & vbCrLf & _
                    "2. 查看日志工作表" & vbCrLf & _
                    "3. 清除日志" & vbCrLf & _
                    "4. 导出日志" & vbCrLf & _
                    "5. 切换调试级别" & vbCrLf & _
                    "6. 测试时间轴生成" & vbCrLf & _
                    "7. 显示项目信息" & vbCrLf & _
                    "8. 显示任务数据" & vbCrLf & vbCrLf & _
                    "请选择操作：", vbQuestion + vbYesNo + vbDefaultButton1, "调试工具")

    If choice = vbYes Then
        ' 显示选项对话框
        Dim optionNum As Integer
        optionNum = CInt(InputBox("请输入选项编号 (1-8)：", "调试工具", "1"))

        Select Case optionNum
            Case 1 ' 查看日志文件
                modDebug.OpenLogFile

            Case 2 ' 查看日志工作表
                modDebug.ShowLog

            Case 3 ' 清除日志
                If MsgBox("确定要清除日志吗？", vbQuestion + vbYesNo, "清除日志") = vbYes Then
                    modDebug.ClearLog
                    MsgBox "日志已清除", vbInformation, "调试工具"
                End If

            Case 4 ' 导出日志
                modDebug.ExportLogToFile

            Case 5 ' 切换调试级别
                ChangeDebugLevel

            Case 6 ' 测试时间轴生成
                TestTimelineGeneration

            Case 7 ' 显示项目信息
                ShowProjectInfo

            Case 8 ' 显示任务数据
                ShowTasksData

            Case Else
                MsgBox "无效的选项", vbExclamation, "调试工具"
        End Select
    End If
End Sub
```

### 5.2 测试时间轴生成

```vba
' 测试时间轴生成
Private Sub TestTimelineGeneration()
    On Error GoTo ErrorHandler

    ' 记录测试开始
    modDebug.LogInfo "开始测试时间轴生成", "modDebugTools.TestTimelineGeneration"

    ' 获取项目信息
    Dim projectInfo As Dictionary
    Set projectInfo = GetProjectInfo()

    ' 检查项目信息
    If projectInfo Is Nothing Then
        MsgBox "无法获取项目信息", vbExclamation, "测试时间轴生成"
        modDebug.LogError 0, "无法获取项目信息", "modDebugTools.TestTimelineGeneration"
        Exit Sub
    End If

    ' 检查日期
    If Not projectInfo.Exists("StartDate") Or Not projectInfo.Exists("EndDate") Then
        MsgBox "项目开始日期或结束日期不存在", vbExclamation, "测试时间轴生成"
        modDebug.LogError 0, "项目开始日期或结束日期不存在", "modDebugTools.TestTimelineGeneration"
        Exit Sub
    End If

    ' 创建测试工作表并显示时间轴计算结果
    ' ...

    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modDebugTools.TestTimelineGeneration"
    MsgBox "测试时间轴生成时出错: " & Err.Description, vbExclamation, "测试时间轴生成"
End Sub
```

## 6. 错误处理

模块中的所有函数都应包含错误处理代码：

```vba
Private Sub ShowTasksData()
    On Error GoTo ErrorHandler
    
    ' 函数主体代码
    ' ...
    
    Exit Sub
    
ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modDebugTools.ShowTasksData"
    MsgBox "显示任务数据时出错: " & Err.Description, vbExclamation, "任务数据"
End Sub
```

## 7. 依赖关系

modDebugTools模块的依赖关系：

```mermaid
flowchart TD
    modDebugTools --> modDebug[modDebug]
    modDebugTools --> modData[modData]
    modDebugTools --> modGantt[modGantt]
    modDebugTools --> Excel[Excel对象模型]
    modDebugTools --> VBA[VBA标准库]
```

## 8. 用户界面

modDebugTools模块使用简单的消息框和输入框作为用户界面，提供以下交互功能：

1. **主菜单**：显示所有可用的调试工具选项
2. **选项输入**：允许用户输入选项编号
3. **确认对话框**：在执行某些操作前请求用户确认
4. **结果显示**：在消息框或工作表中显示操作结果

在未来的版本中，可以考虑使用自定义用户表单来提供更丰富的用户界面体验。

## 9. 未来扩展

modDebugTools模块的未来扩展可能包括：

1. **自定义用户表单**：使用表单替代简单的消息框，提供更好的用户体验
2. **更多测试工具**：添加更多的测试和诊断工具
3. **性能分析**：添加代码性能分析功能
4. **配置测试**：添加配置验证和测试功能
5. **数据导入/导出测试**：添加数据导入和导出功能的测试工具
