ConfigID	ConfigName	ConfigValue	Description	Module	Category	IsEnabled
UI001	ProjectNameFont	Barlow	项目名称字体(可使用任何系统安装的字体名称) 示例: Arial	UI	Font	TRUE
UI002	ProjectNameFontSize	18	项目名称字号(正整数，单位为磅) 示例: 16磅	UI	Font	TRUE
UI003	ProjectNameFontBold	True	项目名称是否粗体(True=粗体，False=常规) 示例: 粗体	UI	Font	TRUE
UI004	ProjectNameFontColor	#D3E7E3	项目名称字体颜色(十六进制RGB颜色代码，如#000000=黑色)	UI	Color	TRUE
UI005	ProjectNameBackColor	#EB5A50	项目名称背景颜色(十六进制RGB颜色代码，如#FFFFFF=白色)	UI	Color	TRUE
UI006	ProjectNameBorderStyle	none	项目名称边框样式(none=无边框/all=所有边框/outline=外边框/bottom=底部边框)	UI	Style	TRUE
UI007	ProjectNameRowHeight	auto	项目名称行高(auto=自动适应/数字=指定高度，单位为磅)	UI	Layout	TRUE
UI059	ProjectNameTextAlign	left	项目名称文本对齐方式(left=左对齐/center=居中/right=右对齐)	UI	Layout	TRUE
UI008	ProjectManagerFont	Barlow	项目经理信息字体(可使用任何系统安装的字体名称) 示例: Arial	UI	Font	TRUE
UI009	ProjectManagerFontSize	11	项目经理信息字号(正整数，单位为磅) 示例: 12磅	UI	Font	TRUE
UI010	ProjectManagerFontBold	False	项目经理信息是否粗体(True=粗体，False=常规) 示例: 常规	UI	Font	TRUE
UI011	ProjectManagerFontColor	#D3E7E3	项目经理信息字体颜色(十六进制RGB颜色代码，如#000000=黑色)	UI	Color	TRUE
UI012	ProjectManagerBackColor	#EB5A50	项目经理信息背景颜色(十六进制RGB颜色代码，如#FFFFFF=白色)	UI	Color	TRUE
UI013	ProjectManagerBorderStyle	none	项目经理信息边框样式(none=无边框/all=所有边框/outline=外边框/bottom=底部边框)	UI	Style	TRUE
UI014	ProjectManagerRowHeight	auto	项目经理信息行高(auto=自动适应/数字=指定高度，单位为磅)	UI	Layout	TRUE
UI060	ProjectManagerTextAlign	left	项目经理信息文本对齐方式(left=左对齐/center=居中/right=右对齐)	UI	Layout	TRUE
UI015	SupplementInfoFont	Barlow	补充信息字体(可使用任何系统安装的字体名称) 示例: Arial	UI	Font	TRUE
UI016	SupplementInfoFontSize	9	补充信息字号(正整数，单位为磅) 示例: 9磅	UI	Font	TRUE
UI017	SupplementInfoFontBold	False	补充信息是否粗体(True=粗体，False=常规) 示例: 常规	UI	Font	TRUE
UI018	SupplementInfoFontColor	#FFFFFF	补充信息字体颜色(十六进制RGB颜色代码，如#000000=黑色)	UI	Color	TRUE
UI019	SupplementInfoBackColor	#EB5A50	补充信息背景颜色(十六进制RGB颜色代码，如#FFFFFF=白色)	UI	Color	TRUE
UI020	SupplementInfoBorderStyle	none	补充信息边框样式(none=无边框/all=所有边框/outline=外边框/bottom=底部边框)	UI	Style	TRUE
UI021	SupplementInfoRowHeight	auto	补充信息行高(auto=自动适应/数字=指定高度，单位为磅)	UI	Layout	TRUE
UI022	TimelineYearFont	Arial	时间轴年份字体(可使用任何系统安装的字体名称) 示例: Arial	UI	Font	TRUE
UI023	TimelineYearFontSize	11	时间轴年份字号(正整数，单位为磅) 示例: 11磅	UI	Font	TRUE
UI024	TimelineYearFontBold	True	时间轴年份是否粗体(True=粗体，False=常规) 示例: 粗体	UI	Font	TRUE
UI025	TimelineYearFontColor	#FFFFFF	时间轴年份字体颜色(十六进制RGB颜色代码，如#FFFFFF=白色)	UI	Color	TRUE
UI026	TimelineYearBackColor	#EB5A50	时间轴年份背景颜色(十六进制RGB颜色代码，如#4472C4=蓝色)	UI	Color	TRUE
UI027	TimelineYearBorderStyle	all	时间轴年份边框样式(none=无边框/all=所有边框/outline=外边框/bottom=底部边框)	UI	Style	TRUE
UI028	TimelineYearRowHeight	auto	时间轴年份行高(auto=自动适应/数字=指定高度，单位为磅)	UI	Layout	TRUE
UI029	TimelineMonthFont	Arial	时间轴月份字体(可使用任何系统安装的字体名称) 示例: Arial	UI	Font	TRUE
UI030	TimelineMonthFontSize	10	时间轴月份字号(正整数，单位为磅) 示例: 10磅	UI	Font	TRUE
UI031	TimelineMonthFontBold	True	时间轴月份是否粗体(True=粗体，False=常规) 示例: 粗体	UI	Font	TRUE
UI032	TimelineMonthFontColor	#FFFFFF	时间轴月份字体颜色(十六进制RGB颜色代码，如#FFFFFF=白色)	UI	Color	TRUE
UI033	TimelineMonthBackColor	#EB5A50	时间轴月份背景颜色(十六进制RGB颜色代码，如#5B9BD5=浅蓝色)	UI	Color	TRUE
UI034	TimelineMonthBorderStyle	all	时间轴月份边框样式(none=无边框/all=所有边框/outline=外边框/bottom=底部边框)	UI	Style	TRUE
UI035	TimelineMonthRowHeight	auto	时间轴月份行高(auto=自动适应/数字=指定高度，单位为磅)	UI	Layout	TRUE
UI036	TimelineWeekFont	Arial	时间轴周数字体(可使用任何系统安装的字体名称) 示例: Arial	UI	Font	TRUE
UI037	TimelineWeekFontSize	9	时间轴周数字号(正整数，单位为磅) 示例: 9磅	UI	Font	TRUE
UI038	TimelineWeekFontBold	False	时间轴周数是否粗体(True=粗体，False=常规) 示例: 常规	UI	Font	TRUE
UI039	TimelineWeekFontColor	#FFFFFF	时间轴周数字体颜色(十六进制RGB颜色代码，如#FFFFFF=白色)	UI	Color	TRUE
UI040	TimelineWeekBackColor	#EB5A50	时间轴周数背景颜色(十六进制RGB颜色代码，如#70AD47=绿色)	UI	Color	TRUE
UI041	TimelineWeekBorderStyle	all	时间轴周数边框样式(none=无边框/all=所有边框/outline=外边框/bottom=底部边框)	UI	Style	TRUE
UI042	TimelineWeekRowHeight	auto	时间轴周数行高(auto=自动适应/数字=指定高度，单位为磅)	UI	Layout	TRUE
UI043	CategoryFont	Barlow	任务类别字体(可使用任何系统安装的字体名称) 示例: Barlow	UI	Font	TRUE
UI044	CategoryFontSize	11	任务类别字号(正整数，单位为磅) 示例: 11磅	UI	Font	TRUE
UI045	CategoryFontBold	True	任务类别是否粗体(True=粗体，False=常规) 示例: 粗体	UI	Font	TRUE
UI046	CategoryFontColor	#000000	任务类别字体颜色(十六进制RGB颜色代码，如#000000=黑色)	UI	Color	TRUE
UI047	CategoryBackColor	#F2F2F2	任务类别背景颜色(十六进制RGB颜色代码，如#F2F2F2=浅灰色)	UI	Color	TRUE
UI048	CategoryBorderStyle	bottom	任务类别边框样式(none=无边框/all=所有边框/outline=外边框/bottom=底部边框)	UI	Style	TRUE
UI049	CategoryWrapText	True	任务类别文本是否自动换行(True=自动换行，False=不换行)	UI	Layout	TRUE
UI050	CategoryColumnWidth	30	任务类别列宽(正整数，单位为标准列宽)	UI	Layout	TRUE
UI058	CategoryTextAlign	center	任务类别文本对齐方式(left=左对齐/center=居中/right=右对齐)	UI	Layout	TRUE
UI051	ColumnAWidth	2	A列宽度(正整数，单位为标准列宽)	UI	Layout	TRUE
UI061	EnableLogo	True	是否在甘特图中显示logo(True=显示，False=不显示)	UI	Feature	TRUE
UI062	LogoMargin	2	Logo与项目名称区域上下边界的间距(正整数，单位为像素)	UI	Layout	TRUE
UI052	ChartTheme	custom	甘特图主题(1-5=预设主题/custom=自定义主题)	UI	Theme	TRUE
UI053	ChartGridlinesArea	all	网格线应用区域(all=全部区域/header=表头/ganttDrawing=甘特图绘图区/taskcategory=任务类别区/header,ganttDrawing=表头和甘特图绘图区/header,taskcategory=表头和任务类别区/ganttDrawing,taskcategory=甘特图绘图区和任务类别区)	UI	Layout	TRUE
UI054	ChartGridlinesType	all	网格线类型(all=所有网格线/horizontal=水平线/vertical=垂直线)	UI	Layout	TRUE
UI055	ChartGridlineColor	#DDDDDD	网格线颜色(十六进制RGB颜色代码，如#DDDDDD=浅灰色)	UI	Color	TRUE
UI056	ChartBackgroundColor	#FFFFFF	甘特图背景颜色(十六进制RGB颜色代码，如#FFFFFF=白色)	UI	Color	TRUE
UI057	ChartAlternateRowColor	#F5F5F5	甘特图交替行颜色(十六进制RGB颜色代码，如#F5F5F5=浅灰色)	UI	Color	TRUE
GT001	CellWidthFactor	0.8	甘特图时间单元格宽度放大系数(大于0的小数，1.0表示标准宽度)	Gantt	Layout	TRUE
GT002	DefaultTaskColor	#3366CC	默认任务条颜色(十六进制RGB颜色代码，如#3366CC=蓝色)	Gantt	Color	TRUE
GT003	DefaultMilestoneColor	#FF9900	默认里程碑颜色(十六进制RGB颜色代码，如#FF9900=橙色)	Gantt	Color	TRUE
GT033	DefaultColorG	#00FF00	颜色代码G对应的颜色值(十六进制RGB颜色代码，绿色)	Gantt	Color	TRUE
GT034	DefaultColorY	#FFFF00	颜色代码Y对应的颜色值(十六进制RGB颜色代码，黄色)	Gantt	Color	TRUE
GT035	DefaultColorR	#FF0000	颜色代码R对应的颜色值(十六进制RGB颜色代码，红色)	Gantt	Color	TRUE
GT036	DefaultColorS	#800080	颜色代码S对应的颜色值(十六进制RGB颜色代码，紫色)	Gantt	Color	TRUE
GT004	TaskProgressColor	#66CC66	任务进度颜色(十六进制RGB颜色代码，如#66CC66=绿色)	Gantt	Color	TRUE
GT005	CurrentDateLineColor	#41B7AC	当前日期线颜色(十六进制RGB颜色代码，如#FF0000=红色)	Gantt	Color	TRUE
GT006	CurrentDateLineStyle	2	当前日期线样式(1=实线，2=虚线)	Gantt	Style	TRUE
GT018	EnableCurrentDateLine	False	是否启用当前日期线(True=启用，False=禁用)	Gantt	Feature	TRUE
GT019	CurrentDateLineWeight	0.8	当前日期线宽度(大于0的小数，单位为磅)	Gantt	Style	TRUE
GT007	DefaultTaskPosition	next	默认任务位置(next=下一行/same=同一行/数字=指定行号)	Gantt	Layout	TRUE
GT008	DefaultTaskTextPosition	right	默认任务文字位置(left=左侧/right=右侧/top=顶部/bottom=底部/inside=内部)	Gantt	Layout	TRUE
GT009	DefaultMilestoneTextPosition	right	默认里程碑文字位置(left=左侧/right=右侧/top=顶部/bottom=底部)	Gantt	Layout	TRUE
GT010	BaselineColor	#FF0000	基准线颜色(十六进制RGB颜色代码，如#FF0000=红色)	Gantt	Color	TRUE
GT011	BaselineStyle	2	基准线样式(1=实线，2=虚线)	Gantt	Style	TRUE
GT012	BaselineWeight	0.8	基准线宽度(大于0的小数，单位为磅)	Gantt	Style	TRUE
GT013	TaskBarHeight	11	任务条高度/里程碑大小(正整数，单位为像素)	Gantt	Layout	TRUE
GT014	TaskBarBorderWidth	0	任务条/里程碑边框宽度(0=无边框，正整数表示边框宽度，单位为磅)	Gantt	Style	TRUE
GT015	ProgressBarColor	#66CC66	进度条颜色(十六进制RGB颜色代码，如#66CC66=绿色)	Gantt	Color	TRUE
GT016	LabelDistance	5	标签与任务条/里程碑的距离(正整数，单位为像素)	Gantt	Layout	TRUE
GT017	RowPadding	3	行高上下预留空隙(正整数，单位为像素)	Gantt	Layout	TRUE
GT020	MilestoneShapeStyle	1	里程碑图形样式(1=菱形/2=旗子/3=折角/4=等腰三角形/5=椭圆形/6=五角星/7=太阳/8=水滴/9=笑脸/10=月亮/11=雪佛龙/12=闪电/13=等边三角形)	Gantt	Style	TRUE
GT021	TaskBarShapeStyle	1	任务条图形样式(1=圆角矩形/2=矩形/3=流程图处理)	Gantt	Style	TRUE
GT022	EnableSpotlight	True	是否启用聚光灯效果(True=启用，False=禁用)	Gantt	Feature	TRUE
GT023	SpotlightMode	all	聚光灯模式(all=水平和垂直/horizontal=仅水平/vertical=仅垂直)	Gantt	Feature	TRUE
GT024	SpotlightColor	#E6F2FF	聚光灯颜色(十六进制RGB颜色代码，如#E6F2FF=浅蓝色)	Gantt	Color	TRUE
GT025	EnableGanttBorder	True	是否显示甘特图外边框(True=显示，False=不显示)	Gantt	Border	TRUE
GT026	GanttBorderColor	#D3D3D3	甘特图外边框颜色(十六进制RGB颜色代码，如#D3D3D3=浅灰色)	Gantt	Border	TRUE
GT027	GanttBorderWeight	1	甘特图外边框粗细(1=细/2=中/3=粗/4=极粗)	Gantt	Border	TRUE
GT028	GanttBorderStyle	1	甘特图外边框线型(1=实线/2=虚线/3=点线/4=点划线/5=双点划线)	Gantt	Border	TRUE
GT029	TaskLabelFont	Arial	任务标签字体(可使用任何系统安装的字体名称) 示例: Arial	Gantt	Font	TRUE
GT030	TaskLabelFontSize	8	任务标签字号(正整数，单位为磅) 示例: 8磅	Gantt	Font	TRUE
GT031	MilestoneLabelFont	Arial	里程碑标签字体(可使用任何系统安装的字体名称) 示例: Arial	Gantt	Font	TRUE
GT032	MilestoneLabelFontSize	8	里程碑标签字号(正整数，单位为磅) 示例: 8磅	Gantt	Font	TRUE

DT001	AutoCalculateDuration	True	是否自动计算任务持续时间(True=自动计算，False=使用输入值)	Data	Calculation	TRUE
DT002	ExcludeWeekends	True	计算持续时间时是否排除周末(True=排除周末，False=包含周末)	Data	Calculation	TRUE
DT003	DefaultTaskProgress	0	默认任务进度(0-100的整数，表示完成百分比)	Data	Default	TRUE
DB001	EnableDebug	FALSE	是否开启debug模式(True=开启，False=关闭)	Debug	Default	FALSE
DB002	DebugLevel	4	调试级别(1=错误,2=警告,3=信息,4=详细)	Debug	Default	FALSE
DB003	EnableFileLogging	FALSE	是否启用文件日志(True=启用，False=禁用)	Debug	Default	FALSE
DB004	EnableImmediateOutput	False	是否输出到即时窗口(True=输出，False=不输出)	Debug	Default	FALSE
DB005	UTF8Encoding	True	是否使用UTF-8编码日志文件(True=使用UTF-8，False=使用默认编码)	Debug	Default	TRUE
