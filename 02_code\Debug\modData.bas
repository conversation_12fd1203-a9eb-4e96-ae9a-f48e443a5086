'Attribute VB_Name = "modData"
Option Explicit

' =========================================================
' Module: modData
' Description: Responsible for data processing, validation and management
' =========================================================

' ---------------------------------------------------------
' Data Validation Functions
' ---------------------------------------------------------

' Validate all data
Public Function ValidateAllData() As Boolean
    On Error GoTo ErrorHandler

    ' 用于收集所有错误的变量
    Dim allErrorMessages As New Collection
    Dim allErrorCells As New Collection
    Dim totalErrorCount As Long
    Dim projectInfoErrors As Long
    Dim tasksDataErrors As Long
    Dim isValid As Boolean

    ' 清除所有工作表上的验证标记
    ClearAllValidationMarks

    isValid = True
    totalErrorCount = 0

    ' 记录函数进入
    modDebug.LogFunctionEntry "modData.ValidateAllData"

    ' 1. 验证项目信息
    modDebug.LogInfo "开始验证项目信息", "modData.ValidateAllData"
    Dim projectInfoErrorMessages As Collection
    Dim projectInfoErrorCells As Collection

    ' 验证项目信息，但不显示错误消息
    If Not ValidateProjectInfo(projectInfoErrorMessages, projectInfoErrorCells, projectInfoErrors) Then
        isValid = False
        modDebug.LogWarning "项目信息验证失败，发现 " & projectInfoErrors & " 个错误", "modData.ValidateAllData"

        ' 添加项目信息错误到总错误集合
        Dim i As Long
        For i = 1 To projectInfoErrorMessages.Count
            totalErrorCount = totalErrorCount + 1
            allErrorMessages.Add totalErrorCount & ". [项目信息] " & Mid(projectInfoErrorMessages(i), InStr(projectInfoErrorMessages(i), ".") + 1)
            allErrorCells.Add projectInfoErrorCells(i)
        Next i
    Else
        modDebug.LogInfo "项目信息验证通过", "modData.ValidateAllData"
    End If

    ' 2. 验证任务和里程碑数据
    modDebug.LogInfo "开始验证任务和里程碑数据", "modData.ValidateAllData"
    Dim tasksErrorMessages As Collection
    Dim tasksErrorCells As Collection

    ' 验证任务数据，但不显示错误消息
    If Not ValidateTasksData(tasksErrorMessages, tasksErrorCells, tasksDataErrors) Then
        isValid = False
        modDebug.LogWarning "任务和里程碑数据验证失败，发现 " & tasksDataErrors & " 个错误", "modData.ValidateAllData"

        ' 添加任务数据错误到总错误集合
        Dim j As Long
        For j = 1 To tasksErrorMessages.Count
            totalErrorCount = totalErrorCount + 1
            allErrorMessages.Add totalErrorCount & ". [任务数据] " & Mid(tasksErrorMessages(j), InStr(tasksErrorMessages(j), ".") + 1)
            allErrorCells.Add tasksErrorCells(j)
        Next j
    Else
        modDebug.LogInfo "任务和里程碑数据验证通过", "modData.ValidateAllData"
    End If

    ' 显示汇总错误消息
    If totalErrorCount > 0 Then
        Dim errorMsg As String, k As Long
        errorMsg = "发现 " & totalErrorCount & " 个数据验证错误：" & vbCrLf & vbCrLf

        For k = 1 To allErrorMessages.Count
            errorMsg = errorMsg & allErrorMessages(k) & vbCrLf
        Next k

        errorMsg = errorMsg & vbCrLf & "请修正标记为红色的单元格。"

        MsgBox errorMsg, vbExclamation, "数据验证错误"

        ' 选择第一个错误单元格
        If allErrorCells.Count > 0 Then
            Dim cellAddress As String
            cellAddress = allErrorCells(1)

            ' 确定单元格所在的工作表
            Dim cellSheet As String
            Dim cellRef As String

            ' 解析单元格地址，获取工作表名称和单元格引用
            If InStr(cellAddress, "!") > 0 Then
                ' 地址包含工作表名称
                cellSheet = Left(cellAddress, InStr(cellAddress, "!") - 1)
                cellRef = Mid(cellAddress, InStr(cellAddress, "!") + 1)

                ' 如果工作表名称带引号，去掉引号
                If Left(cellSheet, 1) = "'" Then
                    cellSheet = Mid(cellSheet, 2, Len(cellSheet) - 2)
                End If
            Else
                ' 地址不包含工作表名称，假设是当前工作表
                cellSheet = ActiveSheet.Name
                cellRef = cellAddress
            End If

            ' 激活包含错误的工作表
            ThisWorkbook.Worksheets(cellSheet).Activate

            ' 选择错误单元格
            Range(cellRef).Select
        End If
    End If

    ' 返回最终结果
    ValidateAllData = isValid

    ' 记录函数退出
    modDebug.LogFunctionExit "modData.ValidateAllData", "结果: " & isValid & ", 总错误数: " & totalErrorCount
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modData.ValidateAllData"
    ValidateAllData = False
    modDebug.LogFunctionExit "modData.ValidateAllData", "错误: " & Err.Description
End Function

' 清除所有工作表上的验证标记
Private Sub ClearAllValidationMarks()
    On Error Resume Next

    ' 清除项目信息工作表上的验证标记
    ClearValidationMarks ThisWorkbook.Worksheets("ProjectInfo").UsedRange

    ' 清除任务工作表上的验证标记
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("Milestones&WBS")

    ' 如果任务表存在，清除其数据区域的验证标记
    Dim tbl As ListObject
    On Error Resume Next
    Set tbl = ws.ListObjects("taskTable")
    If Not tbl Is Nothing Then
        If Not tbl.DataBodyRange Is Nothing Then
            ClearValidationMarks tbl.DataBodyRange
        End If
    End If

    ' 清除整个工作表的验证标记（以防有些标记在表格外）
    ClearValidationMarks ws.UsedRange

    On Error GoTo 0
End Sub

' Validate project information
Private Function ValidateProjectInfo(ByRef outErrorMessages As Collection, ByRef outErrorCells As Collection, ByRef outErrorCount As Long) As Boolean
    On Error GoTo ErrorHandler

    Dim projectName As String
    Dim projectManager As String
    Dim startDate As Date
    Dim endDate As Date
    Dim ws As Worksheet
    Dim errorCount As Long

    ' 初始化输出集合
    Set outErrorMessages = New Collection
    Set outErrorCells = New Collection
    outErrorCount = 0

    ' 获取工作表引用
    Set ws = ThisWorkbook.Worksheets("ProjectInfo")

    ' 注意：验证标记已在ValidateAllData函数中清除

    errorCount = 0

    ' Use named ranges to get project information
    projectName = Range("projectName").Value
    projectManager = Range("projectManager").Value

    ' Check if project name is empty
    If Trim(projectName) = "" Then
        errorCount = errorCount + 1
        MarkErrorCell Range("projectName"), errorCount
        outErrorMessages.Add errorCount & ". 项目名称不能为空。"
        outErrorCells.Add Range("projectName").Address
    End If

    ' Check if project manager is empty
    If Trim(projectManager) = "" Then
        errorCount = errorCount + 1
        MarkErrorCell Range("projectManager"), errorCount
        outErrorMessages.Add errorCount & ". 项目经理不能为空。"
        outErrorCells.Add Range("projectManager").Address
    End If

    ' Check date validity
    On Error Resume Next
    startDate = Range("projectStartDate").Value
    endDate = Range("projectEndDate").Value
    On Error GoTo ErrorHandler

    ' Check if start date is valid
    If startDate = 0 Then
        errorCount = errorCount + 1
        MarkErrorCell Range("projectStartDate"), errorCount
        outErrorMessages.Add errorCount & ". 项目开始日期无效。"
        outErrorCells.Add Range("projectStartDate").Address
    End If

    ' Check if end date is valid
    If endDate = 0 Then
        errorCount = errorCount + 1
        MarkErrorCell Range("projectEndDate"), errorCount
        outErrorMessages.Add errorCount & ". 项目结束日期无效。"
        outErrorCells.Add Range("projectEndDate").Address
    End If

    ' Check date relationship
    If startDate > endDate And startDate <> 0 And endDate <> 0 Then
        errorCount = errorCount + 1
        MarkErrorCell Range("projectEndDate"), errorCount
        outErrorMessages.Add errorCount & ". 项目开始日期不能晚于结束日期。"
        outErrorCells.Add Range("projectEndDate").Address
    End If

    ' 设置输出错误计数
    outErrorCount = errorCount

    ' 返回验证结果
    ValidateProjectInfo = (errorCount = 0)

    Exit Function

ErrorHandler:
    modUtilities.LogError Err.Number, Err.Description, "modData.ValidateProjectInfo"
    ValidateProjectInfo = False
End Function

' Validate tasks/milestones data
Private Function ValidateTasksData(ByRef outErrorMessages As Collection, ByRef outErrorCells As Collection, ByRef outErrorCount As Long) As Boolean
    On Error GoTo ErrorHandler

    Dim tbl As ListObject
    Dim dataRange As Range
    Dim i As Long
    Dim taskType As String
    Dim startDate As Date
    Dim endDate As Date
    Dim progress As Double
    Dim position As Variant
    Dim textPosition As String
    Dim ws As Worksheet
    Dim errorCount As Long

    ' 初始化输出集合
    Set outErrorMessages = New Collection
    Set outErrorCells = New Collection
    outErrorCount = 0

    ' 获取工作表引用
    Set ws = ThisWorkbook.Worksheets("Milestones&WBS")

    ' Get project start and end dates for validation
    Dim projectStartDate As Date
    Dim projectEndDate As Date
    projectStartDate = Range("projectStartDate").Value
    projectEndDate = Range("projectEndDate").Value

    ' Get task table
    Set tbl = ws.ListObjects("taskTable")

    ' Check if there is task data
    If tbl.DataBodyRange Is Nothing Then
        errorCount = errorCount + 1
        outErrorMessages.Add errorCount & ". 未找到任务/里程碑数据。"
        outErrorCells.Add tbl.Range.Address
        outErrorCount = errorCount
        ValidateTasksData = False
        Exit Function
    End If

    Set dataRange = tbl.DataBodyRange

    ' 注意：验证标记已在ValidateAllData函数中清除

    errorCount = 0

    ' Loop through each row of task data
    For i = 1 To dataRange.Rows.Count
        ' 获取实际行号（用于错误消息）
        Dim actualRow As Long
        actualRow = dataRange.Cells(i, 1).Row

        ' Check required fields

        ' Category is now optional - removed validation check

        ' Check Description
        If IsEmpty(dataRange.Cells(i, tbl.ListColumns("Description").Index).Value) Then
            errorCount = errorCount + 1
            MarkErrorCell dataRange.Cells(i, tbl.ListColumns("Description").Index), errorCount
            outErrorMessages.Add errorCount & ". 第 " & actualRow & " 行的描述不能为空。"
            outErrorCells.Add dataRange.Cells(i, tbl.ListColumns("Description").Index).Address
        End If

        ' Check Type
        taskType = dataRange.Cells(i, tbl.ListColumns("Type").Index).Value
        If taskType <> "A" And taskType <> "M" Then
            errorCount = errorCount + 1
            MarkErrorCell dataRange.Cells(i, tbl.ListColumns("Type").Index), errorCount
            outErrorMessages.Add errorCount & ". 第 " & actualRow & " 行的类型必须是 'A' 或 'M'。"
            outErrorCells.Add dataRange.Cells(i, tbl.ListColumns("Type").Index).Address
        End If

        ' Check Start Date
        startDate = dataRange.Cells(i, tbl.ListColumns("Start Date").Index).Value
        If startDate = 0 Then
            errorCount = errorCount + 1
            MarkErrorCell dataRange.Cells(i, tbl.ListColumns("Start Date").Index), errorCount
            outErrorMessages.Add errorCount & ". 第 " & actualRow & " 行的开始日期无效。"
            outErrorCells.Add dataRange.Cells(i, tbl.ListColumns("Start Date").Index).Address
        Else
            ' Check if Start Date is within project date range (for both tasks and milestones)
            If startDate < projectStartDate Then
                errorCount = errorCount + 1
                MarkErrorCell dataRange.Cells(i, tbl.ListColumns("Start Date").Index), errorCount
                outErrorMessages.Add errorCount & ". 第 " & actualRow & " 行的开始日期 (" & Format(startDate, "yyyy-mm-dd") & ") 早于项目开始日期 (" & Format(projectStartDate, "yyyy-mm-dd") & ")。"
                outErrorCells.Add dataRange.Cells(i, tbl.ListColumns("Start Date").Index).Address
            End If

            If startDate > projectEndDate Then
                errorCount = errorCount + 1
                MarkErrorCell dataRange.Cells(i, tbl.ListColumns("Start Date").Index), errorCount
                outErrorMessages.Add errorCount & ". 第 " & actualRow & " 行的开始日期 (" & Format(startDate, "yyyy-mm-dd") & ") 晚于项目结束日期 (" & Format(projectEndDate, "yyyy-mm-dd") & ")。"
                outErrorCells.Add dataRange.Cells(i, tbl.ListColumns("Start Date").Index).Address
            End If
        End If

        ' For milestones (Type = "M"), the date must be within project range (already checked above)

        ' Check End Date (for task type 'A')
        If taskType = "A" Then
            endDate = dataRange.Cells(i, tbl.ListColumns("End Date").Index).Value
            If endDate = 0 Then
                errorCount = errorCount + 1
                MarkErrorCell dataRange.Cells(i, tbl.ListColumns("End Date").Index), errorCount
                outErrorMessages.Add errorCount & ". 第 " & actualRow & " 行的结束日期无效。"
                outErrorCells.Add dataRange.Cells(i, tbl.ListColumns("End Date").Index).Address
            Else
                If startDate > endDate And startDate <> 0 Then
                    errorCount = errorCount + 1
                    MarkErrorCell dataRange.Cells(i, tbl.ListColumns("End Date").Index), errorCount
                    outErrorMessages.Add errorCount & ". 第 " & actualRow & " 行的开始日期不能晚于结束日期。"
                    outErrorCells.Add dataRange.Cells(i, tbl.ListColumns("End Date").Index).Address
                End If

                ' Check if End Date is within project date range
                If endDate < projectStartDate Then
                    errorCount = errorCount + 1
                    MarkErrorCell dataRange.Cells(i, tbl.ListColumns("End Date").Index), errorCount
                    outErrorMessages.Add errorCount & ". 第 " & actualRow & " 行的结束日期 (" & Format(endDate, "yyyy-mm-dd") & ") 早于项目开始日期 (" & Format(projectStartDate, "yyyy-mm-dd") & ")。"
                    outErrorCells.Add dataRange.Cells(i, tbl.ListColumns("End Date").Index).Address
                End If

                If endDate > projectEndDate Then
                    errorCount = errorCount + 1
                    MarkErrorCell dataRange.Cells(i, tbl.ListColumns("End Date").Index), errorCount
                    outErrorMessages.Add errorCount & ". 第 " & actualRow & " 行的结束日期 (" & Format(endDate, "yyyy-mm-dd") & ") 晚于项目结束日期 (" & Format(projectEndDate, "yyyy-mm-dd") & ")。"
                    outErrorCells.Add dataRange.Cells(i, tbl.ListColumns("End Date").Index).Address
                End If
            End If
        ElseIf taskType = "M" Then
            ' For milestones, explicitly set endDate equal to startDate for clarity
            endDate = startDate
            ' Note: We don't need to check if the milestone date is within project range again
            ' as we already checked the startDate above
        End If

        ' Check Progress (if has value)
        If Not IsEmpty(dataRange.Cells(i, tbl.ListColumns("Progress").Index).Value) Then
            progress = dataRange.Cells(i, tbl.ListColumns("Progress").Index).Value
            If progress < 0 Or progress > 1 Then
                errorCount = errorCount + 1
                MarkErrorCell dataRange.Cells(i, tbl.ListColumns("Progress").Index), errorCount
                outErrorMessages.Add errorCount & ". 第 " & actualRow & " 行的进度必须在0-100%之间。"
                outErrorCells.Add dataRange.Cells(i, tbl.ListColumns("Progress").Index).Address
            End If
        End If

        ' Check Position (if has value)
        If Not IsEmpty(dataRange.Cells(i, tbl.ListColumns("Position").Index).Value) Then
            position = dataRange.Cells(i, tbl.ListColumns("Position").Index).Value
            If TypeName(position) = "String" Then
                If position <> "same" And position <> "next" Then
                    errorCount = errorCount + 1
                    MarkErrorCell dataRange.Cells(i, tbl.ListColumns("Position").Index), errorCount
                    outErrorMessages.Add errorCount & ". 第 " & actualRow & " 行的位置必须是 'same'、'next' 或有效的整数。"
                    outErrorCells.Add dataRange.Cells(i, tbl.ListColumns("Position").Index).Address
                End If
            ElseIf TypeName(position) = "Double" Then
                If Int(position) <> position Then
                    errorCount = errorCount + 1
                    MarkErrorCell dataRange.Cells(i, tbl.ListColumns("Position").Index), errorCount
                    outErrorMessages.Add errorCount & ". 第 " & actualRow & " 行的位置必须是整数。"
                    outErrorCells.Add dataRange.Cells(i, tbl.ListColumns("Position").Index).Address
                End If

                ' 验证数值不会导致行位置过大（可选）
                If position > 1000 Then ' 假设允许最多向下移动1000行
                    errorCount = errorCount + 1
                    MarkErrorCell dataRange.Cells(i, tbl.ListColumns("Position").Index), errorCount
                    outErrorMessages.Add errorCount & ". 第 " & actualRow & " 行的位置值过大。最大允许值为1000。"
                    outErrorCells.Add dataRange.Cells(i, tbl.ListColumns("Position").Index).Address
                End If
            End If
        End If

        ' Check Text Position (if has value)
        If Not IsEmpty(dataRange.Cells(i, tbl.ListColumns("Text Position").Index).Value) Then
            textPosition = dataRange.Cells(i, tbl.ListColumns("Text Position").Index).Value
            If taskType = "M" Then
                If textPosition <> "left" And textPosition <> "right" And textPosition <> "top" And textPosition <> "bottom" Then
                    errorCount = errorCount + 1
                    MarkErrorCell dataRange.Cells(i, tbl.ListColumns("Text Position").Index), errorCount
                    outErrorMessages.Add errorCount & ". 第 " & actualRow & " 行的里程碑文本位置必须是 'left'、'right'、'top' 或 'bottom'。"
                    outErrorCells.Add dataRange.Cells(i, tbl.ListColumns("Text Position").Index).Address
                End If
            ElseIf taskType = "A" Then
                If textPosition <> "left" And textPosition <> "right" And textPosition <> "top" And textPosition <> "bottom" And textPosition <> "inside" Then
                    errorCount = errorCount + 1
                    MarkErrorCell dataRange.Cells(i, tbl.ListColumns("Text Position").Index), errorCount
                    outErrorMessages.Add errorCount & ". 第 " & actualRow & " 行的任务文本位置必须是 'left'、'right'、'top'、'bottom' 或 'inside'。"
                    outErrorCells.Add dataRange.Cells(i, tbl.ListColumns("Text Position").Index).Address
                End If
            End If
        End If
    Next i

    ' 设置输出错误计数
    outErrorCount = errorCount

    ' 返回验证结果
    ValidateTasksData = (errorCount = 0)

    Exit Function

ErrorHandler:
    modUtilities.LogError Err.Number, Err.Description, "modData.ValidateTasksData"
    ValidateTasksData = False
End Function

' 清除验证标记
Private Sub ClearValidationMarks(dataRange As Range)
    On Error Resume Next

    ' 清除整个数据区域的填充颜色和字体颜色
    With dataRange.Interior
        .Pattern = xlNone
        .TintAndShade = 0
        .PatternTintAndShade = 0
    End With

    With dataRange.Font
        .ColorIndex = xlAutomatic
    End With

    ' 清除单元格注释
    Dim cell As Range
    For Each cell In dataRange
        If Not cell.Comment Is Nothing Then
            cell.Comment.Delete
        End If
    Next cell

    On Error GoTo 0
End Sub

' 标记错误单元格
Private Sub MarkErrorCell(cell As Range, errorNumber As Long)
    On Error Resume Next

    ' 设置红色填充和白色字体
    With cell.Interior
        .Color = RGB(255, 0, 0)
    End With

    With cell.Font
        .Color = RGB(255, 255, 255)
    End With

    ' 不再添加注释，只用颜色标记错误单元格

    On Error GoTo 0
End Sub

' ---------------------------------------------------------
' Data Retrieval Functions
' ---------------------------------------------------------

' Get project information
Public Function GetProjectInfo() As Dictionary
    On Error GoTo ErrorHandler

    Dim projectInfo As New Dictionary

    ' Use named ranges to get project information
    projectInfo.Add "ProjectName", Range("projectName").Value
    projectInfo.Add "ProjectManager", Range("projectManager").Value
    projectInfo.Add "StartDate", Range("projectStartDate").Value
    projectInfo.Add "EndDate", Range("projectEndDate").Value
    projectInfo.Add "Description", Range("projectDescription").Value

    Set GetProjectInfo = projectInfo
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modData.GetProjectInfo"
    Set GetProjectInfo = New Dictionary ' Return empty dictionary
End Function

' Get all tasks/milestones
Public Function GetAllTasks(Optional ByRef outBaselineCollection As Collection = Nothing) As Collection
    On Error GoTo ErrorHandler

    Dim tasks As New Collection
    Dim task As Dictionary
    Dim tbl As ListObject
    Dim dataRange As Range
    Dim i As Long
    Dim baselineDate As Date
    Dim baselineDateStr As String
    Dim baselineInfo As Dictionary
    Dim baselineDates As New Dictionary ' 用于跟踪已处理的基准线日期

    ' 直接获取配置参数
    Dim autoCalculateDuration As Boolean
    Dim excludeWeekends As Boolean
    Dim defaultTaskProgress As Double
    Dim defaultTaskPosition As String
    Dim defaultTaskColor As String
    Dim defaultMilestoneColor As String
    Dim defaultTaskTextPosition As String
    Dim defaultMilestoneTextPosition As String

    ' 获取Data模块配置
    autoCalculateDuration = CBool(GetConfig("AutoCalculateDuration", True))
    excludeWeekends = CBool(GetConfig("ExcludeWeekends", True))
    defaultTaskProgress = CDbl(Val(GetConfig("DefaultTaskProgress", 0)))

    ' 获取Gantt模块配置
    defaultTaskPosition = CStr(GetConfig("DefaultTaskPosition", "next"))
    defaultTaskColor = CStr(GetConfig("DefaultTaskColor", "#3366CC"))
    defaultMilestoneColor = CStr(GetConfig("DefaultMilestoneColor", "#FF9900"))
    defaultTaskTextPosition = CStr(GetConfig("DefaultTaskTextPosition", "right"))
    defaultMilestoneTextPosition = CStr(GetConfig("DefaultMilestoneTextPosition", "right"))

    ' Get ListObject reference
    Set tbl = ThisWorkbook.Worksheets("Milestones&WBS").ListObjects("taskTable")

    ' If table has no data, return empty collection
    If tbl.DataBodyRange Is Nothing Then
        Set GetAllTasks = tasks
        Exit Function
    End If

    ' Get data range
    Set dataRange = tbl.DataBodyRange

    ' 获取列索引
    Dim idColIndex As Long, categoryColIndex As Long, descriptionColIndex As Long
    Dim typeColIndex As Long, startDateColIndex As Long, endDateColIndex As Long
    Dim durationColIndex As Long, progressColIndex As Long, positionColIndex As Long
    Dim colorColIndex As Long, textPositionColIndex As Long, baselineColIndex As Long
    Dim showDateInLabelColIndex As Long, markColIndex As Long
    Dim hasBaselineCol As Boolean, hasShowDateInLabelCol As Boolean, hasMarkCol As Boolean

    idColIndex = tbl.ListColumns("ID").Index
    categoryColIndex = tbl.ListColumns("Category").Index
    descriptionColIndex = tbl.ListColumns("Description").Index
    typeColIndex = tbl.ListColumns("Type").Index
    startDateColIndex = tbl.ListColumns("Start Date").Index
    endDateColIndex = tbl.ListColumns("End Date").Index
    durationColIndex = tbl.ListColumns("Duration").Index
    progressColIndex = tbl.ListColumns("Progress").Index
    positionColIndex = tbl.ListColumns("Position").Index
    colorColIndex = tbl.ListColumns("Color").Index
    textPositionColIndex = tbl.ListColumns("Text Position").Index

    ' 检查Baseline列是否存在
    On Error Resume Next
    baselineColIndex = tbl.ListColumns("Baseline").Index
    hasBaselineCol = (Err.Number = 0)

    ' 检查ShowDateInLabel列是否存在
    Err.Clear
    showDateInLabelColIndex = tbl.ListColumns("ShowDateInLabel").Index
    hasShowDateInLabelCol = (Err.Number = 0)

    ' 检查Mark列是否存在
    Err.Clear
    markColIndex = tbl.ListColumns("Mark").Index
    hasMarkCol = (Err.Number = 0)
    On Error GoTo ErrorHandler

    ' 一次性读取整个数据区域到数组
    Dim dataArray As Variant
    dataArray = dataRange.Value
    Dim rowCount As Long
    rowCount = UBound(dataArray, 1)

    modDebug.LogInfo "一次性读取任务数据到数组，共 " & rowCount & " 行", "modData.GetAllTasks"

    ' 记录Mark列过滤状态
    If hasMarkCol Then
        modDebug.LogInfo "检测到Mark列，将只处理Mark列非空的任务行", "modData.GetAllTasks"
    Else
        modDebug.LogInfo "未检测到Mark列，将处理所有任务行", "modData.GetAllTasks"
    End If

    ' 预计算所有列在数组中的相对位置（避免在循环中重复计算）
    Dim idColPos As Long, categoryColPos As Long, descriptionColPos As Long
    Dim typeColPos As Long, startDateColPos As Long, endDateColPos As Long
    Dim durationColPos As Long, progressColPos As Long, positionColPos As Long
    Dim colorColPos As Long, textPositionColPos As Long, markColPos As Long
    Dim baselineColPos As Long, showDateInLabelColPos As Long

    idColPos = idColIndex - idColIndex + 1 ' ID列作为基准，位置为1
    categoryColPos = categoryColIndex - idColIndex + 1
    descriptionColPos = descriptionColIndex - idColIndex + 1
    typeColPos = typeColIndex - idColIndex + 1
    startDateColPos = startDateColIndex - idColIndex + 1
    endDateColPos = endDateColIndex - idColIndex + 1
    durationColPos = durationColIndex - idColIndex + 1
    progressColPos = progressColIndex - idColIndex + 1
    positionColPos = positionColIndex - idColIndex + 1
    colorColPos = colorColIndex - idColIndex + 1
    textPositionColPos = textPositionColIndex - idColIndex + 1

    If hasMarkCol Then
        markColPos = markColIndex - idColIndex + 1
    End If

    If hasBaselineCol Then
        baselineColPos = baselineColIndex - idColIndex + 1
    End If

    If hasShowDateInLabelCol Then
        showDateInLabelColPos = showDateInLabelColIndex - idColIndex + 1
    End If

    ' 遍历数组处理数据
    For i = 1 To rowCount
        ' 检查Mark列过滤条件（如果Mark列存在）
        If hasMarkCol Then
            ' 如果Mark列存在且该行的Mark列为空，则跳过此行
            If markColPos > 0 And markColPos <= UBound(dataArray, 2) Then
                If IsEmpty(dataArray(i, markColPos)) Or dataArray(i, markColPos) = "" Then
                    modDebug.LogVerbose "跳过第 " & i & " 行任务，Mark列为空", "modData.GetAllTasks"
                    GoTo NextIteration ' 跳过当前行，继续下一行
                End If
            End If
        End If

        Set task = New Dictionary

        ' 从数组中获取数据，使用动态计算的列位置
        task.Add "ID", dataArray(i, idColPos)

        ' 处理可能为null的Category
        If IsNull(dataArray(i, categoryColPos)) Then
            task.Add "Category", ""
        Else
            task.Add "Category", dataArray(i, categoryColPos)
        End If

        task.Add "Description", dataArray(i, descriptionColPos)
        task.Add "Type", dataArray(i, typeColPos)
        task.Add "StartDate", dataArray(i, startDateColPos)

        ' For milestones, end date equals start date
        If dataArray(i, typeColPos) = "M" Then
            task.Add "EndDate", dataArray(i, startDateColPos)
        Else
            task.Add "EndDate", dataArray(i, endDateColPos)
        End If



        ' Add other fields (if they have values)
        If Not IsEmpty(dataArray(i, durationColPos)) Then
            task.Add "Duration", dataArray(i, durationColPos)
        Else
            ' Calculate duration
            If task("Type") = "A" Then
                task.Add "Duration", CalculateWorkingDays(task("StartDate"), task("EndDate"), excludeWeekends)
            Else
                task.Add "Duration", 0
            End If
        End If

        If Not IsEmpty(dataArray(i, progressColPos)) Then
            task.Add "Progress", dataArray(i, progressColPos)
        Else
            task.Add "Progress", defaultTaskProgress
        End If

        If Not IsEmpty(dataArray(i, positionColPos)) Then
            task.Add "Position", dataArray(i, positionColPos)
        Else
            task.Add "Position", defaultTaskPosition
        End If

        If Not IsEmpty(dataArray(i, colorColPos)) Then
            task.Add "Color", dataArray(i, colorColPos)
        Else
            If task("Type") = "A" Then
                task.Add "Color", defaultTaskColor
            Else
                task.Add "Color", defaultMilestoneColor
            End If
        End If

        If Not IsEmpty(dataArray(i, textPositionColPos)) Then
            task.Add "TextPosition", dataArray(i, textPositionColPos)
        Else
            If task("Type") = "A" Then
                task.Add "TextPosition", defaultTaskTextPosition
            Else
                task.Add "TextPosition", defaultMilestoneTextPosition
            End If
        End If

        ' 处理Baseline列（如果存在）
        If hasBaselineCol Then
            If baselineColPos > 0 And baselineColPos <= UBound(dataArray, 2) Then
                If Not IsEmpty(dataArray(i, baselineColPos)) And IsDate(dataArray(i, baselineColPos)) Then
                    baselineDate = dataArray(i, baselineColPos)
                    task.Add "Baseline", baselineDate
                    modDebug.LogVerbose "任务ID: " & task("ID") & " 的基准线日期: " & Format(baselineDate, "yyyy-mm-dd"), "modData.GetAllTasks"

                    ' 如果传入了基准线集合参数，收集唯一的基准线信息
                    If Not outBaselineCollection Is Nothing Then
                        baselineDateStr = Format(baselineDate, "yyyy-mm-dd")

                        ' 检查是否已经处理过相同日期的基准线
                        If Not baselineDates.Exists(baselineDateStr) Then
                            baselineDates.Add baselineDateStr, True

                            ' 创建基准线信息字典
                            Set baselineInfo = New Dictionary
                            baselineInfo.Add "Date", baselineDate
                            baselineInfo.Add "TaskID", task("ID")

                            ' 添加到基准线集合
                            outBaselineCollection.Add baselineInfo

                            modDebug.LogInfo "收集基准线信息 - 任务ID: " & task("ID") & ", 日期: " & baselineDateStr, "modData.GetAllTasks"
                        Else
                            modDebug.LogVerbose "跳过重复的基准线日期: " & baselineDateStr, "modData.GetAllTasks"
                        End If
                    End If
                End If
            End If
        End If

        ' 处理ShowDateInLabel列（如果存在）
        If hasShowDateInLabelCol Then
            If showDateInLabelColPos > 0 And showDateInLabelColPos <= UBound(dataArray, 2) Then
                If Not IsEmpty(dataArray(i, showDateInLabelColPos)) Then
                    task.Add "ShowDateInLabel", dataArray(i, showDateInLabelColPos)
                    modDebug.LogVerbose "任务ID: " & task("ID") & " 的ShowDateInLabel值: " & dataArray(i, showDateInLabelColPos), "modData.GetAllTasks"
                End If
            End If
        End If

        tasks.Add task
        modDebug.LogVerbose "任务ID: " & task("ID") & " 已添加到任务集合", "modData.GetAllTasks"

NextIteration:
    Next i

    ' 记录处理结果统计
    modDebug.LogInfo "任务数据处理完成 - 总行数: " & rowCount & ", 实际处理任务数: " & tasks.Count, "modData.GetAllTasks"

    Set GetAllTasks = tasks
    Exit Function

ErrorHandler:
    modUtilities.LogError Err.Number, Err.Description, "modData.GetAllTasks"
    Set GetAllTasks = New Collection ' Return empty collection
End Function

' ---------------------------------------------------------
' Configuration Access Functions
' ---------------------------------------------------------

' 获取特定模块的所有配置，使用ConfigName作为键，并支持默认值
Public Function GetModuleConfig(moduleName As String, Optional defaultValues As Dictionary = Nothing) As Dictionary
    On Error GoTo ErrorHandler

    Dim result As New Dictionary
    Dim tbl As ListObject
    Dim dataArray As Variant
    Dim i As Long, moduleCol As Long, enabledCol As Long, nameCol As Long, valueCol As Long

    ' 记录函数进入
    modDebug.LogFunctionEntry "modData.GetModuleConfig"

    ' 获取配置表引用
    Set tbl = ThisWorkbook.Worksheets("Config").ListObjects("configTable")

    ' 如果表格没有数据，返回默认值字典或空字典
    If tbl.DataBodyRange Is Nothing Then
        If Not defaultValues Is Nothing Then
            Set GetModuleConfig = defaultValues
            modDebug.LogInfo "配置表为空，使用提供的默认值", "modData.GetModuleConfig"
        Else
            Set GetModuleConfig = result
            modDebug.LogInfo "配置表为空，返回空字典", "modData.GetModuleConfig"
        End If
        modDebug.LogFunctionExit "modData.GetModuleConfig"
        Exit Function
    End If

    ' 获取列索引（相对于ListObject）
    moduleCol = tbl.ListColumns("Module").Index
    enabledCol = tbl.ListColumns("IsEnabled").Index
    nameCol = tbl.ListColumns("ConfigName").Index
    valueCol = tbl.ListColumns("ConfigValue").Index

    ' 一次性读取整个数据区域到数组
    dataArray = tbl.DataBodyRange.Value

    ' 计算数组中的相对列索引
    ' 在Excel Range.Value数组中，第一列的索引是1，不管ListObject的第一列是什么
    Dim moduleColArray As Long, enabledColArray As Long, nameColArray As Long, valueColArray As Long
    Dim firstColIndex As Long

    ' 获取ListObject第一列的索引
    firstColIndex = tbl.ListColumns(1).Index

    ' 计算数组中的相对列索引
    moduleColArray = moduleCol - firstColIndex + 1
    enabledColArray = enabledCol - firstColIndex + 1
    nameColArray = nameCol - firstColIndex + 1
    valueColArray = valueCol - firstColIndex + 1

    modDebug.LogVerbose "列索引映射 - Module: " & moduleCol & "->" & moduleColArray & _
                       ", Enabled: " & enabledCol & "->" & enabledColArray & _
                       ", Name: " & nameCol & "->" & nameColArray & _
                       ", Value: " & valueCol & "->" & valueColArray, _
                       "modData.GetModuleConfig"

    ' 遍历数组处理数据
    For i = LBound(dataArray, 1) To UBound(dataArray, 1)
        ' 如果模块名称匹配且配置已启用
        If dataArray(i, moduleColArray) = moduleName And dataArray(i, enabledColArray) = True Then
            Dim configName As String
            Dim configValue As Variant

            configName = dataArray(i, nameColArray)
            configValue = dataArray(i, valueColArray)

            ' 使用ConfigName作为键
            result.Add configName, configValue
        End If
    Next i

    ' 如果提供了默认值字典，将缺失的键从默认值字典中添加到结果中
    If Not defaultValues Is Nothing Then
        Dim key As Variant
        For Each key In defaultValues.Keys
            If Not result.Exists(key) Then
                result.Add key, defaultValues(key)
                modDebug.LogVerbose "配置项 " & key & " 不存在，使用默认值: " & defaultValues(key), "modData.GetModuleConfig"
            End If
        Next key
    End If

    Set GetModuleConfig = result
    modDebug.LogFunctionExit "modData.GetModuleConfig", "获取到 " & result.Count & " 个配置项"
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modData.GetModuleConfig"

    ' 如果出错且提供了默认值字典，返回默认值字典
    If Not defaultValues Is Nothing Then
        Set GetModuleConfig = defaultValues
    Else
        Set GetModuleConfig = New Dictionary ' 返回空字典
    End If
    modDebug.LogFunctionExit "modData.GetModuleConfig", "错误: " & Err.Description
End Function

' 从配置字典中获取值，支持默认值
Public Function GetConfigFromDict(dict As Dictionary, configName As String, Optional defaultValue As Variant = Null) As Variant
    On Error GoTo ErrorHandler

    If dict.Exists(configName) Then
        GetConfigFromDict = dict(configName)
    Else
        GetConfigFromDict = defaultValue
        modDebug.LogVerbose "配置项 " & configName & " 在字典中不存在，使用默认值: " & defaultValue, "modData.GetConfigFromDict"
    End If

    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modData.GetConfigFromDict"
    GetConfigFromDict = defaultValue
End Function

' 直接获取配置项的值，不考虑模块，支持默认值
Public Function GetConfig(configName As String, Optional defaultValue As Variant = Null) As Variant
    On Error GoTo ErrorHandler

    Dim tbl As ListObject
    Dim dataArray As Variant
    Dim i As Long, enabledCol As Long, nameCol As Long, valueCol As Long

    ' 记录函数进入
    modDebug.LogFunctionEntry "modData.GetConfig"

    ' 获取配置表引用
    Set tbl = ThisWorkbook.Worksheets("Config").ListObjects("configTable")

    ' 如果表格没有数据，返回默认值
    If tbl.DataBodyRange Is Nothing Then
        GetConfig = defaultValue
        modDebug.LogInfo "配置表为空，使用默认值: " & defaultValue, "modData.GetConfig"
        modDebug.LogFunctionExit "modData.GetConfig"
        Exit Function
    End If

    ' 获取列索引（相对于ListObject）
    enabledCol = tbl.ListColumns("IsEnabled").Index
    nameCol = tbl.ListColumns("ConfigName").Index
    valueCol = tbl.ListColumns("ConfigValue").Index

    ' 一次性读取整个数据区域到数组
    dataArray = tbl.DataBodyRange.Value

    ' 计算数组中的相对列索引
    ' 在Excel Range.Value数组中，第一列的索引是1，不管ListObject的第一列是什么
    Dim enabledColArray As Long, nameColArray As Long, valueColArray As Long
    Dim firstColIndex As Long

    ' 获取ListObject第一列的索引
    firstColIndex = tbl.ListColumns(1).Index

    ' 计算数组中的相对列索引
    enabledColArray = enabledCol - firstColIndex + 1
    nameColArray = nameCol - firstColIndex + 1
    valueColArray = valueCol - firstColIndex + 1

    modDebug.LogVerbose "列索引映射 - Enabled: " & enabledCol & "->" & enabledColArray & _
                       ", Name: " & nameCol & "->" & nameColArray & _
                       ", Value: " & valueCol & "->" & valueColArray, _
                       "modData.GetConfig"

    ' 遍历数组处理数据
    For i = LBound(dataArray, 1) To UBound(dataArray, 1)
        ' 如果配置名称匹配且配置已启用
        If dataArray(i, nameColArray) = configName And dataArray(i, enabledColArray) = True Then
            GetConfig = dataArray(i, valueColArray)
            modDebug.LogVerbose "找到配置项 " & configName & "，值为: " & GetConfig, "modData.GetConfig"
            modDebug.LogFunctionExit "modData.GetConfig"
            Exit Function
        End If
    Next i

    ' 如果没有找到配置项，返回默认值
    GetConfig = defaultValue
    modDebug.LogVerbose "配置项 " & configName & " 不存在或未启用，使用默认值: " & defaultValue, "modData.GetConfig"
    modDebug.LogFunctionExit "modData.GetConfig"
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modData.GetConfig"
    GetConfig = defaultValue
    modDebug.LogFunctionExit "modData.GetConfig", "错误: " & Err.Description
End Function
