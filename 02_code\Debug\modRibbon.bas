'Attribute VB_Name = "modRibbon"
Option Explicit

' =========================================================
' 模块: modRibbon
' 描述: 包含所有与Excel Ribbon界面相关的宏函数
' =========================================================

' ---------------------------------------------------------
' Ribbon回调函数
' ---------------------------------------------------------

' 生成甘特图
Public Sub Ribbon_GenerateGanttChart(control As IRibbonControl)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modRibbon.Ribbon_GenerateGanttChart"

    ' 调用主函数生成甘特图
    modMain.GenerateGanttChart

    ' 记录函数退出
    modDebug.LogFunctionExit "modRibbon.Ribbon_GenerateGanttChart", "成功"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modRibbon.Ribbon_GenerateGanttChart"
    MsgBox "生成甘特图时发生错误: " & Err.Description, vbExclamation, "错误"
End Sub

' 应用配置表预览
Public Sub Ribbon_ApplyConfigPreview(control As IRibbonControl)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modRibbon.Ribbon_ApplyConfigPreview"

    ' 直接切换到Config工作表
    On Error Resume Next
    ThisWorkbook.Worksheets("Config").Activate
    If Err.Number <> 0 Then
        MsgBox "未找到Config工作表，无法应用配置预览。", vbExclamation, "错误"
        modDebug.LogError Err.Number, "未找到Config工作表", "modRibbon.Ribbon_ApplyConfigPreview"
        Exit Sub
    End If
    On Error GoTo ErrorHandler

    ' 调用UI模块的函数应用配置预览
    modUI.ApplyConfigTablePreview

    ' 记录函数退出
    modDebug.LogFunctionExit "modRibbon.Ribbon_ApplyConfigPreview", "成功"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modRibbon.Ribbon_ApplyConfigPreview"
    MsgBox "应用配置预览时发生错误: " & Err.Description, vbExclamation, "错误"
End Sub

' 合并跨月周单元格
Public Sub Ribbon_MergeWeekColumns(control As IRibbonControl)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modRibbon.Ribbon_MergeWeekColumns"

    ' 直接切换到GanttChart工作表
    On Error Resume Next
    ThisWorkbook.Worksheets("GanttChart").Activate
    If Err.Number <> 0 Then
        MsgBox "未找到GanttChart工作表，无法执行合并操作。", vbExclamation, "错误"
        modDebug.LogError Err.Number, "未找到GanttChart工作表", "modRibbon.Ribbon_MergeWeekColumns"
        Exit Sub
    End If
    On Error GoTo ErrorHandler

    ' 调用UI模块的函数合并跨月周单元格
    modUI.MergeWeekColumns

    ' 记录函数退出
    modDebug.LogFunctionExit "modRibbon.Ribbon_MergeWeekColumns", "成功"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modRibbon.Ribbon_MergeWeekColumns"
    MsgBox "合并跨月周单元格时发生错误: " & Err.Description, vbExclamation, "错误"
End Sub

' ---------------------------------------------------------
' 辅助函数
' ---------------------------------------------------------

' 获取Ribbon XML
Public Function GetRibbonXML() As String
    ' 这个函数可以返回动态生成的Ribbon XML
    ' 目前返回一个基本的XML结构，可以根据需要扩展

    Dim xml As String
    xml = "<customUI xmlns='http://schemas.microsoft.com/office/2009/07/customui'>" & _
          "  <ribbon>" & _
          "    <tabs>" & _
          "      <tab id='tabGanttChart' label='甘特图'>" & _
          "        <group id='grpGantt' label='甘特图工具'>" & _
          "          <button id='btnGenerateGantt' label='生成甘特图' " & _
          "                  imageMso='ChartInsert' size='large' " & _
          "                  onAction='modRibbon.Ribbon_GenerateGanttChart'/>" & _
          "          <button id='btnMergeWeekColumns' label='合并跨月周' " & _
          "                  imageMso='MergeCells' size='large' " & _
          "                  onAction='modRibbon.Ribbon_MergeWeekColumns'/>" & _
          "        </group>" & _
          "        <group id='grpConfig' label='配置工具'>" & _
          "          <button id='btnApplyConfigPreview' label='应用配置预览' " & _
          "                  imageMso='FormattingProperties' size='large' " & _
          "                  onAction='modRibbon.Ribbon_ApplyConfigPreview'/>" & _
          "        </group>" & _
          "      </tab>" & _
          "    </tabs>" & _
          "  </ribbon>" & _
          "</customUI>"

    GetRibbonXML = xml
End Function
