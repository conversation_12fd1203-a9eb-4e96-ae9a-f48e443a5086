# DrawTask 和 DrawMilestone 函数详细说明文档

本文档详细说明Excel VBA项目管理甘特图系统中的DrawTask和DrawMilestone函数的实现逻辑、输入、输出和处理过程。

## 1. DrawTask 函数

### 1.1 功能概述

`DrawTask` 函数负责在甘特图工作表上绘制任务条形图形。该函数根据任务的开始日期和结束日期，在指定行位置创建矩形形状表示任务，并根据任务进度添加进度条，同时添加任务描述标签。

### 1.2 函数签名

```vba
Private Sub DrawTask(task As Dictionary, row As Long, timelineCoords As Dictionary)
```

### 1.3 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| task | Dictionary | 包含任务信息的字典对象 |
| row | Long | 任务在甘特图上的行位置（行号） |
| timelineCoords | Dictionary | 时间轴坐标系信息 |

#### 1.3.1 task 字典必需的键

- **StartDate**：任务开始日期（Date类型）
- **EndDate**：任务结束日期（Date类型）
- **Color**：任务颜色（十六进制格式，如"#FF0000"）
- **Progress**：任务进度（0-1之间的小数）

#### 1.3.2 task 字典可选的键

- **Description**：任务描述（用于标签）
- **ID**：任务ID（用于日志记录）
- **TextPosition**/**Text Position**/**Text_Position**：标签位置（"right"、"left"、"top"、"bottom"、"inside"）

#### 1.3.3 timelineCoords 字典必需的键

- **OriginX**：坐标系原点的X坐标
- **Width**：坐标系的总宽度
- **StartDate**：坐标系的起始日期
- **EndDate**：坐标系的结束日期
- **TotalDays**：坐标系覆盖的总天数

### 1.4 配置参数

函数使用以下配置参数，可以在Config工作表中设置：

| 配置ID | 配置名称 | 默认值 | 说明 |
|--------|----------|--------|------|
| GT026 | TaskBarHeight | 11 | 任务条高度（像素） |
| GT027 | TaskBarBorderWidth | 0 | 任务条边框宽度（0=无边框） |
| GT028 | ProgressBarColor | #66CC66 | 进度条颜色 |
| GT029 | LabelDistance | 5 | 标签与任务条的距离（像素） |
| GT030 | RowPadding | 3 | 行高上下预留空隙（像素） |

### 1.5 处理流程

```mermaid
flowchart TD
    A[开始] --> B[初始化和获取配置参数]
    B --> C[获取GanttChart工作表引用]
    C --> D[计算任务条位置和大小]
    D --> E[创建任务条形状]
    E --> F{创建成功?}
    F -->|否| G[记录错误并退出]
    F -->|是| H[设置任务条填充颜色和边框]
    H --> I{任务有进度?}
    I -->|是| J[计算进度条宽度]
    J --> K[创建进度条形状]
    K --> L{创建成功?}
    L -->|否| M[记录错误但继续执行]
    L -->|是| N[设置进度条颜色和边框]
    N --> O[将进度条置于最顶层]
    I -->|否| P[跳过进度条创建]
    M --> P
    O --> P
    P --> Q[调用AddTaskLabelWithCoordinates2添加标签]
    Q --> R{标签创建成功?}
    R -->|否| S[记录警告]
    R -->|是| T[调用AdjustRowHeightWithPadding调整行高]
    T --> U[记录函数退出]
    S --> U
    U --> V[结束]
    G --> V
```

#### 1.5.1 初始化和获取配置参数

```vba
' 记录函数进入
Dim taskId As String, taskDesc As String
taskId = IIf(task.Exists("ID"), task("ID"), "未知ID")
taskDesc = IIf(task.Exists("Description"), task("Description"), "未知描述")
modDebug.LogFunctionEntry "modGantt.DrawTask", "任务ID: " & taskId & ", 描述: " & taskDesc & ", 行: " & row

' 获取配置参数
Dim taskBarHeight As Long
configValue = GetConfigValue("GT026", "11")
If IsNumeric(configValue) Then
    taskBarHeight = CLng(Val(configValue))
Else
    taskBarHeight = 11 ' 默认值
End If

' 获取任务条边框宽度
configValue = GetConfigValue("GT027", "0")
If IsNumeric(configValue) Then
    taskBarBorderWidth = CSng(Val(configValue))
Else
    taskBarBorderWidth = 0 ' 默认值
End If

' 获取进度条颜色
progressBarColor = GetConfigValue("GT028", "#66CC66")

' 获取标签距离
configValue = GetConfigValue("GT029", "5")
If IsNumeric(configValue) Then
    labelDistance = CLng(Val(configValue))
Else
    labelDistance = 5 ' 默认值
End If

' 获取行高预留空隙
configValue = GetConfigValue("GT030", "3")
If IsNumeric(configValue) Then
    rowPadding = CLng(Val(configValue))
Else
    rowPadding = 3 ' 默认值
End If
```

#### 1.5.2 计算任务条位置和大小

```vba
' 计算任务开始和结束的X坐标
Dim startX As Double, endX As Double
startX = CalculateXCoordinate(task("StartDate"), timelineCoords)
endX = CalculateXCoordinate(task("EndDate"), timelineCoords)

' 计算任务条的左边缘、宽度和高度
left = startX
width = endX - startX
height = taskBarHeight

' 计算任务条的垂直居中位置
Dim rowHeight As Double
rowHeight = ws.Cells(row, 1).Height
top = ws.Cells(row, 1).Top + (rowHeight - height) / 2 ' 垂直居中
```

#### 1.5.3 创建任务条形状

```vba
' 创建任务条形状
Dim taskShape As shape
On Error Resume Next
Set taskShape = ws.Shapes.AddShape(msoShapeRectangle, left, top, width, height)

If Err.Number <> 0 Then
    modDebug.LogError Err.Number, "创建任务条形状失败 - " & Err.description, "modGantt.DrawTask"
    modDebug.LogFunctionExit "modGantt.DrawTask", "失败 - 无法创建任务条形状"
    Exit Sub
End If
On Error GoTo ErrorHandler

' 设置任务条格式
taskShape.Fill.ForeColor.RGB = GetRGBColor(task("Color"))

' 设置任务条边框
If taskBarBorderWidth = 0 Then
    taskShape.Line.Visible = msoFalse ' 无边框
Else
    taskShape.Line.Visible = msoTrue
    taskShape.Line.Weight = taskBarBorderWidth
    taskShape.Line.ForeColor.RGB = GetRGBColor(task("Color"))
End If
```

#### 1.5.4 创建进度条（如果有进度）

```vba
' 如果有进度，绘制进度条
Dim progressShape As Shape
Set progressShape = Nothing

If task("Progress") > 0 Then
    Dim progressWidth As Double
    progressWidth = width * task("Progress")

    On Error Resume Next
    Set progressShape = ws.Shapes.AddShape(msoShapeRectangle, left, top, progressWidth, height)

    If Err.Number <> 0 Then
        modDebug.LogError Err.Number, "创建进度条形状失败 - " & Err.description, "modGantt.DrawTask"
    Else
        progressShape.Fill.ForeColor.RGB = GetRGBColor(progressBarColor)

        ' 设置进度条边框与任务条一致
        If taskBarBorderWidth = 0 Then
            progressShape.Line.Visible = msoFalse ' 无边框
        Else
            progressShape.Line.Visible = msoTrue
            progressShape.Line.Weight = taskBarBorderWidth
            progressShape.Line.ForeColor.RGB = GetRGBColor(task("Color"))
        End If

        ' 将进度条置于最顶层
        progressShape.ZOrder msoBringToFront
    End If

    On Error GoTo ErrorHandler
End If
```

#### 1.5.5 添加任务描述标签

```vba
' 添加任务描述标签
Dim labelShape As Shape
Set labelShape = AddTaskLabelWithCoordinates2(task, taskShape, startX + width / 2, top + height / 2, CDbl(height), labelDistance)

' 动态调整行高以适应任务和标签
If Not labelShape Is Nothing Then
    AdjustRowHeightWithPadding ws, row, taskShape, labelShape, rowPadding
End If
```

### 1.6 输出

函数没有直接的返回值，但会在工作表上创建以下图形元素：
1. 任务条形状（矩形）
2. 进度条形状（如果任务有进度）
3. 任务描述标签（文本框）

## 2. DrawMilestone 函数

### 2.1 功能概述

`DrawMilestone` 函数负责在甘特图工作表上绘制里程碑图形。该函数根据里程碑的日期，在指定行位置创建菱形形状表示里程碑，并添加里程碑描述标签。

### 2.2 函数签名

```vba
Private Sub DrawMilestone(task As Dictionary, row As Long, timelineCoords As Dictionary)
```

### 2.3 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| task | Dictionary | 包含里程碑信息的字典对象 |
| row | Long | 里程碑在甘特图上的行位置（行号） |
| timelineCoords | Dictionary | 时间轴坐标系信息 |

#### 2.3.1 task 字典必需的键

- **StartDate**：里程碑日期（Date类型）
- **Color**：里程碑颜色（十六进制格式，如"#FF0000"）

#### 2.3.2 task 字典可选的键

- **Description**：里程碑描述（用于标签）
- **ID**：里程碑ID（用于日志记录）
- **TextPosition**/**Text Position**/**Text_Position**：标签位置（"right"、"left"、"top"、"bottom"）

#### 2.3.3 timelineCoords 字典必需的键

- **OriginX**：坐标系原点的X坐标
- **Width**：坐标系的总宽度
- **StartDate**：坐标系的起始日期
- **EndDate**：坐标系的结束日期
- **TotalDays**：坐标系覆盖的总天数

### 2.4 配置参数

函数使用以下配置参数，可以在Config工作表中设置：

| 配置ID | 配置名称 | 默认值 | 说明 |
|--------|----------|--------|------|
| GT026 | TaskBarHeight | 11 | 里程碑大小（像素） |
| GT027 | TaskBarBorderWidth | 0 | 里程碑边框宽度（0=无边框） |
| GT029 | LabelDistance | 5 | 标签与里程碑的距离（像素） |
| GT030 | RowPadding | 3 | 行高上下预留空隙（像素） |

### 2.5 处理流程

```mermaid
flowchart TD
    A[开始] --> B[初始化和获取配置参数]
    B --> C[获取GanttChart工作表引用]
    C --> D[计算里程碑在坐标系中的位置]
    D --> E[计算里程碑形状的左上角坐标]
    E --> F[创建里程碑形状]
    F --> G{创建成功?}
    G -->|否| H[记录错误并退出]
    G -->|是| I[设置里程碑填充颜色]
    I --> J{边框宽度为0?}
    J -->|是| K[设置无边框]
    J -->|否| L[设置边框宽度和颜色]
    K --> M[调用AddTaskLabelWithCoordinates2添加标签]
    L --> M
    M --> N{标签创建成功?}
    N -->|否| O[记录警告]
    N -->|是| P[调用AdjustRowHeightWithPadding调整行高]
    P --> Q[记录函数退出]
    O --> Q
    Q --> R[结束]
    H --> R
```

#### 2.5.1 初始化和获取配置参数

```vba
' 记录函数进入
Dim taskId As String, taskDesc As String
taskId = IIf(task.Exists("ID"), task("ID"), "未知ID")
taskDesc = IIf(task.Exists("Description"), task("Description"), "未知描述")
modDebug.LogFunctionEntry "modGantt.DrawMilestone", "任务ID: " & taskId & ", 描述: " & taskDesc & ", 行: " & row

' 获取配置参数
Dim milestoneSize As Long
configValue = GetConfigValue("GT026", "11")
If IsNumeric(configValue) Then
    milestoneSize = CLng(Val(configValue))
Else
    milestoneSize = 11 ' 默认值
End If

' 获取里程碑边框宽度
configValue = GetConfigValue("GT027", "0")
If IsNumeric(configValue) Then
    milestoneBorderWidth = CSng(Val(configValue))
Else
    milestoneBorderWidth = 0 ' 默认值
End If

' 获取标签距离
configValue = GetConfigValue("GT029", "5")
If IsNumeric(configValue) Then
    labelDistance = CLng(Val(configValue))
Else
    labelDistance = 5 ' 默认值
End If

' 获取行高预留空隙
configValue = GetConfigValue("GT030", "3")
If IsNumeric(configValue) Then
    rowPadding = CLng(Val(configValue))
Else
    rowPadding = 3 ' 默认值
End If
```

#### 2.5.2 计算里程碑位置

```vba
' 计算里程碑在坐标系中的精确位置
Dim milestoneX As Double, milestoneY As Double
milestoneX = CalculateXCoordinate(task("StartDate"), timelineCoords)

' 计算Y坐标 - 使用行的中心点
Dim rowHeight As Double
rowHeight = ws.Cells(row, 1).Height
milestoneY = ws.Cells(row, 1).Top + rowHeight / 2

' 计算里程碑形状的左上角坐标
Dim shapeLeft As Double, shapeTop As Double
shapeLeft = milestoneX - milestoneSize / 2
shapeTop = milestoneY - milestoneSize / 2
```

#### 2.5.3 创建里程碑形状

```vba
' 创建里程碑形状（菱形）
Dim milestoneShape As Shape
On Error Resume Next
Set milestoneShape = ws.Shapes.AddShape(msoShapeDiamond, shapeLeft, shapeTop, milestoneSize, milestoneSize)

If Err.Number <> 0 Then
    modDebug.LogError Err.Number, "创建里程碑形状失败 - " & Err.Description, "modGantt.DrawMilestone"
    modDebug.LogFunctionExit "modGantt.DrawMilestone", "失败 - 无法创建里程碑形状"
    Exit Sub
End If
On Error GoTo ErrorHandler

' 设置里程碑格式
milestoneShape.Fill.ForeColor.RGB = GetRGBColor(task("Color"))

' 设置里程碑边框
If milestoneBorderWidth = 0 Then
    milestoneShape.Line.Visible = msoFalse ' 无边框
Else
    milestoneShape.Line.Visible = msoTrue
    milestoneShape.Line.Weight = milestoneBorderWidth
    milestoneShape.Line.ForeColor.RGB = GetRGBColor(task("Color"))
End If
```

#### 2.5.4 添加里程碑描述标签

```vba
' 添加里程碑描述标签
Dim labelShape As Shape
Set labelShape = AddTaskLabelWithCoordinates2(task, milestoneShape, milestoneX, milestoneY, CDbl(milestoneSize), labelDistance)

' 动态调整行高以适应里程碑和标签
If Not labelShape Is Nothing Then
    AdjustRowHeightWithPadding ws, row, milestoneShape, labelShape, rowPadding
End If
```

### 2.6 输出

函数没有直接的返回值，但会在工作表上创建以下图形元素：
1. 里程碑形状（菱形）
2. 里程碑描述标签（文本框）

## 3. 标签处理 - AddTaskLabelWithCoordinates2 函数

### 3.1 功能概述

`AddTaskLabelWithCoordinates2` 函数负责创建任务或里程碑的描述标签，并根据指定的位置或自动判断将标签放置在合适的位置。这是对原始`AddTaskLabelWithCoordinates`函数的改进版本，支持可配置的标签距离和智能对齐。

### 3.2 函数签名

```vba
Private Function AddTaskLabelWithCoordinates2(task As Dictionary, shape As Shape, centerX As Double, centerY As Double, shapeSize As Double, labelDistance As Long) As Shape
```

### 3.3 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| task | Dictionary | 包含任务或里程碑信息的字典对象 |
| shape | Shape | 任务条或里程碑形状对象 |
| centerX | Double | 形状中心点的X坐标 |
| centerY | Double | 形状中心点的Y坐标 |
| shapeSize | Double | 形状的大小（高度） |
| labelDistance | Long | 标签与形状之间的距离（像素） |

### 3.4 处理流程

```mermaid
flowchart TD
    A[开始] --> B[获取任务描述]
    B --> C{描述为空?}
    C -->|是| D[返回Nothing]
    C -->|否| E[创建临时文本框计算文本尺寸]
    E --> F[获取任务的TextPosition属性]
    F --> G[判断标签是否能放在形状内部]
    G --> H{能放在内部?}
    H -->|是| I[设置内部居中位置]
    H -->|否| J[根据TextPosition或自动判断位置]
    J --> K{TextPosition值?}
    K -->|right| L[设置右侧位置]
    K -->|left| M[设置左侧位置]
    K -->|top| N[设置上方位置]
    K -->|bottom| O[设置下方位置]
    K -->|未指定| P[自动判断最佳位置]
    P --> Q{右侧有足够空间?}
    Q -->|是| L
    Q -->|否| R{左侧有足够空间?}
    R -->|是| M
    R -->|否| S{上方有足够空间?}
    S -->|是| N
    S -->|否| O
    I --> T[创建标签文本框]
    L --> T
    M --> T
    N --> T
    O --> T
    T --> U{创建成功?}
    U -->|否| V[记录错误并返回Nothing]
    U -->|是| W[设置标签文本和格式]
    W --> X[设置标签透明]
    X --> Y[返回创建的标签]
    V --> Z[结束]
    Y --> Z
    D --> Z
```

### 3.5 标签位置算法

函数根据任务的TextPosition属性或自动判断确定标签位置：

#### 3.5.1 内部位置

如果标签宽度小于任务条宽度的90%且未指定位置或指定为"inside"，则放在任务条内部，居中对齐：

```vba
' 检查标签是否能放在任务条内部
Dim canFitInside As Boolean
canFitInside = (textWidth <= taskWidth * 0.9) And (textPosition = "" Or textPosition = "inside")

If canFitInside Then
    ' 放在任务条内部，居中对齐
    labelX = centerX - textWidth / 2  ' 水平居中对齐
    labelY = centerY - textHeight / 2
    textAlign = xlCenter
End If
```

#### 3.5.2 指定位置

根据TextPosition属性确定位置：

1. **右侧位置**（"right"）：
   ```vba
   ' 放在右侧，左对齐
   If isMilestone Then
       ' 里程碑是菱形，需要调整水平距离
       labelX = centerX + taskWidth / 2 * 0.7 + labelDistance
   Else
       labelX = centerX + taskWidth / 2 + labelDistance
   End If
   labelY = centerY - textHeight / 2  ' 水平中心与任务条/里程碑中心水平对齐
   textAlign = xlLeft
   ```

2. **左侧位置**（"left"）：
   ```vba
   ' 放在左侧，右对齐
   If isMilestone Then
       ' 里程碑是菱形，需要调整水平距离
       labelX = centerX - taskWidth / 2 * 0.7 - labelDistance - textWidth
   Else
       labelX = centerX - taskWidth / 2 - labelDistance - textWidth
   End If
   labelY = centerY - textHeight / 2  ' 水平中心与任务条/里程碑中心水平对齐
   textAlign = xlRight
   ```

3. **上方位置**（"top"）：
   ```vba
   ' 放在上方
   If isMilestone Then
       ' 里程碑是菱形，标签居中于菱形上方
       labelX = centerX - textWidth / 2
   Else
       ' 任务条是矩形，标签框左边与任务条左边垂直对齐
       labelX = taskLeft
   End If
   labelY = centerY - taskHeight / 2 - labelDistance - textHeight
   textAlign = xlLeft

   ' 确保标签不会太远离任务条/里程碑
   If (centerY - taskHeight / 2) - (labelY + textHeight) > 10 Then
       labelY = centerY - taskHeight / 2 - textHeight - 5
   End If
   ```

4. **下方位置**（"bottom"）：
   ```vba
   ' 放在下方
   If isMilestone Then
       ' 里程碑是菱形，标签居中于菱形下方
       labelX = centerX - textWidth / 2
   Else
       ' 任务条是矩形，标签框左边与任务条左边垂直对齐
       labelX = taskLeft
   End If
   labelY = centerY + taskHeight / 2 + labelDistance
   textAlign = xlLeft

   ' 确保标签不会太远离任务条/里程碑
   If labelY - (centerY + taskHeight / 2) > 10 Then
       labelY = centerY + taskHeight / 2 + 5
   End If
   ```

#### 3.5.3 自动判断位置

如果未指定位置，函数会自动判断最佳位置：

```vba
' 未指定或无效的Text Position，自动判断最佳位置
' 尝试放在右侧
If centerX + taskWidth / 2 + labelDistance + textWidth < ws.UsedRange.Width Then
    ' 放在右侧，左对齐
    ' [右侧位置代码]
' 尝试放在左侧
ElseIf centerX - taskWidth / 2 - labelDistance - textWidth > 0 Then
    ' 放在左侧，右对齐
    ' [左侧位置代码]
' 尝试放在上方
ElseIf centerY - taskHeight / 2 - labelDistance - textHeight > 0 Then
    ' 放在上方
    ' [上方位置代码]
' 放在下方
Else
    ' 放在下方
    ' [下方位置代码]
End If
```

### 3.6 标签创建和格式设置

创建标签文本框并设置格式：

```vba
' 创建标签文本框
Dim labelShape As Shape
Set labelShape = ws.Shapes.AddTextbox(msoTextOrientationHorizontal, labelX, labelY, textWidth, textHeight)

' 设置标签文本和格式
labelShape.TextFrame.Characters.Text = description
labelShape.TextFrame.Characters.Font.Size = 8
labelShape.TextFrame.Characters.Font.Name = "Arial"
labelShape.TextFrame.HorizontalAlignment = textAlign
labelShape.TextFrame.VerticalAlignment = xlCenter

' 设置文本框边距，使标签框尺寸更小
labelShape.TextFrame.MarginLeft = 1
labelShape.TextFrame.MarginRight = 1
labelShape.TextFrame.MarginTop = 1
labelShape.TextFrame.MarginBottom = 1
labelShape.TextFrame.AutoSize = True

' 设置标签透明
labelShape.Fill.Visible = msoFalse
labelShape.Line.Visible = msoFalse
```

## 4. 行高调整 - AdjustRowHeightWithPadding 函数

### 4.1 功能概述

`AdjustRowHeightWithPadding` 函数负责动态调整行高，确保任务条/里程碑和标签能够完全显示，不会被截断。该函数考虑形状和标签的总高度，并添加上下预留空隙。

### 4.2 函数签名

```vba
Private Sub AdjustRowHeightWithPadding(ws As Worksheet, row As Long, shapeObj As Shape, labelObj As Shape, padding As Long)
```

### 4.3 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| ws | Worksheet | 工作表引用 |
| row | Long | 需要调整高度的行号 |
| shapeObj | Shape | 任务条或里程碑形状对象 |
| labelObj | Shape | 标签形状对象 |
| padding | Long | 行高上下预留空隙（像素） |

### 4.4 处理流程

```mermaid
flowchart TD
    A[开始] --> B[获取当前行高]
    B --> C[获取形状的上下边界]
    C --> D[获取标签的上下边界]
    D --> E[找出最高和最低点]
    E --> F[计算所需高度]
    F --> G{所需高度>当前行高?}
    G -->|否| H[无需调整]
    G -->|是| I[检查工作表是否被保护]
    I --> J{工作表被保护?}
    J -->|是| K[临时取消保护]
    J -->|否| L[尝试设置行高]
    K --> L
    L --> M{设置成功?}
    M -->|否| N[尝试使用替代方法]
    M -->|是| O[记录行高调整成功]
    N --> P{替代方法成功?}
    P -->|否| Q[记录警告]
    P -->|是| R[记录替代方法成功]
    O --> S[恢复工作表保护]
    R --> S
    Q --> S
    S --> T[记录函数退出]
    H --> T
    T --> U[结束]
```

### 4.5 实现细节

1. **计算所需高度**：
   ```vba
   ' 找出最高和最低点
   Dim topMost As Double, bottomMost As Double
   topMost = Application.WorksheetFunction.Min(shapeTop, labelTop)
   bottomMost = Application.WorksheetFunction.Max(shapeBottom, labelBottom)

   ' 计算所需高度（添加上下预留空隙）
   requiredHeight = bottomMost - topMost + (padding * 2)
   ```

2. **调整行高**：
   ```vba
   ' 如果所需高度大于当前行高，则调整行高
   If requiredHeight > currentHeight Then
       ' 尝试设置行高
       ws.Rows(row).RowHeight = requiredHeight

       ' 如果失败，尝试替代方法
       If Err.Number <> 0 Then
           Err.Clear
           ws.Cells(row, 1).EntireRow.RowHeight = requiredHeight
       End If
   End If
   ```

## 5. 注意事项

### 5.1 DrawTask 函数注意事项

1. **进度条处理**：
   - 只有当任务进度大于0时才创建进度条
   - 进度条与任务条使用相同的边框设置
   - 进度条总是置于最顶层，确保可见
   - 进度条宽度根据任务进度比例计算（width * progress）

2. **错误处理**：
   - 使用On Error Resume Next捕获形状创建错误
   - 如果任务条创建失败，函数会记录错误并退出
   - 如果进度条创建失败，函数会记录错误但继续执行

3. **坐标计算**：
   - 任务条左边缘位置基于开始日期的X坐标
   - 任务条宽度基于开始日期和结束日期的X坐标差
   - 任务条垂直位置计算确保在行内垂直居中

### 5.2 DrawMilestone 函数注意事项

1. **里程碑形状**：
   - 使用菱形形状（msoShapeDiamond）表示里程碑
   - 里程碑大小由配置参数控制（默认11像素）
   - 里程碑在行内垂直居中
   - 里程碑的左上角坐标需要特殊计算，确保菱形中心点位于正确位置

2. **标签位置**：
   - 里程碑标签位置与任务标签使用相同的逻辑
   - 对于上/下位置，里程碑标签居中对齐，而任务标签左对齐
   - 里程碑的左/右位置需要特殊调整（使用0.7系数），考虑菱形的几何特性

### 5.3 标签处理注意事项

1. **标签位置检测**：
   - 函数会检查多种可能的键名：TextPosition、Text Position、Text_Position
   - 标签位置字符串不区分大小写，并会去除空格
   - 支持的位置值：
     - 任务(Type="A")：right、left、top、bottom、inside
     - 里程碑(Type="M")：right、left、top、bottom

2. **智能位置判断**：
   - 如果未指定位置，函数会自动判断最佳位置
   - 优先考虑右侧、左侧、上方，最后考虑下方
   - 自动判断会考虑工作表边界，确保标签不会超出可见区域

3. **里程碑与任务条的区别**：
   - 对于里程碑（菱形），上/下位置的标签居中对齐
   - 对于任务条（矩形），上/下位置的标签左对齐
   - 里程碑的左/右位置需要特殊调整（使用0.7系数），考虑菱形的几何特性

4. **标签格式设置**：
   - 标签使用Arial字体，8号字体大小
   - 标签文本框是透明的（无填充和边框）
   - 标签文本框使用最小边距（1像素），减少空白区域
   - 标签使用AutoSize属性，自动调整大小以适应文本内容

5. **标签与形状的距离控制**：
   - 标签与形状之间的距离由labelDistance参数控制（默认5像素）
   - 对于上/下位置，会额外检查距离，确保标签不会太远离形状（最大10像素）

6. **空描述处理**：
   - 如果任务或里程碑没有Description属性或Description为空，函数会返回Nothing
   - 这种情况下不会创建标签，也不会调整行高

7. **动态行高调整**：
   - 标签创建后，会调用AdjustRowHeightWithPadding函数动态调整行高
   - 调整考虑形状和标签的总高度，加上上下预留空隙（默认3像素）
   - 这确保了所有元素都能完全显示，不会被截断
