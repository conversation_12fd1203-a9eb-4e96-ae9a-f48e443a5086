# 甘特图基准线(Baseline)功能说明文档

## 目录

1. [概述](#概述)
2. [基准线功能的输入](#基准线功能的输入)
3. [基准线功能的输出](#基准线功能的输出)
4. [处理流程](#处理流程)
5. [配置参数](#配置参数)
6. [函数说明](#函数说明)
7. [使用示例](#使用示例)
8. [注意事项](#注意事项)

## 概述

基准线(Baseline)是甘特图中的一种重要元素，用于标记项目中的关键日期，如项目启动日、里程碑日期、截止日期等。在甘特图上，基准线以垂直的虚线形式显示，贯穿整个甘特图区域，帮助用户直观地了解项目中的重要时间点。

本文档详细说明甘特图系统中基准线功能的输入、输出、处理流程和实现细节。

## 基准线功能的输入

### 1. 任务字典中的基准线属性

基准线信息来源于任务数据集合中的`Baseline`属性。每个任务字典可以包含一个基准线日期：

| 属性名 | 数据类型 | 必填 | 说明 |
|--------|----------|------|------|
| Baseline | Date | 否 | 基准线日期，表示需要在甘特图上绘制垂直基准线的日期 |

### 2. DrawBaselineWithBounds函数参数

`DrawBaselineWithBounds`函数是绘制基准线的核心函数，它接收以下输入参数：

| 参数名 | 数据类型 | 说明 |
|--------|----------|------|
| baselineDate | Date | 基准线日期，表示需要绘制基准线的日期 |
| ws | Worksheet | 工作表对象，表示要在哪个工作表上绘制基准线 |
| timelineCoords | Dictionary | 时间轴坐标系信息，包含原点坐标、宽度、起始日期、结束日期和总天数 |
| topRow | Long | 基准线的顶部边界行号，通常为6（甘特图内容的起始行） |
| bottomRow | Long | 基准线的底部边界行号，通常为甘特图的最后一行 |

### 3. 配置表中的基准线格式参数

基准线的格式可以通过配置表进行自定义：

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| GT023 | #FF0000 | 基准线颜色，十六进制RGB颜色代码，默认为红色 |
| GT024 | 2 | 基准线线型，数值，默认为2 (msoLineDash，虚线) |
| GT025 | 1.5 | 基准线线宽，磅值，默认为1.5磅 |

## 基准线功能的输出

### 1. 视觉输出

基准线功能的主要输出是在甘特图上绘制的垂直线形状：

- **形状类型**：直线 (Line)
- **位置**：基于基准线日期计算的X坐标，从甘特图顶部延伸到底部
- **外观**：
  - 颜色：由配置项GT023决定，默认为红色
  - 线型：由配置项GT024决定，默认为虚线
  - 线宽：由配置项GT025决定，默认为1.5磅

### 2. 函数返回值

`DrawBaselineWithBounds`和`DrawBaseline`函数没有显式的返回值，它们是Sub过程而非Function函数。这些函数的主要作用是在工作表上创建基准线形状。

### 3. 日志输出

函数执行过程中会生成以下类型的日志信息：

- **信息日志**：记录基准线绘制的关键步骤，如基准线日期、坐标计算结果等
- **警告日志**：记录非致命性问题，如基准线日期不在时间轴范围内
- **错误日志**：记录致命性问题，如无法创建基准线形状

## 处理流程

基准线的处理流程分为三个主要阶段：

### 1. 收集基准线信息

在`DrawTasksAndMilestones`函数中，系统会在遍历任务集合时同时收集所有基准线信息：

```vba
' 初始化基准线收集变量
Dim baselineCollection As New Collection
Dim baselineDates As New Dictionary ' 用于跟踪已处理的基准线日期

' 遍历每个任务
For i = 1 To tasks.Count
    Set task = tasks(i)
    
    ' 收集基准线信息
    If task.Exists("Baseline") Then
        baselineDate = task("Baseline")
        baselineDateStr = Format(baselineDate, "yyyy-mm-dd")
        
        ' 检查是否已经处理过相同日期的基准线
        If Not baselineDates.Exists(baselineDateStr) Then
            baselineDates.Add baselineDateStr, True
            
            ' 创建基准线信息字典
            Set baselineInfo = New Dictionary
            baselineInfo.Add "Date", baselineDate
            baselineInfo.Add "TaskID", taskId
            
            ' 添加到基准线集合
            baselineCollection.Add baselineInfo
        End If
    End If
Next i
```

这个阶段的关键点：
- 使用字典跟踪已处理的基准线日期，避免重复处理相同日期的基准线
- 将每个唯一的基准线日期及相关信息存储在集合中，供后续统一处理

### 2. 统一处理基准线

在所有任务处理完成后，系统会统一处理所有收集到的基准线：

```vba
' 记录最后一行位置（用于基准线绘制）
Dim lastRowPosition As Long
lastRowPosition = currentRow

' 统一处理所有基准线
If baselineCollection.Count > 0 Then
    For j = 1 To baselineCollection.Count
        Set baseline = baselineCollection(j)
        DrawBaselineWithBounds baseline("Date"), ws, timelineCoords, 6, lastRowPosition
    Next j
End If
```

这个阶段的关键点：
- 使用最后一行位置作为基准线的底部边界
- 对每个基准线调用`DrawBaselineWithBounds`函数进行绘制
- 确保在甘特图的最终行边界确定后再绘制基准线

### 3. 绘制基准线

`DrawBaselineWithBounds`函数负责实际绘制基准线：

```vba
Private Sub DrawBaselineWithBounds(baselineDate As Date, ws As Worksheet, timelineCoords As Dictionary, topRow As Long, bottomRow As Long)
    ' 检查基准线日期是否在时间轴范围内
    If baselineDate < timelineCoords("StartDate") Or baselineDate > timelineCoords("EndDate") Then
        Exit Sub
    End If
    
    ' 计算基准线的X坐标
    Dim baselineX As Double
    baselineX = CalculateXCoordinate(baselineDate, timelineCoords)
    
    ' 计算线的起点和终点坐标
    Dim startY As Double, endY As Double
    startY = ws.Cells(topRow, 1).Top
    endY = ws.Cells(bottomRow, 1).Top + ws.Cells(bottomRow, 1).Height
    
    ' 创建基准线形状
    Dim baselineShape As Shape
    Set baselineShape = ws.Shapes.AddLine(baselineX, startY, baselineX, endY)
    
    ' 设置基准线格式
    With baselineShape.Line
        ' 设置颜色（从配置获取，默认为红色）
        .ForeColor.RGB = GetRGBColor(GetConfigValue("GT023", "#FF0000"))
        
        ' 设置线型（从配置获取，默认为虚线）
        .DashStyle = Val(GetConfigValue("GT024", "2"))
        
        ' 设置线宽（从配置获取，默认为1.5磅）
        .Weight = Val(GetConfigValue("GT025", "1.5"))
    End With
End Sub
```

这个阶段的关键点：
- 检查基准线日期是否在时间轴范围内，如果不在则跳过绘制
- 使用`CalculateXCoordinate`函数计算基准线的X坐标
- 使用传入的上下边界参数计算线的起点和终点Y坐标
- 创建一条垂直线形状
- 根据配置设置线的格式（颜色、线型、线宽）

## 配置参数

基准线功能支持以下配置参数，这些参数可以在Config工作表的configTable中设置：

| 配置项 | 说明 | 默认值 | 可选值 |
|--------|------|--------|--------|
| GT023 | 基准线颜色 | #FF0000 (红色) | 任何有效的十六进制RGB颜色代码 |
| GT024 | 基准线线型 | 2 (虚线) | 1=实线, 2=虚线, 3=点线, 4=点划线, 5=双点划线 |
| GT025 | 基准线线宽 | 1.5 | 任何大于0的数值，单位为磅 |

## 函数说明

### 1. DrawBaselineWithBounds

这是绘制基准线的核心函数，使用明确的上下边界参数。

**函数签名**：
```vba
Private Sub DrawBaselineWithBounds(baselineDate As Date, ws As Worksheet, timelineCoords As Dictionary, topRow As Long, bottomRow As Long)
```

**参数说明**：
- `baselineDate`：基准线日期，表示需要绘制基准线的日期
- `ws`：工作表对象，表示要在哪个工作表上绘制基准线
- `timelineCoords`：时间轴坐标系信息，包含原点坐标、宽度、起始日期、结束日期和总天数
- `topRow`：基准线的顶部边界行号，通常为6（甘特图内容的起始行）
- `bottomRow`：基准线的底部边界行号，通常为甘特图的最后一行

**功能**：
- 检查基准线日期是否在时间轴范围内
- 计算基准线的X坐标
- 使用传入的上下边界参数计算线的起点和终点Y坐标
- 创建一条垂直线形状
- 根据配置设置线的格式（颜色、线型、线宽）

### 2. DrawBaseline

这是为了保持兼容性而保留的原始函数，内部调用`DrawBaselineWithBounds`函数。

**函数签名**：
```vba
Private Sub DrawBaseline(baselineDate As Date, ws As Worksheet, timelineCoords As Dictionary)
```

**参数说明**：
- `baselineDate`：基准线日期，表示需要绘制基准线的日期
- `ws`：工作表对象，表示要在哪个工作表上绘制基准线
- `timelineCoords`：时间轴坐标系信息，包含原点坐标、宽度、起始日期、结束日期和总天数

**功能**：
- 自动确定甘特图的上下边界（顶部为第6行，底部为有内容的最后一行）
- 调用`DrawBaselineWithBounds`函数绘制基准线

## 使用示例

### 1. 在任务数据中添加基准线

```vba
' 创建任务字典
Dim task As New Dictionary
task.Add "ID", "T001"
task.Add "Description", "项目启动"
task.Add "StartDate", #1/15/2023#
task.Add "EndDate", #1/20/2023#
task.Add "Type", "A"
task.Add "Baseline", #1/15/2023# ' 添加基准线日期
```

### 2. 自定义基准线格式

在Config工作表的configTable中设置以下配置项：

```
GT023 | #0000FF | 基准线颜色（蓝色）
GT024 | 1       | 基准线线型（实线）
GT025 | 2       | 基准线线宽（2磅）
```

## 注意事项

1. **基准线日期验证**：
   - 基准线日期必须在时间轴范围内（项目开始日期所在月的第一天到项目结束日期所在月的最后一天）
   - 超出范围的基准线日期将被忽略，不会绘制

2. **重复基准线处理**：
   - 系统会自动检测并跳过重复的基准线日期，确保每个日期只绘制一条基准线
   - 如果多个任务指定了相同的基准线日期，只会绘制一条基准线

3. **基准线绘制时机**：
   - 基准线在所有任务处理完成后统一绘制，确保在甘特图的最终行边界确定后再绘制
   - 这种设计确保基准线能够正确延伸到甘特图底部

4. **配置参数**：
   - 基准线的颜色、线型和线宽可以通过配置表进行自定义
   - 如果配置项不存在或无效，系统会使用默认值

5. **性能考虑**：
   - 提前收集所有基准线信息并统一处理，避免了重复处理相同日期的基准线，提高了效率
   - 使用字典跟踪已处理的日期，确保每个基准线日期只处理一次
