# DrawTasksAndMilestones 函数说明文档

## 功能概述

`DrawTasksAndMilestones` 函数是 Excel VBA 项目管理甘特图系统中的核心函数之一，负责在甘特图工作表上绘制任务条和里程碑图形。该函数接收任务集合作为输入，根据任务类型（任务或里程碑）和属性在甘特图上绘制相应的图形元素，并添加描述标签。此外，该函数还负责绘制基准线，用于显示项目中的重要日期点。

## 优化设计

为了提高代码的效率和可维护性，`DrawTasksAndMilestones` 函数的流程已经进行了优化：

1. **明确的处理步骤**：
   - 提前收集所有基准线信息（避免重复日期的基准线重复处理）
   - 对每个任务执行两个清晰的步骤：
     - 确定任务行位置并处理类别信息（整合在一个函数中）
     - 绘制任务或里程碑
   - 所有任务处理完成后，统一处理所有基准线

2. **模块化设计**：使用整合的函数处理行位置和类别信息：
   - `DetermineTaskRowAndCategory`：负责计算任务的行位置并处理类别信息

3. **避免重复计算**：
   - 时间轴坐标系只创建一次，然后传递给所有需要的函数
   - 基准线信息在任务遍历前提前收集，避免重复日期的基准线重复处理
   - 基准线在所有任务处理完成后统一绘制，确保在甘特图行边界确定后再绘制

4. **动态行高调整**：
   - 在绘制任务和里程碑时自动调整行高，确保所有元素都能正确显示

## 输入参数

| 参数名 | 数据类型   | 说明                                                                               |
| ------ | ---------- | ---------------------------------------------------------------------------------- |
| tasks  | Collection | 包含所有任务和里程碑信息的集合，每个元素是一个 Dictionary 对象，包含任务的各种属性 |

每个任务 Dictionary 对象包含以下关键属性：

| 属性名       | 数据类型 | 说明                                                                                     |
| ------------ | -------- | ---------------------------------------------------------------------------------------- |
| Type         | String   | 任务类型，"A"表示活动/任务，"M"表示里程碑                                                |
| Category     | String   | 任务所属类别                                                                             |
| Description  | String   | 任务描述文本                                                                             |
| StartDate    | Date     | 任务开始日期                                                                             |
| EndDate      | Date     | 任务结束日期（对里程碑来说，通常与开始日期相同）                                         |
| Position     | Variant  | 任务在甘特图上的位置，可以是"same"（与上一任务同行）、"next"（下一行）或数字（指定行数） |
| Color        | String   | 任务或里程碑的颜色（十六进制格式，如"#FF0000"）                                          |
| Progress     | Double   | 任务完成百分比（0-1之间的小数）                                                          |
| TextPosition | String   | 描述文本的位置，可以是"left"、"right"、"top"、"bottom"或"center"                         |
| Baseline     | Date     | 基准线日期，用于在甘特图上绘制垂直的基准线（可选）                                       |

## 输出

函数没有直接返回值，但会在指定的工作表（"GanttChart"）上绘制以下图形元素：

1. 任务类别标题
2. 任务条（矩形）
3. 任务进度条（如果有进度）
4. 里程碑（菱形）
5. 任务和里程碑的描述标签
6. 基准线（垂直的红色虚线，表示重要日期点）

## 处理流程

```mermaid
flowchart TD
    A[开始] --> B[获取GanttChart工作表]
    B --> C{任务集合为空?}
    C -->|是| Z[结束]
    C -->|否| D[获取项目信息]
    D --> E[建立时间轴坐标系]
    E --> F[初始化变量]

    %% 提前收集基准线信息
    F --> G1[创建基准线集合和日期字典]
    G1 --> G2[遍历所有任务]
    G2 --> G3{任务有基准线日期?}
    G3 -->|否| G7[继续下一任务]
    G3 -->|是| G4{日期已处理?}
    G4 -->|是| G5[跳过重复日期]
    G4 -->|否| G6[添加到基准线集合]
    G5 --> G7
    G6 --> G7
    G7 --> G8{还有更多任务?}
    G8 -->|是| G2

    %% 处理任务
    G8 -->|否| H[遍历任务集合]
    H --> I[获取当前任务Dictionary]
    I --> J[第一步: 确定任务行位置并处理类别信息]
    J --> K[调用DetermineTaskRowAndCategory函数]
    K --> N[更新当前行和上一任务行]
    N --> O[第三步: 绘制任务或里程碑]
    O --> P{任务类型?}
    P -->|"A"任务| Q[调用DrawTask绘制任务条]
    P -->|"M"里程碑| R[调用DrawMilestone绘制里程碑]
    Q --> S[继续处理]
    R --> S
    S --> T{还有更多任务?}
    T -->|是| H

    %% 统一处理基准线
    T -->|否| U[遍历基准线集合]
    U --> V[调用DrawBaseline绘制基准线]
    V --> Z[结束]
```

## 子函数调用关系

```mermaid
flowchart LR
    A[DrawTasksAndMilestones] --> B[EstablishTimelineCoordinateSystem]
    A --> C[DetermineTaskRowAndCategory]
    A --> E[DrawTask]
    A --> F[DrawMilestone]
    A --> G[DrawBaseline]
    E --> H[CalculateXCoordinate]
    E --> I[GetRGBColor]
    E --> J[AddTaskLabelWithCoordinates]
    F --> H
    F --> I
    F --> J
    G --> H
    G --> I
```

## 详细处理步骤

1. **初始化和坐标系建立**

   - 获取"GanttChart"工作表引用
   - 检查任务集合是否为空，如果为空则直接返回
   - 获取项目信息（开始日期、结束日期等）
   - 建立时间轴坐标系（只创建一次）
   - 初始化行位置、当前类别和其他控制变量

2. **提前收集基准线信息**

   - 遍历所有任务，提取基准线日期信息
   - 使用字典跟踪已处理的基准线日期，避免重复
   - 将唯一的基准线信息存储在集合中，供后续统一处理

3. **遍历任务集合**

   - 对于每个任务，执行两个明确的步骤：
     - 确定任务行位置并处理类别信息（DetermineTaskRowAndCategory函数）
     - 绘制任务或里程碑（DrawTask或DrawMilestone函数）

4. **统一处理基准线**

   - 在所有任务处理完成后，遍历收集的基准线信息
   - 对每个基准线调用DrawBaseline函数绘制垂直线

5. **确定任务行位置并处理类别信息 (DetermineTaskRowAndCategory函数)**

   - 第一部分：确定任务行位置
     - 获取任务的Position属性
     - 根据Position类型和值确定行位置：
       - 如果是字符串"same"：与上一任务同行
       - 如果是字符串"next"：移动到下一行
       - 如果是数字0：等同于"same"
       - 如果是其他数字：移动指定的行数
     - 确保行位置不小于最小行位置（第6行）

   - 第二部分：处理类别信息
     - 获取任务的Category属性
     - 检查是否是新行（resultRow与currentRow不同）或首个任务（taskIndex = 1）
     - 如果是新行或首个任务，重置行类别设置状态
     - 如果当前行的类别尚未设置且任务有非空类别：
       - 设置当前类别并标记当前行类别已设置
       - 如果是新行或首个任务，添加类别行标题

6. **绘制任务或里程碑**

   - 根据任务的Type属性决定调用哪个绘制函数：
     - 如果Type是"A"：调用DrawTask函数绘制任务条
     - 如果Type是"M"：调用DrawMilestone函数绘制里程碑
   - 传递任务、行位置和时间轴坐标系给绘制函数

7. **DrawTask函数处理**

   - 获取任务的开始日期和结束日期
   - 使用传入的坐标系统计算任务在时间轴上的位置
   - 创建任务条形状（矩形）
   - 设置任务条的颜色和边框
   - 如果任务有进度，添加进度条
   - 添加任务描述标签
   - 动态调整行高以适应任务和标签

8. **DrawMilestone函数处理**

   - 获取里程碑的日期
   - 使用传入的坐标系统计算里程碑在时间轴上的位置
   - 创建里程碑形状（菱形）
   - 设置里程碑的颜色和边框
   - 添加里程碑描述标签
   - 动态调整行高以适应里程碑和标签

9. **处理基准线 (DrawBaselineWithBounds函数)**

   - 在所有任务处理完成后，遍历收集的基准线信息集合
   - 记录最后一行位置（用于基准线绘制的底部边界）
   - 检查基准线日期是否在时间轴范围内
   - 使用传入的坐标系统计算基准线的X坐标
   - 使用明确的上下边界（顶部为第6行，底部为最后一行位置）
   - 创建一条垂直线形状
   - 设置线的格式（颜色、线型、线宽）

## 数据流图

```mermaid
flowchart TD
    A[任务集合] --> B[DrawTasksAndMilestones]
    C[项目信息] --> B
    B --> D[EstablishTimelineCoordinateSystem]
    D --> E[时间轴坐标系]
    B --> F[DetermineTaskRow]
    B --> G[ProcessTaskCategory]
    B --> H[DrawTask]
    B --> I[DrawMilestone]
    B --> J[DrawBaselineWithBounds]
    E --> H
    E --> I
    E --> J
    F --> K[任务行位置]
    G --> L[任务类别标题]
    H --> M[任务条形状]
    H --> N[进度条形状]
    I --> O[里程碑形状]
    J --> P[基准线形状]
    M --> Q[AddTaskLabelWithCoordinates]
    N --> Q
    O --> Q
    Q --> R[描述标签文本框]
```

## 错误处理

函数包含完整的错误处理机制：

- 使用 On Error GoTo ErrorHandler 捕获可能的错误
- 在错误处理部分记录错误信息并重新引发错误
- 在创建形状时使用 On Error Resume Next 单独处理可能的形状创建错误

## 注意事项

1. 函数依赖于正确设置的时间轴，时间轴必须在调用此函数前创建
2. 日期到坐标的映射使用项目开始日期所在月份的第一天作为时间轴起点，与CreateTimeline函数保持一致
3. 任务的日期如果超出时间轴范围，会被自动调整到时间轴的边界
4. 任务的颜色代码必须是有效的十六进制格式（如"#FF0000"）
5. 函数使用 Shape 对象绘制图形元素，这些对象会保留在工作表上，直到被明确删除
6. 基准线的绘制依赖于任务的Baseline属性，该属性是可选的
7. 基准线的格式（颜色、线型、线宽）可通过配置项（GT023、GT024、GT025）进行自定义
8. 如果基准线日期超出时间轴范围，该基准线将被跳过不绘制
9. 在 `ProcessTaskCategory` 函数中，特别处理了首个任务（行为6）的情况，将其视为"新行"，以确保首个任务的类别标题能够正确显示
10. 基准线信息在任务遍历前提前收集，避免重复日期的基准线重复处理
11. 基准线在所有任务处理完成后统一绘制，使用最后一行位置作为底部边界，确保基准线能够正确延伸到甘特图底部
12. 新增的 `DrawBaselineWithBounds` 函数接受明确的上下边界参数，使基准线绘制更加精确

## 日期到坐标映射说明

在甘特图中，日期到坐标的映射是通过 `EstablishTimelineCoordinateSystem` 和 `CalculateXCoordinate` 函数实现的：

### EstablishTimelineCoordinateSystem 函数

该函数建立时间轴坐标系，返回包含以下信息的字典：

1. StartDate：坐标系的起始日期（项目开始日期所在月份的第一天）
2. EndDate：坐标系的结束日期（项目结束日期所在月份的最后一天）
3. OriginX：坐标系原点的X坐标（C5单元格的左边缘）
4. Width：坐标系的总宽度（从原点到时间轴最后一列的右边缘）
5. TotalDays：坐标系覆盖的总天数

### CalculateXCoordinate 函数

该函数将日期映射到X坐标：

1. 计算目标日期与起始日期的天数差（并加1进行修正）
2. 计算这个天数差在总天数中的比例
3. 根据这个比例计算在总宽度中的偏移量
4. 将原点坐标和偏移量相加，得到最终的X坐标

这种基于坐标系的映射方法相比旧的基于列的方法有以下优势：

1. 更高的精度：可以精确定位到像素级别，而不仅仅是列级别
2. 更好的视觉效果：任务和里程碑的位置与其日期更准确对应
3. 更灵活的布局：可以处理不同时间单位（日、周、月）的显示
