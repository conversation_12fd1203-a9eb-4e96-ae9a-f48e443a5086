# Project Management Gantt Chart System - 系统设计文档 (V1.0)

## 1. 引言

### 1.1 目的

本文档旨在详细描述Excel VBA项目管理甘特图系统（第一版）的设计方案，包括数据结构、模块设计、算法设计和用户界面设计等方面，为系统实现提供详细指导。本文档反映了系统的实际实现状态，与代码保持一致。

### 1.2 范围

本设计文档涵盖系统的整体架构、数据模型、模块设计、界面设计和算法设计等方面，主要针对第一版系统的开发。文档包含了所有已实现的功能和模块，包括调试系统、配置系统、甘特图生成和聚光灯效果等。

### 1.3 参考文档

- 《Project Management Gantt Chart System - 需求规格说明书 (V1.0)》
- Excel VBA编程指南
- Excel对象模型参考
- 《Project Management Gantt Chart System - 系统架构设计》
- 《Project Management Gantt Chart System - 数据模型设计》
- 《Project Management Gantt Chart System - 用户界面设计》

## 2. 系统架构设计

### 2.1 整体架构

系统采用模块化设计，分为数据层、业务逻辑层和表现层三个主要部分，并包含横切关注点（Cross-cutting Concerns）如调试和工具模块：

```mermaid
flowchart TD
    subgraph 表现层
        UI[用户界面模块\nmodUI]
        Ribbon[Ribbon界面模块\nmodRibbon]
        ThisWB[ThisWorkbook类]
    end

    subgraph 业务逻辑层
        Main[主控模块\nmodMain]
        Gantt[甘特图模块\nmodGantt]
        GanttSpotlight[甘特图聚光灯模块\nmodGanttSpotlight]
    end

    subgraph 数据层
        Data[数据模块\nmodData]
        ConfigDefaults[配置默认值模块\nmodConfigDefaults]
    end

    subgraph 横切关注点
        Debug[调试模块\nmodDebug]
        Utilities[工具模块\nmodUtilities]
    end

    Main --> UI
    Main --> Gantt
    Main --> Data
    Main --> GanttSpotlight
    Main --> Debug

    Ribbon --> Main
    ThisWB --> GanttSpotlight

    Gantt --> Data
    Gantt --> UI
    Gantt --> Utilities

    UI --> Data
    UI --> Utilities

    GanttSpotlight --> Data
    GanttSpotlight --> Utilities

    Data --> ConfigDefaults
    Data --> Debug
    Data --> Utilities

    Debug --> Data
    Debug --> Utilities
```

### 2.2 模块职责

#### 2.2.1 表现层

- **用户界面模块 (modUI)**：负责工作表准备、表头设置、网格线设置、主题应用和配置预览
- **Ribbon界面模块 (modRibbon)**：负责处理Ribbon界面上的按钮点击事件，调用主控模块的相应功能
- **ThisWorkbook类**：负责处理工作簿事件，包括工作簿打开、关闭和工作表选择变化事件

#### 2.2.2 业务逻辑层

- **主控模块 (modMain)**：系统入口，负责协调其他模块的工作，控制甘特图生成的整体流程
- **甘特图模块 (modGantt)**：负责甘特图的生成和管理，包括时间轴创建、任务绘制和标签添加
- **甘特图聚光灯模块 (modGanttSpotlight)**：负责实现甘特图的聚光灯效果，高亮显示当前选中的行和/或列

#### 2.2.3 数据层

- **数据模块 (modData)**：负责数据处理、验证和管理，包括项目信息获取、任务数据获取和配置访问
- **配置默认值模块 (modConfigDefaults)**：负责提供所有配置项的默认值，支持配置系统的初始化和回退

#### 2.2.4 横切关注点

- **调试模块 (modDebug)**：负责日志记录、错误处理、函数跟踪和性能监控
- **工具模块 (modUtilities)**：负责提供通用工具函数，包括日期处理、单元格操作、颜色处理和错误处理

### 2.3 命名规范

- **模块名称**：使用"mod"前缀，如modMain、modData
- **函数和过程**：使用"驼峰法"，如GenerateGanttChart、ValidateData
- **变量**：使用"驼峰法"，如projectInfo、taskTable
- **常量**：使用全大写，如DEBUG_LEVEL_ERROR、DEFAULT_TASK_COLOR
- **参数**：使用"驼峰法"，如startDate、excludeWeekends

## 3. 数据模型设计

### 3.1 工作表结构

#### 3.1.1 项目信息工作表

项目信息工作表采用普通表格格式，存储项目的基本信息。与超级表不同，这里使用预定义的单元格区域，并通过定义名称来引用这些区域，便于后续代码访问。

**工作表名称**: `ProjectInfo`

| 字段名          | 数据类型 | 说明               | 是否必填 | 定义名称           |
| --------------- | -------- | ------------------ | -------- | ------------------ |
| Project Name    | 文本     | 项目的名称         | 是       | projectName        |
| Project Manager | 文本     | 负责项目的经理姓名 | 是       | projectManager     |
| Start Date      | 日期     | 项目的开始日期     | 是       | projectStartDate   |
| End Date        | 日期     | 项目的结束日期     | 是       | projectEndDate     |
| Description     | 文本     | 项目的简要描述     | 否       | projectDescription |

#### 3.1.2 任务/里程碑工作表

**工作表名称**: `Milestones&WBS`
**超级表名称**: `taskTable`

| 字段名        | 数据类型  | 说明                                          | 是否必填 | 定义名称         |
| ------------- | --------- | --------------------------------------------- | -------- | ---------------- |
| ID            | 文本/数字 | 任务/里程碑的唯一标识符（可通过公式自动生成） | 是       | taskId           |
| Category      | 文本      | 所属父任务的大类，如"项目启动"                | 是       | taskCategory     |
| Description   | 文本      | 任务/里程碑的描述                             | 是       | taskDescription  |
| Type          | 文本      | A/M（A:activity任务活动，M:milestone里程碑）  | 是       | taskType         |
| Start Date    | 日期      | 任务开始的时间                                | 是       | taskStartDate    |
| End Date      | 日期      | 任务结束的时间（里程碑只参照开始日期）        | 条件必填 | taskEndDate      |
| Duration      | 数字      | 自动计算（天）                                | 自动计算 | taskDuration     |
| Progress      | 百分比    | 完成百分比（0-100%）                          | 否       | taskProgress     |
| Position      | 文本/数字 | 相对于上一行的位置（same/next/数字）          | 否       | taskPosition     |
| Color         | 文本/数字 | 任务条/里程碑的填充颜色                       | 否       | taskColor        |
| Text Position | 文本      | 文字相对于任务条/里程碑的位置                 | 否       | taskTextPosition |

#### 3.1.3 甘特图工作表

甘特图工作表在每次生成时重新创建，用于展示可视化甘特图。

**工作表名称**: `GanttChart`

| 区域     | 说明                                 |
| -------- | ------------------------------------ |
| B1       | 项目名称（大字体,加粗）              |
| B2       | 项目经理信息（小字体）               |
| B3-B5    | 最近更新时间和计划版本（合并单元格） |
| C3行起   | 年份(yyyy)                           |
| C4行起   | 月份(mm)                             |
| C5行起   | 周数(cwxx)                           |
| B6行起   | 任务/里程碑所属大类（粗体）          |
| 任务区域 | 根据任务/里程碑数据生成的甘特图元素  |

#### 3.1.4 配置工作表

配置工作表存储系统配置参数，用于自定义系统行为和外观。系统使用配置默认值模块提供默认配置，当配置表中没有相应配置或配置未启用时，使用默认值。

**工作表名称**: `Config`, 配置表为超级表名称为 `configTable`

| 字段名      | 数据类型  | 说明                                 | 是否必填 |
| ----------- | --------- | ------------------------------------ | -------- |
| Module      | 文本      | 所属模块（UI/Gantt/Data/Debug等）    | 是       |
| ConfigName  | 文本      | 配置项的名称                         | 是       |
| ConfigValue | 文本/数字 | 配置项的值                           | 是       |
| IsEnabled   | 布尔值    | 是否启用该配置                       | 是       |
| Description | 文本      | 配置项的描述                         | 否       |

**配置访问方式**:

系统提供三种配置访问方式：

1. **GetConfig**：直接获取单个配置项的值
   ```vba
   ' 获取配置值，支持默认值
   value = GetConfig("CellWidthFactor", 1.2)
   ```

2. **GetModuleConfig**：获取特定模块的所有配置
   ```vba
   ' 获取模块配置，支持默认值字典
   Dim config As Dictionary
   Set config = GetModuleConfig("Gantt", modConfigDefaults.GetDefaults())
   ```

3. **GetConfigFromDict**：从配置字典中获取值
   ```vba
   ' 从配置字典中获取值，支持默认值
   value = GetConfigFromDict(config, "CellWidthFactor", 1.2)
   ```

**配置默认值**:

系统使用modConfigDefaults模块提供所有配置项的默认值，主要包括以下类别：

1. **UI相关配置**：
   - 项目名称区域：字体、大小、颜色、边框等
   - 项目经理区域：字体、大小、颜色等
   - 时间轴区域：字体、大小、颜色、背景色等
   - 全局主题：网格线、背景色、交替行颜色等
   - Logo相关：启用/禁用、边距等

2. **Gantt相关配置**：
   - 布局：单元格宽度系数、任务位置、文字位置、任务条高度等
   - 颜色：任务条颜色、里程碑颜色、进度条颜色等
   - 样式：当前日期线、基准线、任务条边框等
   - 聚光灯效果：启用/禁用、模式、颜色等
   - 甘特图边框：启用/禁用、颜色、粗细、样式等

3. **Data相关配置**：
   - 计算选项：自动计算持续时间、排除周末等
   - 默认值：默认任务进度等

4. **Debug相关配置**：
   - 调试模式：启用/禁用
   - 日志级别：错误、警告、信息、详细等
   - 日志输出：文件日志、即时窗口输出等

**配置示例**:

| Module | ConfigName           | ConfigValue | IsEnabled | Description                  |
| ------ | -------------------- | ----------- | --------- | ---------------------------- |
| UI     | ProjectNameFont      | Barlow      | TRUE      | 项目名称字体                 |
| UI     | ProjectNameFontSize  | 18          | TRUE      | 项目名称字号                 |
| UI     | ProjectNameBackColor | #EB5A50     | TRUE      | 项目名称背景颜色             |
| Gantt  | CellWidthFactor      | 0.8         | TRUE      | 甘特图时间单元格宽度放大系数 |
| Gantt  | DefaultTaskColor     | #3366CC     | TRUE      | 默认任务条颜色               |
| Gantt  | EnableSpotlight      | TRUE        | TRUE      | 是否启用聚光灯效果           |
| Gantt  | SpotlightMode        | all         | TRUE      | 聚光灯模式                   |
| Data   | AutoCalculateDuration| TRUE        | TRUE      | 是否自动计算任务持续时间     |
| Debug  | EnableDebug          | FALSE       | TRUE      | 是否启用调试模式             |
| Debug  | DebugLevel           | 4           | TRUE      | 调试日志级别                 |

### 3.2 数据验证规则

#### 3.2.1 项目信息验证

- 项目名称不能为空
- 项目经理不能为空
- 开始日期和结束日期必须是有效日期
- 开始日期不能晚于结束日期

#### 3.2.2 任务/里程碑验证

- 类型不能为空
- 属性必须是"A"或"M"
- 描述不能为空
- 开始日期必须是有效日期
- 如果属性为"A"（活动），结束日期必须是有效日期且不早于开始日期
- 如果属性为"M"（里程碑），结束日期应等于开始日期
- 进度值必须在0-100%之间
- 位置值必须是"same"、"next"或有效整数数字,如果留空则默认 `next`
- 文字位置必须是有效值（里程碑：left/right/top/bottom/；任务条：left/right/top/bottom/center）,如果留空则默认 `right`

## 4. 模块设计

### 4.1 Main模块

Main模块是系统的入口点和控制中心，负责协调其他模块的工作，处理主要事件和错误。

#### 4.1.1 模块结构

```mermaid
classDiagram
    class modMain {
        +GenerateGanttChart()
        +HandleError(errNumber, errDescription, errSource)
        -ValidateData() Boolean
        -PrepareGanttSheet()
        -CreateGanttChart()
        -ApplyGanttTheme()
        -InitializeGanttSpotlight()
    }
```

#### 4.1.2 模块流程

下图展示Main模块的主要流程：

```mermaid
flowchart TD
    Start([开始]) --> InitDebug[初始化调试模块]
    InitDebug --> ValidateData{验证数据}
    ValidateData -->|验证失败| ShowError[显示错误消息]
    ValidateData -->|验证成功| PrepareGanttSheet[准备甘特图工作表]

    PrepareGanttSheet --> CreateGanttChart[创建甘特图]
    CreateGanttChart --> ApplyGanttTheme[应用甘特图主题]
    ApplyGanttTheme --> InitSpotlight[初始化聚光灯效果]
    InitSpotlight --> ShowSuccess[显示成功消息]

    ShowError --> CloseDebug[关闭调试日志]
    ShowSuccess --> CloseDebug
    CloseDebug --> End([结束])
```

#### 4.1.3 主要函数和过程

```vba
' 生成甘特图的主函数
Public Sub GenerateGanttChart()
    On Error GoTo ErrorHandler

    ' 初始化调试模块
    Dim debugLevel As Integer
    Dim enableDebug As Boolean
    Dim enableFileLogging As Boolean
    Dim enableImmediateOutput As Boolean

    ' 获取配置值
    debugLevel = CInt(GetConfig("DebugLevel", 4))
    enableDebug = CBool(GetConfig("EnableDebug", False))
    enableFileLogging = CBool(GetConfig("EnableFileLogging", False))
    enableImmediateOutput = CBool(GetConfig("EnableImmediateOutput", False))

    ' 初始化调试系统
    modDebug.InitDebug _
        level:=debugLevel, _
        enableDebug:=enableDebug, _
        enableFileLogging:=enableFileLogging, _
        enableImmediateOutput:=enableImmediateOutput

    ' 记录函数进入
    modDebug.LogFunctionEntry "modMain.GenerateGanttChart"

    ' 关闭屏幕更新以提高性能
    Application.ScreenUpdating = False
    Application.EnableEvents = False
    Application.Calculation = xlCalculationManual

    ' 1. 验证项目信息和任务数据
    modDebug.LogInfo "步骤1: 开始验证数据", "modMain.GenerateGanttChart"
    If Not ValidateData() Then
        modDebug.LogWarning "数据验证失败，退出生成过程", "modMain.GenerateGanttChart"
        GoTo CleanExit
    End If
    modDebug.LogInfo "数据验证成功", "modMain.GenerateGanttChart"

    ' 2. 准备甘特图工作表
    modDebug.LogInfo "步骤2: 开始准备甘特图工作表", "modMain.GenerateGanttChart"
    PrepareGanttSheet
    modDebug.LogInfo "甘特图工作表准备完成", "modMain.GenerateGanttChart"

    ' 3. 创建甘特图
    modDebug.LogInfo "步骤3: 开始创建甘特图", "modMain.GenerateGanttChart"
    CreateGanttChart
    modDebug.LogInfo "甘特图创建完成", "modMain.GenerateGanttChart"

    ' 4. 应用甘特图主题
    modDebug.LogInfo "步骤4: 开始应用甘特图主题", "modMain.GenerateGanttChart"
    ApplyGanttTheme
    modDebug.LogInfo "甘特图主题应用完成", "modMain.GenerateGanttChart"

    ' 5. 初始化甘特图聚光灯效果
    modDebug.LogInfo "步骤5: 初始化甘特图聚光灯效果", "modMain.GenerateGanttChart"
    InitializeGanttSpotlight
    modDebug.LogInfo "甘特图聚光灯效果初始化完成", "modMain.GenerateGanttChart"

    ' 6. 显示成功消息
    modDebug.LogInfo "步骤6: 显示成功消息", "modMain.GenerateGanttChart"
    MsgBox "甘特图生成成功！", vbInformation, "成功"

CleanExit:
    ' 恢复屏幕更新
    Application.ScreenUpdating = True
    Application.EnableEvents = True
    Application.Calculation = xlCalculationAutomatic

    ' 记录函数退出
    modDebug.LogFunctionExit "modMain.GenerateGanttChart"
    Exit Sub

ErrorHandler:
    ' 处理错误
    HandleError Err.Number, Err.Description, "modMain.GenerateGanttChart"
    Resume CleanExit
End Sub

' 验证数据的函数
Private Function ValidateData() As Boolean
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modMain.ValidateData"

    ' 调用Data模块的验证函数
    ValidateData = modData.ValidateAllData()

    ' 记录函数退出
    modDebug.LogFunctionExit "modMain.ValidateData", "结果: " & ValidateData
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modMain.ValidateData"
    ValidateData = False
    modDebug.LogFunctionExit "modMain.ValidateData", "错误: " & Err.Description
End Function

' 错误处理函数
Public Sub HandleError(errNumber As Long, errDescription As String, errSource As String)
    On Error Resume Next

    ' 记录错误
    modDebug.LogError errNumber, errDescription, errSource

    ' 显示用户友好的错误消息
    MsgBox "发生错误: " & errDescription, vbExclamation, "错误"
End Sub
```

### 4.2 Data模块

Data模块负责数据处理、验证和管理。

#### 4.2.0 模块流程

##### 数据验证流程

```mermaid
flowchart TD
    Start([开始]) --> ValidateAllData[验证所有数据]

    ValidateAllData --> ValidateProjectInfo{验证项目信息}
    ValidateProjectInfo -->|验证失败| ReturnFalse[返回False]
    ValidateProjectInfo -->|验证成功| ValidateTasksData{验证任务数据}

    ValidateTasksData -->|验证失败| ReturnFalse
    ValidateTasksData -->|验证成功| ReturnTrue[返回True]

    ReturnFalse --> End([结束])
    ReturnTrue --> End
```

##### 配置访问流程

```mermaid
flowchart TD
    Start([开始]) --> GetConfigValue[获取配置值]

    GetConfigValue --> GetConfigTable[获取配置表引用]
    GetConfigTable --> CheckTableData{表格有数据?}

    CheckTableData -->|否| ReturnDefault[返回默认值]
    CheckTableData -->|是| FindConfigID[查找配置ID]

    FindConfigID --> ConfigFound{找到配置?}
    ConfigFound -->|否| ReturnDefault
    ConfigFound -->|是| ReturnValue[返回配置值]

    ReturnDefault --> End([结束])
    ReturnValue --> End
```

#### 4.2.1 模块结构

```mermaid
classDiagram
    class modData {
        +ValidateAllData() Boolean
        +GetProjectInfo() Dictionary
        +GetAllTasks(outBaselineCollection) Collection
        +GetModuleConfig(moduleName, defaultValues) Dictionary
        +GetConfigFromDict(dict, configName, defaultValue) Variant
        +GetConfig(configName, defaultValue) Variant
        -ValidateProjectInfo(errorMessages, errorCells, errorCount) Boolean
        -ValidateTasksData(errorMessages, errorCells, errorCount) Boolean
        -ClearAllValidationMarks()
        -ClearValidationMarks(range)
    }
```

#### 4.2.2 配置访问函数

```vba
' 获取特定模块的所有配置，使用ConfigName作为键，并支持默认值
Public Function GetModuleConfig(moduleName As String, Optional defaultValues As Dictionary = Nothing) As Dictionary
    On Error GoTo ErrorHandler

    Dim result As New Dictionary
    Dim tbl As ListObject
    Dim dataArray As Variant
    Dim i As Long, moduleCol As Long, enabledCol As Long, nameCol As Long, valueCol As Long

    ' 记录函数进入
    modDebug.LogFunctionEntry "modData.GetModuleConfig"

    ' 获取配置表引用
    Set tbl = ThisWorkbook.Worksheets("Config").ListObjects("configTable")

    ' 如果表格没有数据，返回默认值字典或空字典
    If tbl.DataBodyRange Is Nothing Then
        If Not defaultValues Is Nothing Then
            Set GetModuleConfig = defaultValues
            modDebug.LogInfo "配置表为空，使用默认值字典", "modData.GetModuleConfig"
        Else
            Set GetModuleConfig = result
            modDebug.LogInfo "配置表为空，返回空字典", "modData.GetModuleConfig"
        End If
        modDebug.LogFunctionExit "modData.GetModuleConfig"
        Exit Function
    End If

    ' 获取列索引
    moduleCol = tbl.ListColumns("Module").Index
    enabledCol = tbl.ListColumns("IsEnabled").Index
    nameCol = tbl.ListColumns("ConfigName").Index
    valueCol = tbl.ListColumns("ConfigValue").Index

    ' 一次性读取整个数据区域到数组
    dataArray = tbl.DataBodyRange.Value

    ' 计算数组中的相对列索引
    Dim moduleColArray As Long, enabledColArray As Long, nameColArray As Long, valueColArray As Long
    Dim firstColIndex As Long

    ' 获取ListObject第一列的索引
    firstColIndex = tbl.ListColumns(1).Index

    ' 计算数组中的相对列索引
    moduleColArray = moduleCol - firstColIndex + 1
    enabledColArray = enabledCol - firstColIndex + 1
    nameColArray = nameCol - firstColIndex + 1
    valueColArray = valueCol - firstColIndex + 1

    modDebug.LogVerbose "列索引映射 - Module: " & moduleCol & "->" & moduleColArray & _
                       ", Enabled: " & enabledCol & "->" & enabledColArray & _
                       ", Name: " & nameCol & "->" & nameColArray & _
                       ", Value: " & valueCol & "->" & valueColArray, _
                       "modData.GetModuleConfig"

    ' 遍历数组处理数据
    For i = LBound(dataArray, 1) To UBound(dataArray, 1)
        ' 如果模块名称匹配且配置已启用
        If dataArray(i, moduleColArray) = moduleName And dataArray(i, enabledColArray) = True Then
            Dim configName As String
            Dim configValue As Variant

            configName = dataArray(i, nameColArray)
            configValue = dataArray(i, valueColArray)

            ' 使用ConfigName作为键
            result.Add configName, configValue
        End If
    Next i

    ' 合并默认值
    If Not defaultValues Is Nothing Then
        For Each key In defaultValues.Keys
            If Not result.Exists(key) Then
                result.Add key, defaultValues(key)
                modDebug.LogVerbose "配置项 " & key & " 在配置表中不存在，使用默认值: " & defaultValues(key), "modData.GetModuleConfig"
            End If
        Next key
    End If

    Set GetModuleConfig = result
    modDebug.LogFunctionExit "modData.GetModuleConfig", "找到 " & result.Count & " 个配置项"
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modData.GetModuleConfig"

    ' 如果出错且提供了默认值字典，返回默认值字典
    If Not defaultValues Is Nothing Then
        Set GetModuleConfig = defaultValues
    Else
        Set GetModuleConfig = New Dictionary ' 返回空字典
    End If
    modDebug.LogFunctionExit "modData.GetModuleConfig", "错误: " & Err.Description
End Function

' 从配置字典中获取值，支持默认值
Public Function GetConfigFromDict(dict As Dictionary, configName As String, Optional defaultValue As Variant = Null) As Variant
    On Error GoTo ErrorHandler

    If dict.Exists(configName) Then
        GetConfigFromDict = dict(configName)
    Else
        GetConfigFromDict = defaultValue
        modDebug.LogVerbose "配置项 " & configName & " 在字典中不存在，使用默认值: " & defaultValue, "modData.GetConfigFromDict"
    End If

    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modData.GetConfigFromDict"
    GetConfigFromDict = defaultValue
End Function

' 直接获取配置项的值，不考虑模块，支持默认值
Public Function GetConfig(configName As String, Optional defaultValue As Variant = Null) As Variant
    On Error GoTo ErrorHandler

    Dim tbl As ListObject
    Dim dataArray As Variant
    Dim i As Long, enabledCol As Long, nameCol As Long, valueCol As Long

    ' 记录函数进入
    modDebug.LogFunctionEntry "modData.GetConfig"

    ' 获取配置表引用
    Set tbl = ThisWorkbook.Worksheets("Config").ListObjects("configTable")

    ' 如果表格没有数据，返回默认值
    If tbl.DataBodyRange Is Nothing Then
        GetConfig = defaultValue
        modDebug.LogInfo "配置表为空，使用默认值: " & defaultValue, "modData.GetConfig"
        modDebug.LogFunctionExit "modData.GetConfig"
        Exit Function
    End If

    ' 获取列索引
    enabledCol = tbl.ListColumns("IsEnabled").Index
    nameCol = tbl.ListColumns("ConfigName").Index
    valueCol = tbl.ListColumns("ConfigValue").Index

    ' 一次性读取整个数据区域到数组
    dataArray = tbl.DataBodyRange.Value

    ' 计算数组中的相对列索引
    Dim enabledColArray As Long, nameColArray As Long, valueColArray As Long
    Dim firstColIndex As Long

    ' 获取ListObject第一列的索引
    firstColIndex = tbl.ListColumns(1).Index

    ' 计算数组中的相对列索引
    enabledColArray = enabledCol - firstColIndex + 1
    nameColArray = nameCol - firstColIndex + 1
    valueColArray = valueCol - firstColIndex + 1

    ' 遍历数组处理数据
    For i = LBound(dataArray, 1) To UBound(dataArray, 1)
        ' 如果配置名称匹配且配置已启用
        If dataArray(i, nameColArray) = configName And dataArray(i, enabledColArray) = True Then
            GetConfig = dataArray(i, valueColArray)
            modDebug.LogVerbose "找到配置项 " & configName & "，值为: " & GetConfig, "modData.GetConfig"
            modDebug.LogFunctionExit "modData.GetConfig"
            Exit Function
        End If
    Next i

    ' 如果未找到配置项，返回默认值
    GetConfig = defaultValue
    modDebug.LogVerbose "配置项 " & configName & " 未找到，使用默认值: " & defaultValue, "modData.GetConfig"
    modDebug.LogFunctionExit "modData.GetConfig"
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modData.GetConfig"
    GetConfig = defaultValue
    modDebug.LogFunctionExit "modData.GetConfig", "错误: " & Err.Description
End Function
```

#### 4.2.1 主要函数和过程

```vba
' 验证所有数据
Public Function ValidateAllData() As Boolean
    ' 1. 验证项目信息
    If Not ValidateProjectInfo() Then
        ValidateAllData = False
        Exit Function
    End If

    ' 2. 验证任务/里程碑数据
    If Not ValidateTasksData() Then
        ValidateAllData = False
        Exit Function
    End If

    ValidateAllData = True
End Function

' 验证项目信息
Private Function ValidateProjectInfo() As Boolean
    ' 实现项目信息验证逻辑
    Dim projectName As String
    Dim startDate As Date
    Dim endDate As Date

    ' 使用定义名称获取项目信息
    projectName = Range("projectName").Value

    ' 检查项目名称是否为空
    If Trim(projectName) = "" Then
        MsgBox "Project name cannot be empty.", vbExclamation, "Validation Error"
        ValidateProjectInfo = False
        Exit Function
    End If

    ' 检查日期有效性
    On Error Resume Next
    startDate = Range("projectStartDate").Value
    endDate = Range("projectEndDate").Value
    On Error GoTo 0

    ' 检查日期关系
    If startDate > endDate Then
        MsgBox "Start date cannot be later than end date.", vbExclamation, "Validation Error"
        ValidateProjectInfo = False
        Exit Function
    End If

    ValidateProjectInfo = True
End Function

' 验证任务/里程碑数据
Private Function ValidateTasksData() As Boolean
    ' 实现任务/里程碑数据验证逻辑
    ' ...
End Function

' 获取项目信息
Public Function GetProjectInfo() As Dictionary
    Dim projectInfo As New Dictionary

    ' 使用定义名称获取项目信息
    projectInfo.Add "ProjectName", Range("projectName").Value
    projectInfo.Add "ProjectManager", Range("projectManager").Value
    projectInfo.Add "StartDate", Range("projectStartDate").Value
    projectInfo.Add "EndDate", Range("projectEndDate").Value
    projectInfo.Add "Description", Range("projectDescription").Value

    Set GetProjectInfo = projectInfo
End Function

' 获取所有任务/里程碑
Public Function GetAllTasks() As Collection
    Dim tasks As New Collection
    Dim task As Dictionary
    Dim tbl As ListObject
    Dim dataRange As Range
    Dim i As Long

    ' 获取超级表引用
    Set tbl = ThisWorkbook.Worksheets("Milestones&WBS").ListObjects("taskTable")

    ' 如果表格没有数据，返回空集合
    If tbl.DataBodyRange Is Nothing Then
        Set GetAllTasks = tasks
        Exit Function
    End If

    ' 获取数据范围
    Set dataRange = tbl.DataBodyRange

    ' 遍历每一行数据
    For i = 1 To dataRange.Rows.Count
        Set task = New Dictionary

        ' 使用超级表结构化引用获取数据
        task.Add "ID", dataRange.Cells(i, tbl.ListColumns("ID").Index).Value
        task.Add "Category", dataRange.Cells(i, tbl.ListColumns("Category").Index).Value
        task.Add "Description", dataRange.Cells(i, tbl.ListColumns("Description").Index).Value
        task.Add "Type", dataRange.Cells(i, tbl.ListColumns("Type").Index).Value
        task.Add "StartDate", dataRange.Cells(i, tbl.ListColumns("Start Date").Index).Value
        task.Add "EndDate", dataRange.Cells(i, tbl.ListColumns("End Date").Index).Value
        task.Add "Duration", dataRange.Cells(i, tbl.ListColumns("Duration").Index).Value
        task.Add "Progress", dataRange.Cells(i, tbl.ListColumns("Progress").Index).Value
        task.Add "Position", dataRange.Cells(i, tbl.ListColumns("Position").Index).Value
        task.Add "Color", dataRange.Cells(i, tbl.ListColumns("Color").Index).Value
        task.Add "TextPosition", dataRange.Cells(i, tbl.ListColumns("Text Position").Index).Value

        tasks.Add task
    Next i

    Set GetAllTasks = tasks
End Function
```

### 4.3 Gantt模块

Gantt模块负责甘特图的生成和管理。

#### 4.3.0 模块流程

##### 甘特图生成流程

```mermaid
flowchart TD
    Start([开始]) --> CreateGanttChart[创建甘特图]

    CreateGanttChart --> GetProjectInfo[获取项目信息]
    GetProjectInfo --> GetAllTasks[获取所有任务]

    GetAllTasks --> CreateGanttHeader[创建甘特图表头]
    CreateGanttHeader --> CreateTimeline[创建时间轴]
    CreateTimeline --> DrawTasksAndMilestones[绘制任务和里程碑]

    DrawTasksAndMilestones --> End([结束])
```

##### 时间轴生成流程

```mermaid
flowchart TD
    Start([开始]) --> CreateTimeline[创建时间轴]

    CreateTimeline --> CalculateTimeRange[计算时间范围]
    CalculateTimeRange --> GenerateYearRow[生成年份行]
    GenerateYearRow --> GenerateMonthRow[生成月份行]
    GenerateMonthRow --> GenerateWeekRow[生成周数行]

    GenerateWeekRow --> MergeCells[合并相同单元格]
    MergeCells --> FormatTimeline[格式化时间轴]

    FormatTimeline --> End([结束])
```

##### 任务绘制流程

```mermaid
flowchart TD
    Start([开始]) --> DrawTasksAndMilestones[绘制任务和里程碑]

    DrawTasksAndMilestones --> GetTasksData[获取任务数据]
    GetTasksData --> InitializeRowPosition[初始化行位置]

    InitializeRowPosition --> ProcessTasks[处理每个任务]
    ProcessTasks --> TaskType{任务类型?}

    TaskType -->|里程碑| DrawMilestone[绘制里程碑]
    TaskType -->|任务| DrawTask[绘制任务条]

    DrawMilestone --> CalculatePosition[计算位置]
    DrawTask --> CalculatePosition

    CalculatePosition --> ApplyTaskFormat[应用任务格式]
    ApplyTaskFormat --> AddTaskLabel[添加任务标签]

    AddTaskLabel --> MoreTasks{还有任务?}
    MoreTasks -->|是| ProcessTasks
    MoreTasks -->|否| End([结束])
```

#### 4.3.1 主要函数和过程

```vba
' 创建甘特图
Public Sub CreateGanttChart()
    ' 1. 获取项目信息和任务数据
    Dim projectInfo As Dictionary
    Set projectInfo = Data.GetProjectInfo()

    Dim tasks As Collection
    Set tasks = Data.GetAllTasks()

    ' 2. 创建表头
    CreateGanttHeader projectInfo

    ' 3. 创建时间轴
    CreateTimeline projectInfo("StartDate"), projectInfo("EndDate")

    ' 4. 绘制任务和里程碑
    DrawTasksAndMilestones tasks
End Sub

' 创建甘特图表头
Private Sub CreateGanttHeader(projectInfo As Dictionary)
    ' 实现创建表头的逻辑
    ' ...
End Sub

' 创建时间轴
Private Sub CreateTimeline(startDate As Date, endDate As Date)
    ' 实现创建时间轴的逻辑
    ' ...
End Sub

' 绘制任务和里程碑
Private Sub DrawTasksAndMilestones(tasks As Collection)
    ' 实现绘制任务和里程碑的逻辑
    ' ...
End Sub
```

### 4.4 UI模块

UI模块负责用户界面交互和显示。

#### 4.4.0 模块流程

##### 甘特图工作表准备流程

```mermaid
flowchart TD
    Start([开始]) --> PrepareGanttWorksheet[准备甘特图工作表]

    PrepareGanttWorksheet --> CheckExistingSheet{存在旧工作表?}
    CheckExistingSheet -->|是| DeleteOldSheet[删除旧工作表]
    CheckExistingSheet -->|否| CreateNewSheet[创建新工作表]

    DeleteOldSheet --> CreateNewSheet
    CreateNewSheet --> FormatWorksheet[设置工作表格式]
    FormatWorksheet --> SetupHeaders[设置表头]

    SetupHeaders --> End([结束])
```

##### 数据输入流程

```mermaid
flowchart TD
    Start([开始]) --> InputData[数据输入]

    InputData --> DataType{数据类型?}
    DataType -->|项目信息| InputProjectInfo[输入项目信息]
    DataType -->|任务/里程碑| InputTaskInfo[输入任务信息]

    InputProjectInfo --> ValidateInput[验证输入]
    InputTaskInfo --> ValidateInput

    ValidateInput --> IsValid{数据有效?}
    IsValid -->|否| ShowError[显示错误]
    IsValid -->|是| SaveData[保存数据]

    ShowError --> InputData
    SaveData --> End([结束])
```

##### 模块间交互流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Main as Main模块
    participant UI as UI模块
    participant Data as Data模块
    participant Gantt as Gantt模块

    User->>Main: 触发生成甘特图
    Main->>Data: 验证数据
    Data-->>Main: 返回验证结果

    alt 数据验证成功
        Main->>UI: 准备甘特图工作表
        UI-->>Main: 工作表准备完成

        Main->>Gantt: 创建甘特图
        Gantt->>Data: 获取项目信息
        Data-->>Gantt: 返回项目信息

        Gantt->>Data: 获取任务数据
        Data-->>Gantt: 返回任务数据

        Gantt->>Gantt: 创建时间轴
        Gantt->>Gantt: 绘制任务和里程碑

        Gantt-->>Main: 甘特图创建完成
        Main-->>User: 显示成功消息
    else 数据验证失败
        Main-->>User: 显示错误消息
    end
```

#### 4.4.1 主要函数和过程

```vba
' 准备甘特图工作表
Public Sub PrepareGanttWorksheet()
    ' 1. 检查并删除旧的甘特图工作表
    DeleteGanttWorksheetIfExists

    ' 2. 创建新的甘特图工作表
    CreateGanttWorksheet

    ' 3. 设置工作表格式
    FormatGanttWorksheet
End Sub

' 检查并删除旧的甘特图工作表
Private Sub DeleteGanttWorksheetIfExists()
    ' 实现检查并删除旧工作表的逻辑
    ' ...
End Sub

' 创建新的甘特图工作表
Private Sub CreateGanttWorksheet()
    ' 实现创建新工作表的逻辑
    ' ...
End Sub

' 设置工作表格式
Private Sub FormatGanttWorksheet()
    ' 实现设置工作表格式的逻辑
    ' ...
End Sub
```

## 5. 算法设计

### 5.1 甘特图生成算法

甘特图生成是系统的核心功能，主要包括以下步骤：

1. **准备工作表**：

   - 检查并删除旧的甘特图工作表
   - 创建新的甘特图工作表
   - 设置工作表格式
2. **创建表头**：

   - 在B1单元格显示项目名称
   - 在B2单元格显示项目经理信息
   - 在B3-B5合并单元格显示更新时间和版本
3. **创建时间轴**：

   - 计算项目开始日期到结束日期的时间跨度
   - 在C3行起显示年份
   - 在C4行起显示月份
   - 在C5行起显示周数
   - 合并相邻相等的单元格
4. **绘制任务和里程碑**：

   - 在B6行起显示任务/里程碑所属大类
   - 根据任务/里程碑的开始日期、结束日期计算在时间轴上的位置
   - 根据任务/里程碑的属性（A/M）绘制水平条或菱形
   - 根据任务/里程碑的进度设置任务条的填充
   - 根据任务/里程碑的位置设置在甘特图中的相对位置
   - 根据任务/里程碑的文字位置设置描述文字的位置

### 5.2 时间轴生成算法

时间轴生成是甘特图的重要组成部分，主要包括以下步骤：

1. **计算时间范围**：

   - 获取项目开始日期和结束日期
   - 计算项目持续的年份、月份和周数
2. **生成年份行**：

   - 从项目开始日期的年份开始，到项目结束日期的年份结束
   - 每个年份占用相应的列数（根据该年在项目中包含的周数）
3. **生成月份行**：

   - 从项目开始日期的月份开始，到项目结束日期的月份结束
   - 每个月份占用相应的列数（根据该月在项目中包含的周数）
4. **生成周数行**：

   - 使用ISO 8601标准计算周数
   - 从项目开始日期的周数开始，到项目结束日期的周数结束
   - 每周占用一列
5. **合并单元格**：

   - 对于年份行，合并同一年份的单元格
   - 对于月份行，合并同一月份的单元格

### 5.3 任务位置计算算法

任务位置计算是确定任务在甘特图中位置的关键，主要包括以下步骤：

1. **计算水平位置**：

   - 根据任务开始日期，计算与项目开始日期的周数差，确定起始列
   - 根据任务持续时间（天数），计算占用的列数
2. **计算垂直位置**：

   - 根据任务的"位置"属性确定行位置
   - 如果位置为"same"，与上一个任务在同一行
   - 如果位置为"next"，在上一个任务的下一行
   - 如果位置为数字，相对于上一个任务偏移相应的行数
3. **处理文字位置**：

   - 根据任务的"文字位置"属性确定描述文字的位置
   - 对于里程碑，可以是上、下、左、右四个位置
   - 对于任务条，可以是上、下、左、右、中间五个位置

## 6. 用户界面设计

### 6.1 工作表布局

#### 6.1.1 项目信息工作表

项目信息工作表采用简洁的表单布局，包括以下元素：

- 标题：工作表顶部的标题
- 字段标签：每个字段的标签
- 输入区域：用户输入数据的单元格
- 说明文本：提供填写指导的文本

布局示意图：

```
PROJECT INFORMATION

Project Name:	X123 Project Timeing
Project Manager:	Yuno
Start Date:	2025-01-03
End Date:	2026-04-02

Project Description:	first version

```

#### 6.1.2 任务/里程碑工作表

任务/里程碑工作表采用表格布局，包括以下元素：

- 表头：每列的字段名称
- 数据行：每行代表一个任务或里程碑
- 数据验证：提供下拉列表等数据验证控件

布局示意图：

```
+----+------------+-------------+------+------------+------------+----------+----------+----------+-------+---------------+
| ID | Category   | Description | Type | Start Date | End Date   | Duration | Progress | Position | Color | Text Position |
+----+------------+-------------+------+------------+------------+----------+----------+----------+-------+---------------+
|    |            |             |      |            |            |          |          |          |       |               |
+----+------------+-------------+------+------------+------------+----------+----------+----------+-------+---------------+
|    |            |             |      |            |            |          |          |          |       |               |
+----+------------+-------------+------+------------+------------+----------+----------+----------+-------+---------------+
|    |            |             |      |            |            |          |          |          |       |               |
+----+------------+-------------+------+------------+------------+----------+----------+----------+-------+---------------+


```

#### 6.1.3 甘特图工作表

甘特图工作表的布局由系统自动生成，包括以下元素：

- 项目信息：顶部显示项目名称、经理等信息
- 时间轴：显示年份、月份和周数
- 任务/里程碑：根据数据绘制的任务条和里程碑

布局示意图：

```
+-------+-------+-------+-------+-------+-------+-------+-------+
| 项目名称                                                      |
+-------+-------+-------+-------+-------+-------+-------+-------+
| 项目经理                                                      |
+-------+-------+-------+-------+-------+-------+-------+-------+
| 更新时间和版本                                                |
+-------+-------+-------+-------+-------+-------+-------+-------+
|       | 2023                  | 2024                          |
+-------+-------+-------+-------+-------+-------+-------+-------+
|       | 01    | 02    | 03    | 01    | 02    | 03    | 04    |
+-------+-------+-------+-------+-------+-------+-------+-------+
|       | W01   | W02   | W03   | W04   | W05   | W06   | W07   |
+-------+-------+-------+-------+-------+-------+-------+-------+
| 类型1 |                                                       |
+-------+-------+-------+-------+-------+-------+-------+-------+
|       | ◆                                                     |
+-------+-------+-------+-------+-------+-------+-------+-------+
|       |       | ============================                  |
+-------+-------+-------+-------+-------+-------+-------+-------+
| 类型2 |                                                       |
+-------+-------+-------+-------+-------+-------+-------+-------+
|       |               | ◆                                     |
+-------+-------+-------+-------+-------+-------+-------+-------+
|       |                       | ==================            |
+-------+-------+-------+-------+-------+-------+-------+-------+
```

### 6.2 颜色和格式

#### 6.2.1 颜色方案

- **标题和表头**：深蓝色背景，白色文字
- **任务条**：蓝色填充，深蓝色边框
- **里程碑**：红色填充，深红色边框
- **进度**：绿色填充
- **时间轴**：浅灰色背景
- **当前日期线**：红色虚线

#### 6.2.2 字体和格式

- **标题**：Arial, 14pt, 粗体
- **表头**：Arial, 11pt, 粗体
- **正文**：Arial, 10pt
- **项目名称**：Arial, 16pt, 粗体
- **项目经理**：Arial, 12pt
- **时间轴**：Arial, 10pt, 粗体

## 7. 错误处理和调试设计

### 7.1 调试模块

系统包含专门的调试模块(modDebug)，提供全面的日志记录和错误处理功能。

#### 7.1.1 调试模块结构

```mermaid
classDiagram
    class modDebug {
        +InitDebug(level, enableDebug, enableFileLogging, enableImmediateOutput)
        +LogError(errNumber, errDescription, errSource)
        +LogWarning(message, source)
        +LogInfo(message, source)
        +LogVerbose(message, source)
        +LogFunctionEntry(functionName)
        +LogFunctionExit(functionName, additionalInfo)
        -OpenLogFile()
        -CloseLogFile()
        -WriteToLog(message)
        -FormatLogMessage(level, message, source)
    }
```

#### 7.1.2 调试级别

系统支持以下调试级别：

1. **ERROR (1)**：仅记录错误信息
2. **WARNING (2)**：记录错误和警告信息
3. **INFO (3)**：记录错误、警告和一般信息
4. **VERBOSE (4)**：记录所有信息，包括详细的跟踪信息

#### 7.1.3 日志输出方式

系统支持两种日志输出方式：

1. **文件日志**：将日志信息写入UTF-8编码的文本文件
2. **即时窗口**：将日志信息输出到VBA即时窗口

### 7.2 错误处理流程

```mermaid
flowchart TD
    Start([开始]) --> SetupErrorHandler[设置错误处理]
    SetupErrorHandler --> ExecuteCode[执行代码]
    ExecuteCode --> ErrorOccurred{发生错误?}

    ErrorOccurred -->|是| LogError[记录错误]
    ErrorOccurred -->|否| CleanExit[清理并退出]

    LogError --> DisplayMessage[显示用户友好消息]
    DisplayMessage --> AttemptRecovery{尝试恢复?}

    AttemptRecovery -->|是| RecoveryAction[执行恢复操作]
    AttemptRecovery -->|否| CleanExit

    RecoveryAction --> CleanExit
    CleanExit --> LogExit[记录函数退出]
    LogExit --> End([结束])
```

### 7.3 错误类型

系统可能遇到的错误类型包括：

1. **数据验证错误**：用户输入的数据不符合验证规则
2. **日期计算错误**：日期计算过程中出现错误
3. **工作表操作错误**：创建、删除或修改工作表时出现错误
4. **绘图错误**：绘制甘特图元素时出现错误
5. **配置访问错误**：访问配置时出现错误
6. **其他运行时错误**：其他未预见的错误

### 7.4 错误处理策略

系统采用以下错误处理策略：

1. **预防性验证**：在操作前验证数据和条件，避免错误发生
2. **错误捕获**：使用VBA的错误处理机制捕获错误
3. **日志记录**：使用调试模块记录详细的错误信息
4. **用户反馈**：向用户提供友好的错误消息
5. **恢复机制**：提供从错误中恢复的机制
6. **默认值**：在错误情况下使用默认值继续操作

### 7.5 错误处理代码结构

每个主要函数和过程都包含错误处理代码，基本结构如下：

```vba
Public Sub SomeFunction()
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "ModuleName.SomeFunction"

    ' 函数主体代码
    ' ...

CleanExit:
    ' 清理代码
    ' ...

    ' 记录函数退出
    modDebug.LogFunctionExit "ModuleName.SomeFunction"
    Exit Sub

ErrorHandler:
    ' 记录错误
    modDebug.LogError Err.Number, Err.Description, "ModuleName.SomeFunction"

    ' 显示用户友好的错误消息
    MsgBox "发生错误: " & Err.Description, vbExclamation, "错误"

    ' 恢复操作或跳转到清理代码
    Resume CleanExit
End Sub
```

### 7.6 函数跟踪

系统使用函数进入和退出日志记录来跟踪函数调用，便于调试和性能分析：

```vba
' 记录函数进入
modDebug.LogFunctionEntry "ModuleName.FunctionName"

' 函数代码
' ...

' 记录函数退出，可以包含额外信息
modDebug.LogFunctionExit "ModuleName.FunctionName", "处理了 " & count & " 个项目"
```

## 8. 测试计划

### 8.1 单元测试

对系统的各个模块和函数进行单独测试，确保它们正确工作。

#### 8.1.1 Data模块测试

- 测试数据验证函数
- 测试数据获取函数
- 测试日期计算函数

#### 8.1.2 Gantt模块测试

- 测试时间轴生成函数
- 测试任务绘制函数
- 测试里程碑绘制函数

#### 8.1.3 UI模块测试

- 测试工作表创建和删除函数
- 测试格式设置函数

### 8.2 集成测试

测试模块之间的交互，确保它们能够协同工作。

- 测试Data模块和Gantt模块的交互
- 测试UI模块和Gantt模块的交互
- 测试Main模块对其他模块的协调

### 8.3 系统测试

测试整个系统的功能，确保它能够满足需求。

- 测试项目信息管理功能
- 测试任务和里程碑管理功能
- 测试甘特图生成功能
- 测试错误处理功能

### 8.4 验收测试

根据验收标准对系统进行最终测试，确保它满足用户需求。

- 测试系统是否满足所有功能需求
- 测试系统是否满足所有非功能需求
- 测试系统的易用性和用户体验

## 9. 实现计划

### 9.1 开发阶段

系统开发分为以下阶段：

1. **准备阶段**：

   - 创建Excel工作簿模板
   - 设置工作表结构
   - 创建VBA模块框架
2. **核心功能开发**：

   - 实现Data模块
   - 实现Gantt模块
   - 实现UI模块
   - 实现Main模块
3. **测试和优化**：

   - 执行单元测试
   - 执行集成测试
   - 执行系统测试
   - 优化性能和用户体验
4. **文档和交付**：

   - 完善代码注释
   - 编写用户手册
   - 准备交付包

### 9.2 开发时间表

| 阶段         | 任务                | 预计时间 |
| ------------ | ------------------- | -------- |
| 准备阶段     | 创建Excel工作簿模板 | 1天      |
| 准备阶段     | 设置工作表结构      | 1天      |
| 准备阶段     | 创建VBA模块框架     | 1天      |
| 核心功能开发 | 实现Data模块        | 2天      |
| 核心功能开发 | 实现Gantt模块       | 3天      |
| 核心功能开发 | 实现UI模块          | 2天      |
| 核心功能开发 | 实现Main模块        | 1天      |
| 测试和优化   | 执行单元测试        | 1天      |
| 测试和优化   | 执行集成测试        | 1天      |
| 测试和优化   | 执行系统测试        | 1天      |
| 测试和优化   | 优化性能和用户体验  | 2天      |
| 文档和交付   | 完善代码注释        | 1天      |
| 文档和交付   | 编写用户手册        | 1天      |
| 文档和交付   | 准备交付包          | 1天      |

## 10. 附录

### 10.1 术语表

- **VBA**: Visual Basic for Applications，Excel内置的编程语言
- **甘特图**: 一种条形图，用于显示项目进度计划
- **里程碑**: 项目中的重要节点或事件
- **超级表**: Excel中的表格区域，具有自动扩展和格式化功能

### 10.2 参考资料

- Excel VBA编程指南
- Excel对象模型参考
- 项目管理最佳实践指南
