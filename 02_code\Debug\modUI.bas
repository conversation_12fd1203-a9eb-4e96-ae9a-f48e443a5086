'Attribute VB_Name = "modUI"
Option Explicit

' =========================================================
' 模块: modUI
' 描述: 负责用户界面交互和显示
' =========================================================

' ---------------------------------------------------------
' 工作表准备函数
' ---------------------------------------------------------

' 准备甘特图工作表
Public Sub PrepareGanttWorksheet()
    On Error GoTo ErrorHandler

    ' 1. 检查并删除旧的甘特图工作表
    DeleteGanttWorksheetIfExists

    ' 2. 创建新的甘特图工作表
    Dim ws As Worksheet
    Set ws = CreateGanttWorksheet

    ' 3. 设置最近更新时间信息
    SetupHeaders ws


    ' 4. 设置网格线
    SetupGridlines ws

    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modUI.PrepareGanttWorksheet"
    Err.Raise Err.Number, "modUI.PrepareGanttWorksheet", Err.description
End Sub

' 检查并删除旧的甘特图工作表
Private Sub DeleteGanttWorksheetIfExists()
    On Error Resume Next

    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("GanttChart")

    ' 如果工作表存在，则删除
    If Not ws Is Nothing Then
        Application.DisplayAlerts = False
        ws.Delete
        Application.DisplayAlerts = True
    End If

    On Error GoTo 0
End Sub

' 创建新的甘特图工作表
Private Function CreateGanttWorksheet() As Worksheet
    On Error GoTo ErrorHandler

    ' 添加新工作表
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets.Add(After:=ThisWorkbook.Worksheets(ThisWorkbook.Worksheets.Count))

    ' 设置工作表名称
    ws.Name = "GanttChart"

    ' 设置工作表颜色
    ws.Tab.Color = RGB(0, 112, 192)

    Set CreateGanttWorksheet = ws
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modUI.CreateGanttWorksheet"
    Err.Raise Err.Number, "modUI.CreateGanttWorksheet", Err.description
End Function



' 设置表头
Private Sub SetupHeaders(ws As Worksheet)
    On Error GoTo ErrorHandler

    ' 注意：项目信息的设置已移至modGantt.MergeProjectInfoCells函数中

    ' 设置最近更新时间信息（仅设置内容，格式将由ApplyGanttTheme处理）
    ws.range("B3:B5").Merge
    ws.range("B3").value = "Last Updated: " & Format(Date, "yyyy-mm-dd")

    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modUI.SetupHeaders"
    Err.Raise Err.Number, "modUI.SetupHeaders", Err.description
End Sub

' 设置网格线
Private Sub SetupGridlines(ws As Worksheet)
    On Error GoTo ErrorHandler

    ' 显示网格线
    ws.Activate
    ActiveWindow.DisplayGridlines = False

    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.description, "modUI.SetupGridlines"
    Err.Raise Err.Number, "modUI.SetupGridlines", Err.description
End Sub

' 应用甘特图主题
Public Sub ApplyGanttTheme(ws As Worksheet)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modUI.ApplyGanttTheme"

    ' 检查工作表参数是否有效
    If ws Is Nothing Then
        modDebug.LogError 91, "工作表参数无效", "modUI.ApplyGanttTheme"
        Err.Raise 91, "modUI.ApplyGanttTheme", "工作表参数无效"
        Exit Sub
    End If

    ' 检查工作表名称是否为"GanttChart"
    If ws.Name <> "GanttChart" Then
        modDebug.LogWarning "工作表名称不是'GanttChart'，而是'" & ws.Name & "'", "modUI.ApplyGanttTheme"
    End If

    ' 直接获取ChartTheme配置项
    Dim themeValue As String

    ' 获取主题值，如果不存在则使用默认值1
    themeValue = CStr(GetConfig("ChartTheme", "1"))
    modDebug.LogInfo "使用ChartTheme值: " & themeValue, "modUI.ApplyGanttTheme"

    ' 创建主题配置字典
    Dim themeConfig As Dictionary
    Set themeConfig = CreateThemeConfig(themeValue)

    ' 应用主题配置
    ApplyThemeConfig ws, themeConfig

    ' 记录函数退出
    modDebug.LogFunctionExit "modUI.ApplyGanttTheme"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUI.ApplyGanttTheme"
    Err.Raise Err.Number, "modUI.ApplyGanttTheme", Err.Description
End Sub

' 创建主题配置字典
Private Function CreateThemeConfig(themeValue As String) As Dictionary
    On Error GoTo ErrorHandler

    modDebug.LogFunctionEntry "modUI.CreateThemeConfig"
    modDebug.LogInfo "创建主题配置，主题值: " & themeValue, "modUI.CreateThemeConfig"

    Dim themeConfig As New Dictionary
    Dim defaultConfig As Dictionary
    Dim key As Variant
    Dim defaultKey As Variant
    Dim i As Integer
    Dim j As Integer
    Dim configName As String
    Dim defaultValue As Variant

    ' 如果是预设主题
    If IsNumeric(themeValue) Then
        Dim themeNumber As Integer
        themeNumber = CInt(themeValue)

        ' 检查是否是预设主题（1-5）
        If themeNumber >= 1 And themeNumber <= 5 Then
            modDebug.LogInfo "创建预设主题 " & themeNumber & " 的配置", "modUI.CreateThemeConfig"

            ' 获取默认配置作为基础
            Set defaultConfig = modConfigDefaults.GetDefaults()

            ' 复制所有默认配置
            For Each key In defaultConfig.Keys
                ' 只复制UI相关的配置项
                If key Like "Project*" Or key Like "Timeline*" Or key Like "Category*" Or _
                   key Like "Chart*" Or key Like "SupplementInfo*" Or key = "ColumnAWidth" Or _
                   key = "EnableLogo" Or key = "LogoMargin" Then
                    themeConfig.Add key, defaultConfig(key)
                End If
            Next key

            ' 根据主题编号修改特定配置
            Select Case themeNumber
                Case 1
                    ' 主题1: 经典浅色主题 - 使用默认值，无需修改

                Case 2
                    ' 主题2: 深色主题
                    themeConfig("ChartBackgroundColor") = "#333333"
                    themeConfig("ChartAlternateRowColor") = "#444444"
                    themeConfig("ChartGridlineColor") = "#555555"

                    ' 项目名称区域
                    themeConfig("ProjectNameFontColor") = "#FFFFFF"
                    themeConfig("ProjectNameBackColor") = "#333333"

                    ' 项目经理名称区域
                    themeConfig("ProjectManagerFontColor") = "#CCCCCC"
                    themeConfig("ProjectManagerBackColor") = "#333333"

                    ' 补充信息区域
                    themeConfig("SupplementInfoFontColor") = "#CCCCCC"
                    themeConfig("SupplementInfoBackColor") = "#333333"

                    ' 时间轴表头年区域
                    themeConfig("TimelineYearBackColor") = "#2F5597"

                    ' 时间轴表头月区域
                    themeConfig("TimelineMonthBackColor") = "#4472C4"

                    ' 时间轴表头周区域
                    themeConfig("TimelineWeekBackColor") = "#548235"

                    ' 任务类区域
                    themeConfig("CategoryFontColor") = "#FFFFFF"
                    themeConfig("CategoryBackColor") = "#3A3A3A"
                    themeConfig("CategoryBorderStyle") = "all"

                Case 3
                    ' 主题3: 蓝色主题
                    themeConfig("ChartBackgroundColor") = "#F0F8FF"
                    themeConfig("ChartAlternateRowColor") = "#E6F2FF"
                    themeConfig("ChartGridlineColor") = "#B0C4DE"
                    themeConfig("ChartGridlinesType") = "horizontal"

                    ' 项目名称区域
                    themeConfig("ProjectNameFont") = "Calibri"
                    themeConfig("ProjectNameFontColor") = "#1F4E79"
                    themeConfig("ProjectNameBackColor") = "#F0F8FF"
                    themeConfig("ProjectNameBorderStyle") = "bottom"

                    ' 项目经理名称区域
                    themeConfig("ProjectManagerFont") = "Calibri"
                    themeConfig("ProjectManagerFontColor") = "#2F5597"
                    themeConfig("ProjectManagerBackColor") = "#F0F8FF"

                    ' 补充信息区域
                    themeConfig("SupplementInfoFont") = "Calibri"
                    themeConfig("SupplementInfoFontColor") = "#2F5597"
                    themeConfig("SupplementInfoBackColor") = "#F0F8FF"

                    ' 时间轴表头年区域
                    themeConfig("TimelineYearFont") = "Calibri"
                    themeConfig("TimelineYearBackColor") = "#1F4E79"

                    ' 时间轴表头月区域
                    themeConfig("TimelineMonthFont") = "Calibri"
                    themeConfig("TimelineMonthBackColor") = "#2F5597"

                    ' 时间轴表头周区域
                    themeConfig("TimelineWeekFont") = "Calibri"
                    themeConfig("TimelineWeekBackColor") = "#5B9BD5"

                    ' 任务类区域
                    themeConfig("CategoryFont") = "Calibri"
                    themeConfig("CategoryFontColor") = "#1F4E79"
                    themeConfig("CategoryBackColor") = "#DEEBF7"

                Case 4
                    ' 主题4: 绿色主题
                    themeConfig("ChartBackgroundColor") = "#F0FFF0"
                    themeConfig("ChartAlternateRowColor") = "#E6FFE6"
                    themeConfig("ChartGridlineColor") = "#B0DEB0"
                    themeConfig("ChartGridlinesArea") = "ganttDrawing"
                    themeConfig("ChartGridlinesType") = "horizontal"

                    ' 项目名称区域
                    themeConfig("ProjectNameFont") = "Segoe UI"
                    themeConfig("ProjectNameFontColor") = "#375623"
                    themeConfig("ProjectNameBackColor") = "#F0FFF0"
                    themeConfig("ProjectNameBorderStyle") = "bottom"

                    ' 项目经理名称区域
                    themeConfig("ProjectManagerFont") = "Segoe UI"
                    themeConfig("ProjectManagerFontColor") = "#548235"
                    themeConfig("ProjectManagerBackColor") = "#F0FFF0"

                    ' 补充信息区域
                    themeConfig("SupplementInfoFont") = "Segoe UI"
                    themeConfig("SupplementInfoFontSize") = 9.5
                    themeConfig("SupplementInfoFontColor") = "#548235"
                    themeConfig("SupplementInfoBackColor") = "#F0FFF0"

                    ' 时间轴表头年区域
                    themeConfig("TimelineYearFont") = "Segoe UI"
                    themeConfig("TimelineYearBackColor") = "#375623"

                    ' 时间轴表头月区域
                    themeConfig("TimelineMonthFont") = "Segoe UI"
                    themeConfig("TimelineMonthBackColor") = "#548235"

                    ' 时间轴表头周区域
                    themeConfig("TimelineWeekFont") = "Segoe UI"
                    themeConfig("TimelineWeekBackColor") = "#70AD47"

                    ' 任务类区域
                    themeConfig("CategoryFont") = "Segoe UI"
                    themeConfig("CategoryFontSize") = 10
                    themeConfig("CategoryFontColor") = "#375623"
                    themeConfig("CategoryBackColor") = "#E2EFDA"

                Case 5
                    ' 主题5: 高对比度主题
                    themeConfig("ChartBackgroundColor") = "#FFFFFF"
                    themeConfig("ChartAlternateRowColor") = "#E0E0E0"
                    themeConfig("ChartGridlineColor") = "#000000"

                    ' 项目名称区域
                    themeConfig("ProjectNameFont") = "Arial"
                    themeConfig("ProjectNameBorderStyle") = "all"

                    ' 项目经理名称区域
                    themeConfig("ProjectManagerFont") = "Arial"
                    themeConfig("ProjectManagerBorderStyle") = "all"

                    ' 补充信息区域
                    themeConfig("SupplementInfoFont") = "Arial"
                    themeConfig("SupplementInfoBorderStyle") = "all"

                    ' 时间轴表头年区域
                    themeConfig("TimelineYearFont") = "Arial"
                    themeConfig("TimelineYearBackColor") = "#000000"

                    ' 时间轴表头月区域
                    themeConfig("TimelineMonthFont") = "Arial"
                    themeConfig("TimelineMonthBackColor") = "#404040"

                    ' 时间轴表头周区域
                    themeConfig("TimelineWeekFont") = "Arial"
                    themeConfig("TimelineWeekBackColor") = "#808080"

                    ' 任务类区域
                    themeConfig("CategoryFont") = "Arial"
                    themeConfig("CategoryBackColor") = "#D9D9D9"
                    themeConfig("CategoryBorderStyle") = "all"
            End Select
        Else
            ' 无效的主题编号，使用默认主题1
            modDebug.LogWarning "无效的主题编号: " & themeValue & "，使用默认主题1", "modUI.CreateThemeConfig"
            ' 获取默认配置
            Set themeConfig = New Dictionary
            Set defaultConfig = modConfigDefaults.GetDefaults()

            ' 复制所有UI相关的默认配置
            For Each defaultKey In defaultConfig.Keys
                ' 只复制UI相关的配置项
                If defaultKey Like "Project*" Or defaultKey Like "Timeline*" Or defaultKey Like "Category*" Or _
                   defaultKey Like "Chart*" Or defaultKey Like "SupplementInfo*" Or defaultKey = "ColumnAWidth" Or _
                   defaultKey = "EnableLogo" Or defaultKey = "LogoMargin" Then
                    themeConfig.Add defaultKey, defaultConfig(defaultKey)
                End If
            Next defaultKey
        End If
    ElseIf LCase(themeValue) = "custom" Then
        ' 自定义主题 - 使用配置表中的值
        modDebug.LogInfo "创建自定义主题配置", "modUI.CreateThemeConfig"

        ' 获取默认配置作为基础
        Set defaultConfig = modConfigDefaults.GetDefaults()

        ' 复制所有默认配置
        For Each key In defaultConfig.Keys
            ' 只复制UI相关的配置项
            If key Like "Project*" Or key Like "Timeline*" Or key Like "Category*" Or _
               key Like "Chart*" Or key Like "SupplementInfo*" Or key = "ColumnAWidth" Then
                themeConfig.Add key, defaultConfig(key)
            End If
        Next key

        ' 从配置表中获取自定义值覆盖默认值
        Dim customKeys As Variant
        customKeys = Array("ProjectNameFont", "ProjectNameFontSize", "ProjectNameFontBold", "ProjectNameFontColor", _
                          "ProjectNameBackColor", "ProjectNameBorderStyle", "ProjectNameRowHeight", _
                          "ProjectManagerFont", "ProjectManagerFontSize", "ProjectManagerFontBold", _
                          "ProjectManagerFontColor", "ProjectManagerBackColor", "ProjectManagerBorderStyle", _
                          "ProjectManagerRowHeight", "SupplementInfoFont", "SupplementInfoFontSize", _
                          "SupplementInfoFontBold", "SupplementInfoFontColor", "SupplementInfoBackColor", _
                          "SupplementInfoBorderStyle", "SupplementInfoRowHeight", "TimelineYearFont", _
                          "TimelineYearFontSize", "TimelineYearFontBold", "TimelineYearFontColor", _
                          "TimelineYearBackColor", "TimelineYearBorderStyle", "TimelineYearRowHeight", _
                          "TimelineMonthFont", "TimelineMonthFontSize", "TimelineMonthFontBold", _
                          "TimelineMonthFontColor", "TimelineMonthBackColor", "TimelineMonthBorderStyle", _
                          "TimelineMonthRowHeight", "TimelineWeekFont", "TimelineWeekFontSize", _
                          "TimelineWeekFontBold", "TimelineWeekFontColor", "TimelineWeekBackColor", _
                          "TimelineWeekBorderStyle", "TimelineWeekRowHeight", "CategoryFont", _
                          "CategoryFontSize", "CategoryFontBold", "CategoryFontColor", "CategoryBackColor", _
                          "CategoryBorderStyle", "CategoryColumnWidth", "CategoryWrapText", "ChartTheme", _
                          "ChartGridlinesArea", "ChartGridlinesType", "ChartGridlineColor", _
                          "ChartBackgroundColor", "ChartAlternateRowColor", "ColumnAWidth")

        For i = LBound(customKeys) To UBound(customKeys)
            configName = customKeys(i)

            ' 获取配置值，如果不存在则使用已有的默认值
            If themeConfig.Exists(configName) Then
                defaultValue = themeConfig(configName)
                themeConfig(configName) = GetConfig(configName, defaultValue)
            End If
        Next i
    Else
        ' 无效的主题值，使用默认主题1
        modDebug.LogWarning "无效的主题值: " & themeValue & "，使用默认主题1", "modUI.CreateThemeConfig"
        ' 获取默认配置
        Set themeConfig = New Dictionary
        Set defaultConfig = modConfigDefaults.GetDefaults()

        ' 复制所有UI相关的默认配置
        For Each defaultKey In defaultConfig.Keys
            ' 只复制UI相关的配置项
            If defaultKey Like "Project*" Or defaultKey Like "Timeline*" Or defaultKey Like "Category*" Or _
               defaultKey Like "Chart*" Or defaultKey Like "SupplementInfo*" Or defaultKey = "ColumnAWidth" Then
                themeConfig.Add defaultKey, defaultConfig(defaultKey)
            End If
        Next defaultKey
    End If

    ' 记录创建的主题配置内容
    modDebug.LogInfo "主题配置创建完成，包含 " & themeConfig.Count & " 个配置项", "modUI.CreateThemeConfig"

    ' 检查关键配置项是否存在
    Dim requiredKeys As Variant
    requiredKeys = Array("ChartGridlinesArea", "ChartGridlinesType", "ChartGridlineColor", _
                         "ChartBackgroundColor", "ChartAlternateRowColor", "ColumnAWidth")
    For i = LBound(requiredKeys) To UBound(requiredKeys)
        If Not themeConfig.Exists(requiredKeys(i)) Then
            modDebug.LogWarning "主题配置缺少关键项: " & requiredKeys(i), "modUI.CreateThemeConfig"
        End If
    Next i

    Set CreateThemeConfig = themeConfig
    modDebug.LogFunctionExit "modUI.CreateThemeConfig"
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUI.CreateThemeConfig"
    ' 出错时返回默认配置
    Set CreateThemeConfig = New Dictionary
    Set defaultConfig = modConfigDefaults.GetDefaults()

    ' 复制所有UI相关的默认配置
    modDebug.LogInfo "错误处理程序: 使用默认配置", "modUI.CreateThemeConfig"

    For Each defaultKey In defaultConfig.Keys
        ' 只复制UI相关的配置项
        If defaultKey Like "Project*" Or defaultKey Like "Timeline*" Or defaultKey Like "Category*" Or _
           defaultKey Like "Chart*" Or defaultKey Like "SupplementInfo*" Or defaultKey = "ColumnAWidth" Or _
           defaultKey = "EnableLogo" Or defaultKey = "LogoMargin" Then
            CreateThemeConfig.Add defaultKey, defaultConfig(defaultKey)
        End If
    Next defaultKey

    ' 检查关键配置项是否存在
    Dim errRequiredKeys As Variant
    errRequiredKeys = Array("ChartGridlinesArea", "ChartGridlinesType", "ChartGridlineColor", _
                         "ChartBackgroundColor", "ChartAlternateRowColor", "ColumnAWidth")
    For j = LBound(errRequiredKeys) To UBound(errRequiredKeys)
        If Not CreateThemeConfig.Exists(errRequiredKeys(j)) Then
            modDebug.LogWarning "错误处理程序: 默认配置缺少关键项: " & errRequiredKeys(j), "modUI.CreateThemeConfig"
        End If
    Next j

    modDebug.LogFunctionExit "modUI.CreateThemeConfig", "从错误处理程序退出"
End Function

' 应用主题配置
Private Sub ApplyThemeConfig(ws As Worksheet, themeConfig As Dictionary)
    On Error GoTo ErrorHandler

    modDebug.LogFunctionEntry "modUI.ApplyThemeConfig"

    ' 声明所有变量
    Dim temp As Range
    Dim usedRng As Range
    Dim lastRow As Long, endCol As Long
    Dim gridlinesArea As String
    Dim gridlinesType As String
    Dim gridlineColor As String
    Dim backgroundColor As String
    Dim alternateRowColor As String
    Dim columnAWidth As Double
    Dim debugKey As Variant
    Dim rowHeightValue As String
    Dim i As Long
    Dim lastCol As String
    Dim headerRange As Range, ganttDrawingRange As Range, taskCategoryRange As Range

    ' 刷新使用范围并确定甘特图边界
    Application.CalculateFull
    ' 触发UsedRange属性的计算
    Set temp = ws.UsedRange

    ' 获取工作表使用范围
    Set usedRng = ws.UsedRange

    ' 确定最后一行和最后一列
    lastRow = usedRng.Row + usedRng.Rows.Count - 1
    endCol = usedRng.Column + usedRng.Columns.Count - 1

    modDebug.LogInfo "甘特图边界: 最后一行=" & lastRow & ", 最后一列=" & endCol, "modUI.ApplyThemeConfig"

    ' 添加调试记录，记录themeConfig中的所有键值对
    modDebug.LogInfo "主题配置字典内容:", "modUI.ApplyThemeConfig"
    For Each debugKey In themeConfig.Keys
        modDebug.LogInfo "  " & debugKey & " = " & themeConfig(debugKey), "modUI.ApplyThemeConfig"
    Next debugKey

    ' 使用On Error Resume Next捕获可能的错误
    On Error Resume Next

    ' 首先获取ChartBackgroundColor - 需要在设置背景颜色之前获取
    backgroundColor = CStr(themeConfig("ChartBackgroundColor"))
    If Err.Number <> 0 Then
        modDebug.LogError Err.Number, "获取ChartBackgroundColor失败: " & Err.Description, "modUI.ApplyThemeConfig"
        backgroundColor = "#FFFFFF" ' 使用默认值
        Err.Clear
    End If
    modDebug.LogInfo "使用backgroundColor: " & backgroundColor, "modUI.ApplyThemeConfig"

    ' 首先设置背景颜色（从B列开始，A列保持默认）- 作为底色
    modDebug.LogInfo "设置背景颜色: " & backgroundColor, "modUI.ApplyThemeConfig"
    ws.Range(ws.Cells(1, 2), ws.Cells(lastRow, endCol)).Interior.Color = modUtilities.GetRGBColor(backgroundColor)

    ' 获取其他全局配置

    ' 使用On Error Resume Next捕获可能的错误
    On Error Resume Next

    ' 获取ChartGridlinesArea
    gridlinesArea = CStr(themeConfig("ChartGridlinesArea"))
    If Err.Number <> 0 Then
        modDebug.LogError Err.Number, "获取ChartGridlinesArea失败: " & Err.Description, "modUI.ApplyThemeConfig"
        gridlinesArea = "all" ' 使用默认值
        Err.Clear
    End If
    modDebug.LogInfo "使用gridlinesArea: " & gridlinesArea, "modUI.ApplyThemeConfig"

    ' 获取ChartGridlinesType
    gridlinesType = CStr(themeConfig("ChartGridlinesType"))
    If Err.Number <> 0 Then
        modDebug.LogError Err.Number, "获取ChartGridlinesType失败: " & Err.Description, "modUI.ApplyThemeConfig"
        gridlinesType = "all" ' 使用默认值
        Err.Clear
    End If
    modDebug.LogInfo "使用gridlinesType: " & gridlinesType, "modUI.ApplyThemeConfig"

    ' 获取ChartGridlineColor
    gridlineColor = CStr(themeConfig("ChartGridlineColor"))
    If Err.Number <> 0 Then
        modDebug.LogError Err.Number, "获取ChartGridlineColor失败: " & Err.Description, "modUI.ApplyThemeConfig"
        gridlineColor = "#C0C0C0" ' 使用默认值
        Err.Clear
    End If
    modDebug.LogInfo "使用gridlineColor: " & gridlineColor, "modUI.ApplyThemeConfig"

    ' ChartBackgroundColor已在函数开始时获取

    ' 获取ChartAlternateRowColor
    alternateRowColor = CStr(themeConfig("ChartAlternateRowColor"))
    If Err.Number <> 0 Then
        modDebug.LogError Err.Number, "获取ChartAlternateRowColor失败: " & Err.Description, "modUI.ApplyThemeConfig"
        alternateRowColor = "#F0F0F0" ' 使用默认值
        Err.Clear
    End If
    modDebug.LogInfo "使用alternateRowColor: " & alternateRowColor, "modUI.ApplyThemeConfig"

    ' 获取ColumnAWidth
    columnAWidth = CDbl(themeConfig("ColumnAWidth"))
    If Err.Number <> 0 Then
        modDebug.LogError Err.Number, "获取ColumnAWidth失败: " & Err.Description, "modUI.ApplyThemeConfig"
        columnAWidth = 2 ' 使用默认值
        Err.Clear
    End If
    modDebug.LogInfo "使用columnAWidth: " & columnAWidth, "modUI.ApplyThemeConfig"

    ' 恢复错误处理
    On Error GoTo ErrorHandler

    ' 设置A列宽度
    ws.Columns("A").ColumnWidth = columnAWidth

    ' 应用项目名称区域样式（整行，从B列到endCol）
    With ws.Range(ws.Cells(1, 2), ws.Cells(1, endCol))
        .Font.Name = CStr(themeConfig("ProjectNameFont"))
        .Font.Size = CDbl(themeConfig("ProjectNameFontSize"))
        .Font.Bold = CBool(themeConfig("ProjectNameFontBold"))
        .Font.Color = modUtilities.GetRGBColor(CStr(themeConfig("ProjectNameFontColor")))
        .Interior.Color = modUtilities.GetRGBColor(CStr(themeConfig("ProjectNameBackColor")))
        ' 边框样式将在后面统一应用

        ' 设置文本对齐方式
        Dim projectNameTextAlign As String
        projectNameTextAlign = LCase(CStr(themeConfig("ProjectNameTextAlign")))

        Select Case projectNameTextAlign
            Case "left"
                .HorizontalAlignment = xlLeft
                .VerticalAlignment = xlCenter
            Case "right"
                .HorizontalAlignment = xlRight
                .VerticalAlignment = xlCenter
            Case "center"
                ' 居中对齐
                .HorizontalAlignment = xlCenter
                .VerticalAlignment = xlCenter
            Case Else
                ' 默认为水平垂直居中
                .HorizontalAlignment = xlCenter
                .VerticalAlignment = xlCenter
        End Select

        modDebug.LogInfo "应用项目名称区域文本对齐方式: " & projectNameTextAlign, "modUI.ApplyThemeConfig"

        ' 设置行高
        rowHeightValue = CStr(themeConfig("ProjectNameRowHeight"))
        If LCase(rowHeightValue) = "auto" Then
            ws.Rows(1).AutoFit
        Else
            ws.Rows(1).RowHeight = CDbl(rowHeightValue)
        End If

        modDebug.LogInfo "应用项目名称区域样式: B1:" & Split(ws.Cells(1, endCol).Address, "$")(1) & "1", "modUI.ApplyThemeConfig"
    End With

    ' 应用项目经理名称区域样式（整行，从B列到endCol）
    With ws.Range(ws.Cells(2, 2), ws.Cells(2, endCol))
        .Font.Name = CStr(themeConfig("ProjectManagerFont"))
        .Font.Size = CDbl(themeConfig("ProjectManagerFontSize"))
        .Font.Bold = CBool(themeConfig("ProjectManagerFontBold"))
        .Font.Color = modUtilities.GetRGBColor(CStr(themeConfig("ProjectManagerFontColor")))
        .Interior.Color = modUtilities.GetRGBColor(CStr(themeConfig("ProjectManagerBackColor")))
        ' 边框样式将在后面统一应用

        ' 设置文本对齐方式
        Dim projectManagerTextAlign As String
        projectManagerTextAlign = LCase(CStr(themeConfig("ProjectManagerTextAlign")))

        Select Case projectManagerTextAlign
            Case "left"
                .HorizontalAlignment = xlLeft
                .VerticalAlignment = xlCenter
            Case "right"
                .HorizontalAlignment = xlRight
                .VerticalAlignment = xlCenter
            Case "center"
                ' 居中对齐
                .HorizontalAlignment = xlCenter
                .VerticalAlignment = xlCenter
            Case Else
                ' 默认为水平垂直居中
                .HorizontalAlignment = xlCenter
                .VerticalAlignment = xlCenter
        End Select

        modDebug.LogInfo "应用项目经理名称区域文本对齐方式: " & projectManagerTextAlign, "modUI.ApplyThemeConfig"

        ' 设置行高
        rowHeightValue = CStr(themeConfig("ProjectManagerRowHeight"))
        If LCase(rowHeightValue) = "auto" Then
            ws.Rows(2).AutoFit
        Else
            ws.Rows(2).RowHeight = CDbl(rowHeightValue)
        End If

        modDebug.LogInfo "应用项目经理名称区域样式: B2:" & Split(ws.Cells(2, endCol).Address, "$")(1) & "2", "modUI.ApplyThemeConfig"
    End With

    ' 应用补充信息区域样式（B3:B5区域）
    With ws.Range(ws.Cells(3, 2), ws.Cells(5, 2))
        .Font.Name = CStr(themeConfig("SupplementInfoFont"))
        .Font.Size = CDbl(themeConfig("SupplementInfoFontSize"))
        .Font.Bold = CBool(themeConfig("SupplementInfoFontBold"))
        .Font.Color = modUtilities.GetRGBColor(CStr(themeConfig("SupplementInfoFontColor")))
        .Interior.Color = modUtilities.GetRGBColor(CStr(themeConfig("SupplementInfoBackColor")))
        ' 边框样式将在后面统一应用

        ' 设置水平垂直居中
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter

        ' 设置行高
        rowHeightValue = CStr(themeConfig("SupplementInfoRowHeight"))
        If LCase(rowHeightValue) = "auto" Then
            ws.Range(ws.Rows(3), ws.Rows(5)).AutoFit
        Else
            ws.Range(ws.Rows(3), ws.Rows(5)).RowHeight = CDbl(rowHeightValue)
        End If

        modDebug.LogInfo "应用补充信息区域样式: B3:B5", "modUI.ApplyThemeConfig"
    End With

    ' 应用时间轴表头年区域样式
    With ws.Range(ws.Cells(3, 3), ws.Cells(3, endCol))
        .Font.Name = CStr(themeConfig("TimelineYearFont"))
        .Font.Size = CDbl(themeConfig("TimelineYearFontSize"))
        .Font.Bold = CBool(themeConfig("TimelineYearFontBold"))
        .Font.Color = modUtilities.GetRGBColor(CStr(themeConfig("TimelineYearFontColor")))
        .Interior.Color = modUtilities.GetRGBColor(CStr(themeConfig("TimelineYearBackColor")))
        ' 边框样式将在后面统一应用

        ' 设置行高
        rowHeightValue = CStr(themeConfig("TimelineYearRowHeight"))
        If LCase(rowHeightValue) = "auto" Then
            ws.Rows(3).AutoFit
        Else
            ws.Rows(3).RowHeight = CDbl(rowHeightValue)
        End If
    End With

    ' 应用时间轴表头月区域样式
    With ws.Range(ws.Cells(4, 3), ws.Cells(4, endCol))
        .Font.Name = CStr(themeConfig("TimelineMonthFont"))
        .Font.Size = CDbl(themeConfig("TimelineMonthFontSize"))
        .Font.Bold = CBool(themeConfig("TimelineMonthFontBold"))
        .Font.Color = modUtilities.GetRGBColor(CStr(themeConfig("TimelineMonthFontColor")))
        .Interior.Color = modUtilities.GetRGBColor(CStr(themeConfig("TimelineMonthBackColor")))
        ' 边框样式将在后面统一应用

        ' 设置行高
        rowHeightValue = CStr(themeConfig("TimelineMonthRowHeight"))
        If LCase(rowHeightValue) = "auto" Then
            ws.Rows(4).AutoFit
        Else
            ws.Rows(4).RowHeight = CDbl(rowHeightValue)
        End If
    End With

    ' 应用时间轴表头周区域样式
    With ws.Range(ws.Cells(5, 3), ws.Cells(5, endCol))
        .Font.Name = CStr(themeConfig("TimelineWeekFont"))
        .Font.Size = CDbl(themeConfig("TimelineWeekFontSize"))
        .Font.Bold = CBool(themeConfig("TimelineWeekFontBold"))
        .Font.Color = modUtilities.GetRGBColor(CStr(themeConfig("TimelineWeekFontColor")))
        .Interior.Color = modUtilities.GetRGBColor(CStr(themeConfig("TimelineWeekBackColor")))
        ' 边框样式将在后面统一应用

        ' 设置行高
        rowHeightValue = CStr(themeConfig("TimelineWeekRowHeight"))
        If LCase(rowHeightValue) = "auto" Then
            ws.Rows(5).AutoFit
        Else
            ws.Rows(5).RowHeight = CDbl(rowHeightValue)
        End If
    End With

    ' 背景颜色已在函数开始时设置

    ' 设置交替行颜色（从第6行开始，每隔一行设置一次，仅在甘特图实际使用范围内）
    ' 注意：从C列开始，因为A列是边距列，B列是任务类别列，C列开始才是甘特图绘图区域
    lastCol = Split(ws.Cells(1, endCol).Address, "$")(1)

    For i = 6 To lastRow Step 2
        ws.Range("C" & i & ":" & lastCol & i).Interior.Color = modUtilities.GetRGBColor(alternateRowColor)
    Next i

    ' 关闭Excel默认网格线显示
    ws.Activate
    ActiveWindow.DisplayGridlines = False

    ' 定义区域范围

    ' 表头区域（第1-5行，从B列开始，A列保持默认）- 包括项目名称、项目经理、补充信息和时间轴表头
    Set headerRange = ws.Range(ws.Cells(1, 2), ws.Cells(5, endCol))
    modDebug.LogInfo "设置表头区域范围: B1:" & Split(ws.Cells(5, endCol).Address, "$")(1) & "5", "modUI.ApplyThemeConfig"

    ' 甘特图任务及里程碑绘图区域（第6行到最后一行，从C列开始）
    Set ganttDrawingRange = ws.Range(ws.Cells(6, 3), ws.Cells(lastRow, endCol))

    ' 任务类区域（第6行到最后一行，B列）
    Set taskCategoryRange = ws.Range(ws.Cells(6, 2), ws.Cells(lastRow, 2))

    ' 应用任务类区域样式
    With taskCategoryRange
        .Font.Name = CStr(themeConfig("CategoryFont"))
        .Font.Size = CDbl(themeConfig("CategoryFontSize"))
        .Font.Bold = CBool(themeConfig("CategoryFontBold"))
        .Font.Color = modUtilities.GetRGBColor(CStr(themeConfig("CategoryFontColor")))
        .Interior.Color = modUtilities.GetRGBColor(CStr(themeConfig("CategoryBackColor")))
        ' 边框样式将在后面统一应用
        .WrapText = CBool(themeConfig("CategoryWrapText"))
        .ColumnWidth = CDbl(themeConfig("CategoryColumnWidth"))

        ' 设置文本对齐方式
        Dim textAlign As String
        textAlign = LCase(CStr(themeConfig("CategoryTextAlign")))

        Select Case textAlign
            Case "left"
                .HorizontalAlignment = xlLeft
                .VerticalAlignment = xlCenter
            Case "right"
                .HorizontalAlignment = xlRight
                .VerticalAlignment = xlCenter
            Case "center"
                ' 居中对齐
                .HorizontalAlignment = xlCenter
                .VerticalAlignment = xlCenter
            Case Else
                ' 默认为水平垂直居中
                .HorizontalAlignment = xlCenter
                .VerticalAlignment = xlCenter
        End Select

        modDebug.LogInfo "应用任务类区域文本对齐方式: " & textAlign, "modUI.ApplyThemeConfig"
    End With

    ' 第一步：清除所有现有的边框
    headerRange.Borders.LineStyle = xlNone
    ganttDrawingRange.Borders.LineStyle = xlNone
    taskCategoryRange.Borders.LineStyle = xlNone
    ws.Range(ws.Cells(1, 2), ws.Cells(1, endCol)).Borders.LineStyle = xlNone  ' 项目名称区域（整行）
    ws.Range(ws.Cells(2, 2), ws.Cells(2, endCol)).Borders.LineStyle = xlNone  ' 项目经理名称区域（整行）
    ws.Range(ws.Cells(3, 2), ws.Cells(5, 2)).Borders.LineStyle = xlNone  ' 补充信息区域（B3:B5）
    ws.Range(ws.Cells(3, 3), ws.Cells(3, endCol)).Borders.LineStyle = xlNone  ' 时间轴表头年区域
    ws.Range(ws.Cells(4, 3), ws.Cells(4, endCol)).Borders.LineStyle = xlNone  ' 时间轴表头月区域
    ws.Range(ws.Cells(5, 3), ws.Cells(5, endCol)).Borders.LineStyle = xlNone  ' 时间轴表头周区域

    modDebug.LogInfo "已清除所有边框", "modUI.ApplyThemeConfig"

    ' 第二步：根据区域设置应用网格线
    Select Case LCase(gridlinesArea)
        Case "all"
            ' 所有区域都应用网格线
            ApplyGridlines headerRange, gridlinesType, gridlineColor
            ApplyGridlines ganttDrawingRange, gridlinesType, gridlineColor
            ApplyGridlines taskCategoryRange, gridlinesType, gridlineColor

        Case "header"
            ' 只在表头区域应用网格线
            ApplyGridlines headerRange, gridlinesType, gridlineColor

        Case "ganttdrawing"
            ' 只在甘特图任务及里程碑绘图区域应用网格线
            ApplyGridlines ganttDrawingRange, gridlinesType, gridlineColor

        Case "taskcategory"
            ' 只在任务类区域应用网格线
            ApplyGridlines taskCategoryRange, gridlinesType, gridlineColor

        Case "header,ganttdrawing"
            ' 在表头和甘特图任务及里程碑绘图区域应用网格线
            ApplyGridlines headerRange, gridlinesType, gridlineColor
            ApplyGridlines ganttDrawingRange, gridlinesType, gridlineColor

        Case "header,taskcategory"
            ' 在表头和任务类区域应用网格线
            ApplyGridlines headerRange, gridlinesType, gridlineColor
            ApplyGridlines taskCategoryRange, gridlinesType, gridlineColor

        Case "ganttdrawing,taskcategory"
            ' 在甘特图任务及里程碑绘图区域和任务类区域应用网格线
            ApplyGridlines ganttDrawingRange, gridlinesType, gridlineColor
            ApplyGridlines taskCategoryRange, gridlinesType, gridlineColor

        ' 兼容旧版本的区域名称
        Case "gantt"
            ' 只在甘特图任务及里程碑绘图区域应用网格线
            ApplyGridlines ganttDrawingRange, gridlinesType, gridlineColor

        Case "task"
            ' 只在任务类区域应用网格线
            ApplyGridlines taskCategoryRange, gridlinesType, gridlineColor

        Case "header,gantt"
            ' 在表头和甘特图任务及里程碑绘图区域应用网格线
            ApplyGridlines headerRange, gridlinesType, gridlineColor
            ApplyGridlines ganttDrawingRange, gridlinesType, gridlineColor

        Case "header,task"
            ' 在表头和任务类区域应用网格线
            ApplyGridlines headerRange, gridlinesType, gridlineColor
            ApplyGridlines taskCategoryRange, gridlinesType, gridlineColor

        Case "gantt,task"
            ' 在甘特图任务及里程碑绘图区域和任务类区域应用网格线
            ApplyGridlines ganttDrawingRange, gridlinesType, gridlineColor
            ApplyGridlines taskCategoryRange, gridlinesType, gridlineColor

        Case Else
            ' 默认不应用网格线
    End Select

    modDebug.LogInfo "已应用网格线", "modUI.ApplyThemeConfig"

    ' 第三步：应用各区域的边框样式
    ' 项目名称区域边框（整行，从B列到endCol）
    ApplyBorderStyle ws.Range(ws.Cells(1, 2), ws.Cells(1, endCol)).Cells, CStr(themeConfig("ProjectNameBorderStyle")), gridlineColor

    ' 项目经理名称区域边框（整行，从B列到endCol）
    ApplyBorderStyle ws.Range(ws.Cells(2, 2), ws.Cells(2, endCol)).Cells, CStr(themeConfig("ProjectManagerBorderStyle")), gridlineColor

    ' 补充信息区域边框（B3:B5）
    ApplyBorderStyle ws.Range(ws.Cells(3, 2), ws.Cells(5, 2)).Cells, CStr(themeConfig("SupplementInfoBorderStyle")), gridlineColor

    ' 时间轴表头年区域边框
    ApplyBorderStyle ws.Range(ws.Cells(3, 3), ws.Cells(3, endCol)).Cells, CStr(themeConfig("TimelineYearBorderStyle")), gridlineColor

    ' 时间轴表头月区域边框
    ApplyBorderStyle ws.Range(ws.Cells(4, 3), ws.Cells(4, endCol)).Cells, CStr(themeConfig("TimelineMonthBorderStyle")), gridlineColor

    ' 时间轴表头周区域边框
    ApplyBorderStyle ws.Range(ws.Cells(5, 3), ws.Cells(5, endCol)).Cells, CStr(themeConfig("TimelineWeekBorderStyle")), gridlineColor

    ' 任务类区域边框
    ApplyBorderStyle taskCategoryRange.Cells, CStr(themeConfig("CategoryBorderStyle")), gridlineColor

    modDebug.LogInfo "已应用各区域边框样式", "modUI.ApplyThemeConfig"

    ' 在应用所有样式后，添加logo
    AddLogoToGanttChart ws, themeConfig, endCol

    ' 在C6位置冻结窗格，保持时间轴表头和类别列始终可见
    modDebug.LogInfo "在C6位置冻结窗格", "modUI.ApplyThemeConfig"
    ws.Activate ' 确保工作表处于激活状态
    ws.Range("C6").Select ' 选择C6单元格

    ' 冻结窗格 - 保持C6左侧和上方的内容始终可见
    ActiveWindow.FreezePanes = False ' 先取消已有的冻结窗格
    ActiveWindow.FreezePanes = True ' 在当前选定单元格位置冻结窗格

    ' 取消选择，避免留下选择框
    ws.Range("A1").Select

    ' 作为最后一步，应用甘特图外边框，确保不会被其他边框设置覆盖
    ApplyGanttBorder ws, usedRng

    modDebug.LogFunctionExit "modUI.ApplyThemeConfig"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUI.ApplyThemeConfig"
    Err.Raise Err.Number, "modUI.ApplyThemeConfig", Err.Description
End Sub

' 在甘特图中添加logo
Private Sub AddLogoToGanttChart(ws As Worksheet, themeConfig As Dictionary, endCol As Long)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modUI.AddLogoToGanttChart"

    ' 检查是否启用logo
    Dim enableLogo As Boolean
    enableLogo = CBool(GetConfig("EnableLogo", True))

    If Not enableLogo Then
        modDebug.LogInfo "Logo功能已禁用，跳过添加logo", "modUI.AddLogoToGanttChart"
        modDebug.LogFunctionExit "modUI.AddLogoToGanttChart"
        Exit Sub
    End If

    ' 检查Config工作表中是否存在图片
    Dim configWs As Worksheet
    Dim logoShape As Shape
    Dim shp As Shape
    Dim foundPicture As Boolean

    Set configWs = ThisWorkbook.Worksheets("Config")
    foundPicture = False

    ' 检查Config工作表中是否有图片类型的对象
    If configWs.Shapes.Count = 0 Then
        modDebug.LogWarning "在Config工作表中未找到任何形状对象，跳过添加logo", "modUI.AddLogoToGanttChart"
        modDebug.LogFunctionExit "modUI.AddLogoToGanttChart"
        Exit Sub
    End If

    ' 遍历所有形状，查找图片类型的对象
    For Each shp In configWs.Shapes
        ' 检查是否为图片类型（类型为msoPicture）
        If shp.Type = msoPicture Then
            Set logoShape = shp
            foundPicture = True
            modDebug.LogInfo "在Config工作表中找到图片: " & shp.Name, "modUI.AddLogoToGanttChart"
            Exit For
        End If
    Next shp

    ' 如果没有找到图片类型的对象，则退出
    If Not foundPicture Then
        modDebug.LogWarning "在Config工作表中未找到任何图片类型的对象，跳过添加logo", "modUI.AddLogoToGanttChart"
        modDebug.LogFunctionExit "modUI.AddLogoToGanttChart"
        Exit Sub
    End If

    ' 如果图片名称不是"Logo"，则重命名为"Logo"
    If logoShape.Name <> "Logo" Then
        logoShape.Name = "Logo"
        modDebug.LogInfo "将Config工作表中的图片重命名为'Logo'", "modUI.AddLogoToGanttChart"
    End If

    ' 获取配置参数
    Dim logoMargin As Long
    logoMargin = CLng(GetConfig("LogoMargin", 2))

    modDebug.LogInfo "Logo配置: 边距=" & logoMargin, "modUI.AddLogoToGanttChart"

    ' 计算项目名称区域的高度和右上顶点坐标
    Dim nameAreaHeight As Double
    Dim targetTop As Double
    Dim targetLeft As Double
    Dim targetHeight As Double
    Dim targetWidth As Double
    Dim aspectRatio As Double

    ' 项目名称区域的高度（包括第1行和第2行）
    nameAreaHeight = ws.Rows("1:2").Height

    ' 项目名称区域的右上角坐标
    targetTop = ws.Cells(1, 2).Top + logoMargin
    targetLeft = ws.Cells(1, endCol).Left + ws.Cells(1, endCol).Width

    ' 计算logo的尺寸，保持宽高比
    aspectRatio = logoShape.Width / logoShape.Height

    ' 计算logo的目标高度（项目名称区域高度减去上下边距）
    targetHeight = nameAreaHeight - (logoMargin * 2)

    ' 根据宽高比计算宽度
    targetWidth = targetHeight * aspectRatio

    ' 复制logo到甘特图
    logoShape.Copy

    ' 粘贴logo到甘特图
    ws.Paste

    ' 获取新添加的logo
    Dim newLogo As Shape
    Set newLogo = ws.Shapes(ws.Shapes.Count)

    ' 设置logo的名称
    newLogo.Name = "ChartLogo"

    ' 设置logo的位置和尺寸
    With newLogo
        .Left = targetLeft - targetWidth - logoMargin
        .Top = targetTop
        .Height = targetHeight
        .Width = targetWidth

        ' 设置logo的其他属性
        .Placement = xlMoveAndSize  ' 随单元格调整大小和位置
        .PrintObject = True  ' 打印时包含此对象
    End With

    modDebug.LogInfo "Logo已添加到甘特图，位置: 左=" & newLogo.Left & ", 上=" & newLogo.Top & ", 宽=" & newLogo.Width & ", 高=" & newLogo.Height, "modUI.AddLogoToGanttChart"

    modDebug.LogFunctionExit "modUI.AddLogoToGanttChart"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUI.AddLogoToGanttChart"
    modDebug.LogFunctionExit "modUI.AddLogoToGanttChart", "失败 - " & Err.Description
End Sub

' 应用网格线辅助函数
Private Sub ApplyGridlines(targetRange As Range, gridlinesType As String, gridlineColor As String)
    On Error GoTo ErrorHandler

    ' 根据网格线类型设置
    Select Case LCase(gridlinesType)
        Case "horizontal"
            ' 只应用水平网格线
            With targetRange.Borders(xlInsideHorizontal)
                .LineStyle = xlContinuous
                .Color = modUtilities.GetRGBColor(gridlineColor)
                .Weight = xlThin
            End With

        Case "vertical"
            ' 只应用垂直网格线
            With targetRange.Borders(xlInsideVertical)
                .LineStyle = xlContinuous
                .Color = modUtilities.GetRGBColor(gridlineColor)
                .Weight = xlThin
            End With

        Case "all", "both"
            ' 应用水平和垂直网格线
            With targetRange.Borders(xlInsideHorizontal)
                .LineStyle = xlContinuous
                .Color = modUtilities.GetRGBColor(gridlineColor)
                .Weight = xlThin
            End With

            With targetRange.Borders(xlInsideVertical)
                .LineStyle = xlContinuous
                .Color = modUtilities.GetRGBColor(gridlineColor)
                .Weight = xlThin
            End With

        Case Else
            ' 默认不应用网格线
    End Select

    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUI.ApplyGridlines"
End Sub

' 应用边框样式
Private Sub ApplyBorderStyle(targetRange As Range, borderStyle As String, borderColor As String)
    On Error GoTo ErrorHandler

    ' 根据边框样式设置
    Select Case LCase(borderStyle)
        Case "none"
            ' 清除所有边框
            targetRange.Borders.LineStyle = xlNone

        Case "all"
            ' 应用外边框（上、下、左、右）
            With targetRange.BorderAround
                .LineStyle = xlContinuous
                .Color = modUtilities.GetRGBColor(borderColor)
                .Weight = xlThin
            End With

            ' 注意：内部边框由网格线设置控制，这里只设置外边框
            modDebug.LogInfo "应用全部外边框样式", "modUI.ApplyBorderStyle"

        Case "outline"
            ' 只应用外边框
            With targetRange.BorderAround
                .LineStyle = xlContinuous
                .Color = modUtilities.GetRGBColor(borderColor)
                .Weight = xlThin
            End With

            modDebug.LogInfo "应用外边框样式", "modUI.ApplyBorderStyle"

        Case "bottom"
            ' 只应用底部边框
            With targetRange.Borders(xlEdgeBottom)
                .LineStyle = xlContinuous
                .Color = modUtilities.GetRGBColor(borderColor)
                .Weight = xlThin
            End With

            modDebug.LogInfo "应用底部边框样式", "modUI.ApplyBorderStyle"

        Case Else
            ' 默认不应用边框
            modDebug.LogInfo "未应用边框样式（无效的样式值：" & borderStyle & "）", "modUI.ApplyBorderStyle"
    End Select

    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUI.ApplyBorderStyle"
End Sub

' 应用甘特图外边框
Private Sub ApplyGanttBorder(ws As Worksheet, usedRng As Range)
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modUI.ApplyGanttBorder"

    ' 获取甘特图外边框配置
    Dim enableGanttBorder As Boolean
    Dim ganttBorderColor As String
    Dim ganttBorderWeight As Integer
    Dim ganttBorderStyle As Integer

    ' 使用On Error Resume Next捕获可能的错误
    On Error Resume Next

    ' 获取是否启用甘特图外边框
    enableGanttBorder = CBool(GetConfig("EnableGanttBorder", True))
    If Err.Number <> 0 Then
        modDebug.LogError Err.Number, "获取EnableGanttBorder失败: " & Err.Description, "modUI.ApplyGanttBorder"
        enableGanttBorder = True ' 使用默认值
        Err.Clear
    End If

    ' 如果不启用甘特图外边框，则直接退出
    If Not enableGanttBorder Then
        modDebug.LogInfo "甘特图外边框已禁用，跳过应用", "modUI.ApplyGanttBorder"
        modDebug.LogFunctionExit "modUI.ApplyGanttBorder", "跳过 - 功能已禁用"
        Exit Sub
    End If

    ' 获取甘特图外边框颜色
    ganttBorderColor = CStr(GetConfig("GanttBorderColor", "#D3D3D3"))
    If Err.Number <> 0 Then
        modDebug.LogError Err.Number, "获取GanttBorderColor失败: " & Err.Description, "modUI.ApplyGanttBorder"
        ganttBorderColor = "#D3D3D3" ' 使用默认值（浅灰色）
        Err.Clear
    End If

    ' 获取甘特图外边框粗细
    ganttBorderWeight = CInt(GetConfig("GanttBorderWeight", 1))
    If Err.Number <> 0 Then
        modDebug.LogError Err.Number, "获取GanttBorderWeight失败: " & Err.Description, "modUI.ApplyGanttBorder"
        ganttBorderWeight = 1 ' 使用默认值（细）
        Err.Clear
    End If

    ' 获取甘特图外边框线型
    ganttBorderStyle = CInt(GetConfig("GanttBorderStyle", 1))
    If Err.Number <> 0 Then
        modDebug.LogError Err.Number, "获取GanttBorderStyle失败: " & Err.Description, "modUI.ApplyGanttBorder"
        ganttBorderStyle = 1 ' 使用默认值（实线）
        Err.Clear
    End If

    ' 恢复错误处理
    On Error GoTo ErrorHandler

    ' 检查usedRng对象是否有效
    If usedRng Is Nothing Then
        modDebug.LogError 91, "usedRng对象为Nothing", "modUI.ApplyGanttBorder"
        Exit Sub
    End If

    ' 检查usedRng是否为有效的区域
    If usedRng.Cells.Count <= 1 Then
        modDebug.LogError 91, "usedRng不是有效的多单元格区域", "modUI.ApplyGanttBorder"
        Exit Sub
    End If

    ' 使用更安全的方式应用边框
    On Error Resume Next
    Dim borderObj As Border
    Set borderObj = usedRng.Borders(xlEdgeLeft)
    Set borderObj = usedRng.Borders(xlEdgeTop)
    Set borderObj = usedRng.Borders(xlEdgeRight)
    Set borderObj = usedRng.Borders(xlEdgeBottom)

    If Err.Number <> 0 Then
        modDebug.LogError Err.Number, "无法获取边框对象: " & Err.Description, "modUI.ApplyGanttBorder"
        Err.Clear
        Exit Sub
    End If
    On Error GoTo ErrorHandler

    ' 应用甘特图外边框 - 分别设置四条边
    ' 设置线型
    Dim lineStyle As XlLineStyle
    Select Case ganttBorderStyle
        Case 1 ' 实线
            lineStyle = xlContinuous
        Case 2 ' 圆点
            lineStyle = xlDot
        Case 3 ' 虚线
            lineStyle = xlDash
        Case 4 ' 点划线
            lineStyle = xlDashDot
        Case 5 ' 双点划线
            lineStyle = xlDashDotDot
        Case 6 ' 长虚线 (使用虚线作为替代，Excel边框不支持长虚线)
            lineStyle = xlDash
        Case 7 ' 长点划线 (使用点划线作为替代)
            lineStyle = xlDashDot
        Case 8 ' 长双点划线 (使用双点划线作为替代)
            lineStyle = xlDashDotDot
        Case Else ' 默认实线
            lineStyle = xlContinuous
    End Select

    ' 设置粗细
    Dim weight As XlBorderWeight
    Select Case ganttBorderWeight
        Case 1 ' 细
            weight = xlThin
        Case 2 ' 中
            weight = xlMedium
        Case 3 ' 粗
            weight = xlThick
        Case 4 ' 极粗
            weight = xlHairline
        Case Else ' 默认细
            weight = xlThin
    End Select

    ' 设置颜色
    Dim colorValue As Long
    colorValue = modUtilities.GetRGBColor(ganttBorderColor)

    ' 应用到四条边
    With usedRng.Borders(xlEdgeLeft)
        .LineStyle = lineStyle
        .Color = colorValue
        .Weight = weight
    End With

    With usedRng.Borders(xlEdgeTop)
        .LineStyle = lineStyle
        .Color = colorValue
        .Weight = weight
    End With

    With usedRng.Borders(xlEdgeRight)
        .LineStyle = lineStyle
        .Color = colorValue
        .Weight = weight
    End With

    With usedRng.Borders(xlEdgeBottom)
        .LineStyle = lineStyle
        .Color = colorValue
        .Weight = weight
    End With

    modDebug.LogInfo "已应用甘特图外边框，颜色: " & ganttBorderColor & "，粗细: " & ganttBorderWeight & "，线型: " & ganttBorderStyle, "modUI.ApplyGanttBorder"
    modDebug.LogFunctionExit "modUI.ApplyGanttBorder", "成功"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUI.ApplyGanttBorder"
End Sub

' ---------------------------------------------------------
' 甘特图增强功能
' ---------------------------------------------------------

' 合并跨月周单元格
Public Sub MergeWeekColumns()
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modUI.MergeWeekColumns"

    ' 1. 激活GanttChart工作表
    Dim ws As Worksheet
    On Error Resume Next
    Set ws = ThisWorkbook.Worksheets("GanttChart")
    If Err.Number <> 0 Then
        MsgBox "未找到GanttChart工作表，无法执行合并操作。", vbExclamation, "错误"
        modDebug.LogError Err.Number, "未找到GanttChart工作表", "modUI.MergeWeekColumns"
        Exit Sub
    End If
    On Error GoTo ErrorHandler

    ws.Activate

    ' 2. 通过UsedRange获取甘特图中最后一行的行号和右边界列号
    Dim lastRow As Long
    Dim lastCol As Long

    ' 刷新使用范围
    Application.CalculateFull
    Dim temp As Range
    Set temp = ws.UsedRange

    lastRow = ws.UsedRange.Row + ws.UsedRange.Rows.Count - 1
    lastCol = ws.UsedRange.Column + ws.UsedRange.Columns.Count - 1

    modDebug.LogInfo "甘特图边界: 最后一行=" & lastRow & ", 最后一列=" & lastCol, "modUI.MergeWeekColumns"

    ' 3. 从D6单元格开始向右遍历，查找并合并跨月周单元格
    Dim col As Long
    Dim row As Long
    Dim prevColWidth As Double
    Dim currColWidth As Double
    Dim mergeRange As Range

    ' 关闭屏幕更新和警告，提高性能
    Application.ScreenUpdating = False
    Application.DisplayAlerts = False
    Application.Calculation = xlCalculationManual

    ' 获取第一列(D列)的宽度作为初始值
    prevColWidth = ws.Columns(4).ColumnWidth

    ' 记录找到的跨月周列位置
    Dim crossMonthCols As New Collection

    ' 第一步：找出所有跨月周的列位置
    For col = 4 To lastCol - 2
        ' 获取当前列的宽度
        currColWidth = ws.Columns(col).ColumnWidth

        ' 如果当前列宽度与前一列不同，说明是跨月周
        If currColWidth <> prevColWidth Then
            modDebug.LogInfo "在列 " & col & " 发现列宽变化: 前一列=" & prevColWidth & ", 当前列=" & currColWidth, "modUI.MergeWeekColumns"

            ' 将跨月周的起始列添加到集合中
            crossMonthCols.Add col

            ' 跳过下一列，因为它是跨月周的第二部分
            col = col + 1

            ' 如果不是最后一列，更新prevColWidth为下一列的宽度
            If col < lastCol Then
                prevColWidth = ws.Columns(col + 1).ColumnWidth
            End If
        Else
            ' 更新前一列宽度
            prevColWidth = currColWidth
        End If
    Next col

    ' 第二步：对每一行的跨月周单元格进行合并
    If crossMonthCols.Count > 0 Then
        modDebug.LogInfo "找到 " & crossMonthCols.Count & " 个跨月周位置", "modUI.MergeWeekColumns"

        ' 遍历每一行
        For row = 6 To lastRow
            ' 遍历每个跨月周位置
            For col = 1 To crossMonthCols.Count
                ' 获取跨月周的起始列
                Dim startCol As Long
                startCol = crossMonthCols(col)

                ' 创建要合并的范围(当前行的跨月周两列)
                Set mergeRange = ws.Range(ws.Cells(row, startCol), ws.Cells(row, startCol + 1))

                ' 合并单元格
                mergeRange.Merge

                modDebug.LogInfo "合并了第 " & row & " 行的跨月周单元格: 列 " & startCol & " 和 " & (startCol + 1), "modUI.MergeWeekColumns"
            Next col
        Next row
    Else
        modDebug.LogInfo "未找到跨月周位置", "modUI.MergeWeekColumns"
    End If

    ' 恢复屏幕更新和警告
    Application.ScreenUpdating = True
    Application.DisplayAlerts = True
    Application.Calculation = xlCalculationAutomatic

    ' 记录函数退出
    modDebug.LogFunctionExit "modUI.MergeWeekColumns", "成功"
    Exit Sub

ErrorHandler:
    ' 恢复屏幕更新和警告
    Application.ScreenUpdating = True
    Application.DisplayAlerts = True
    Application.Calculation = xlCalculationAutomatic

    modDebug.LogError Err.Number, Err.Description, "modUI.MergeWeekColumns"
    MsgBox "合并跨月周单元格时发生错误: " & Err.Description, vbExclamation, "错误"
End Sub

' ---------------------------------------------------------
' 配置表视觉预览函数
' ---------------------------------------------------------

' 为配置表中的Font和Color类别项提供视觉预览效果
Public Sub ApplyConfigTablePreview()
    On Error GoTo ErrorHandler

    ' 获取Config工作表
    Dim ws As Worksheet
    On Error Resume Next
    Set ws = ThisWorkbook.Worksheets("Config")
    On Error GoTo ErrorHandler

    If ws Is Nothing Then
        Exit Sub
    End If

    ' 获取configTable引用
    Dim tbl As ListObject
    On Error Resume Next
    Set tbl = ws.ListObjects("configTable")
    On Error GoTo ErrorHandler

    If tbl Is Nothing Then
        Exit Sub
    End If

    ' 获取列索引
    Dim categoryColIndex As Long
    Dim descriptionColIndex As Long
    Dim configValueColIndex As Long

    On Error Resume Next
    categoryColIndex = tbl.ListColumns("Category").Index
    descriptionColIndex = tbl.ListColumns("Description").Index
    configValueColIndex = tbl.ListColumns("ConfigValue").Index
    On Error GoTo ErrorHandler

    If categoryColIndex = 0 Or descriptionColIndex = 0 Or configValueColIndex = 0 Then
        Exit Sub
    End If

    ' 清除所有单元格格式
    tbl.DataBodyRange.Cells.Font.Name = "Arial"
    tbl.DataBodyRange.Cells.Font.size = 10
    tbl.DataBodyRange.Cells.Font.Bold = False
    tbl.DataBodyRange.Cells.Font.Italic = False
    tbl.DataBodyRange.Cells.Font.Underline = xlNone
    tbl.DataBodyRange.Cells.Font.Color = RGB(0, 0, 0)

    ' 只清除description列的背景色
    tbl.ListColumns("Description").DataBodyRange.Interior.ColorIndex = xlNone

    ' 如果表格没有数据，退出
    If tbl.DataBodyRange Is Nothing Then
        Exit Sub
    End If

    ' 遍历每一行
    Dim i As Long
    Dim category As String
    Dim configValue As Variant
    Dim cell As range

    For i = 1 To tbl.DataBodyRange.Rows.Count
        ' 获取类别和配置值
        category = tbl.DataBodyRange.Cells(i, categoryColIndex).value
        configValue = tbl.DataBodyRange.Cells(i, configValueColIndex).value

        ' 设置Description单元格引用
        Set cell = tbl.DataBodyRange.Cells(i, descriptionColIndex)

        ' 根据类别应用不同的格式
        Select Case UCase(category)
            Case "FONT"
                ' 应用字体预览
                ApplyFontPreview cell, CStr(configValue)

            Case "COLOR"
                ' 应用颜色预览
                ApplyColorPreview cell, CStr(configValue)
        End Select
    Next i
'清除ConfigValue列的背景色
    tbl.ListColumns("ConfigValue").DataBodyRange.Interior.ColorIndex = xlNone
    Exit Sub

ErrorHandler:
    ' 错误处理
End Sub

' 应用字体预览
Private Sub ApplyFontPreview(cell As range, configValue As String)
    On Error GoTo ErrorHandler

    ' 获取单元格所在行的数据
    Dim tbl As ListObject
    Dim row As Long
    Dim configNameCell As range
    Dim configName As String

    ' 获取表格引用和行索引
    Set tbl = cell.ListObject
    row = cell.row - tbl.HeaderRowRange.row

    ' 获取ConfigName单元格
    Set configNameCell = tbl.ListColumns("ConfigName").DataBodyRange.Cells(row, 1)
    configName = configNameCell.value

    ' 声明变量
    Dim originalText As String

    ' 根据ConfigName的后缀确定应用哪种字体属性
    If Right(configName, 4) = "Font" Then
        ' 检查字体是否存在
        If IsFontInstalled(configValue) Then
            ' 应用字体到单元格
            cell.Font.Name = configValue

            ' 保留原始描述文本，但添加字体示例
            originalText = cell.value

            ' 如果描述文本不包含示例，添加示例
            If InStr(1, originalText, "示例:") = 0 Then
                cell.value = originalText & " 示例: " & configValue
            End If
        End If
    ElseIf Right(configName, 8) = "FontSize" Then
        ' 应用字体大小
        On Error Resume Next
        cell.Font.size = CDbl(configValue)
        On Error GoTo ErrorHandler

        ' 更新描述文本
        Dim sizeText As String
        originalText = cell.value
        sizeText = configValue & "磅"

        If InStr(1, originalText, "示例:") = 0 Then
            cell.value = originalText & " 示例: " & sizeText
        End If
    ElseIf Right(configName, 8) = "FontBold" Then
        ' 应用粗体设置
        On Error Resume Next
        cell.Font.Bold = CBool(configValue)
        On Error GoTo ErrorHandler

        ' 更新描述文本
        Dim boldText As String
        originalText = cell.value
        boldText = IIf(CBool(configValue), "粗体", "常规")

        If InStr(1, originalText, "示例:") = 0 Then
            cell.value = originalText & " 示例: " & boldText
        End If
    ElseIf Right(configName, 5) = "Color" Then
        ' 应用颜色填充
        If left(configValue, 1) = "#" And Len(configValue) = 7 Then
            ' 应用颜色填充
            cell.Interior.Color = modUtilities.GetRGBColor(configValue)

            ' 根据颜色亮度设置文本颜色（深色背景用白色文本，浅色背景用黑色文本）
            If IsColorDark(modUtilities.GetRGBColor(configValue)) Then
                cell.Font.Color = RGB(255, 255, 255)
            Else
                cell.Font.Color = RGB(0, 0, 0)
            End If

            ' 更新描述文本
            originalText = cell.value

            If InStr(1, originalText, "示例:") = 0 Then
                cell.value = originalText & " 示例: " & configValue
            End If
        End If
    End If

    Exit Sub

ErrorHandler:
    ' 错误处理
End Sub

' 应用颜色预览
Private Sub ApplyColorPreview(cell As range, configValue As String)
    On Error GoTo ErrorHandler

    ' 获取ConfigValue单元格
    Dim configValueCell As range
    Dim tbl As ListObject
    Dim row As Long
    Dim configValueColIndex As Long
    Dim isValidHexColor As Boolean
    Dim rgbColor As Long

    ' 获取表格引用和行索引
    Set tbl = cell.ListObject
    row = cell.row - tbl.HeaderRowRange.row

    ' 获取ConfigValue列索引
    configValueColIndex = tbl.ListColumns("ConfigValue").Index

    ' 获取ConfigValue单元格
    Set configValueCell = tbl.DataBodyRange.Cells(row, configValueColIndex - tbl.ListColumns(1).Index + 1)

    ' 检查是否是有效的十六进制颜色代码
    isValidHexColor = (Not IsEmpty(configValue) And left(configValue, 1) = "#" And Len(configValue) = 7)

    ' 如果不是有效的十六进制颜色代码，直接提取单元格的Interior.Color
    If Not isValidHexColor Then
        ' 检查单元格是否有背景色
        If configValueCell.Interior.ColorIndex <> xlNone Then
            ' 获取单元格背景色的RGB值
            rgbColor = configValueCell.Interior.Color

            ' 获取十六进制值
            Dim hexColor As String
            hexColor = Right("000000" & Hex(rgbColor), 6)

            ' 反转十六进制值（BGR转RGB）
            hexColor = Right(hexColor, 2) & Mid(hexColor, 3, 2) & Left(hexColor, 2)

            ' 更新ConfigValue单元格的值（添加#前缀）
            configValueCell.value = "#" & hexColor
            configValue = "#" & hexColor

            isValidHexColor = True
        Else
            ' 如果单元格没有背景色，使用默认颜色（蓝色）
            configValue = "#3366CC" ' 默认蓝色
            configValueCell.value = configValue
            isValidHexColor = True
        End If
    End If

    ' 应用颜色预览
    ' 转换十六进制颜色为RGB
    rgbColor = modUtilities.GetRGBColor(configValue)

    ' 应用颜色填充
    cell.Interior.Color = rgbColor

    ' 根据颜色亮度设置文本颜色（深色背景用白色文本，浅色背景用黑色文本）
    If IsColorDark(rgbColor) Then
        cell.Font.Color = RGB(255, 255, 255)
    Else
        cell.Font.Color = RGB(0, 0, 0)
    End If

    Exit Sub

ErrorHandler:
    ' 错误处理
End Sub

' 检查字体是否已安装
Private Function IsFontInstalled(fontName As String) As Boolean
    On Error GoTo ErrorHandler

    Dim originalFont As String
    originalFont = Application.ActiveWorkbook.Styles("Normal").Font.Name

    ' 尝试设置字体
    Application.ActiveWorkbook.Styles("Normal").Font.Name = fontName

    ' 如果设置成功，字体存在
    IsFontInstalled = (Application.ActiveWorkbook.Styles("Normal").Font.Name = fontName)

    ' 恢复原始字体
    Application.ActiveWorkbook.Styles("Normal").Font.Name = originalFont

    Exit Function

ErrorHandler:
    IsFontInstalled = False
End Function

' 判断颜色是否为深色
Private Function IsColorDark(rgbColor As Long) As Boolean
    On Error GoTo ErrorHandler

    ' 提取RGB分量
    Dim r As Integer, g As Integer, b As Integer
    r = rgbColor Mod 256
    g = (rgbColor \ 256) Mod 256
    b = (rgbColor \ 65536) Mod 256

    ' 计算亮度（使用感知亮度公式）
    ' 亮度 = 0.299*R + 0.587*G + 0.114*B
    Dim brightness As Double
    brightness = (0.299 * r + 0.587 * g + 0.114 * b)

    ' 亮度小于128认为是深色
    IsColorDark = (brightness < 128)

    Exit Function

ErrorHandler:
    IsColorDark = False
End Function

' ---------------------------------------------------------
' 用户可直接调用的宏函数
' ---------------------------------------------------------

' 应用配置表预览的宏函数（可通过宏列表或快捷键调用）
Public Sub ApplyConfigPreview()
    On Error GoTo ErrorHandler

    ' 直接切换到Config工作表
    On Error Resume Next
    ThisWorkbook.Worksheets("Config").Activate
    If Err.Number <> 0 Then
        MsgBox "未找到Config工作表，无法应用配置预览。", vbExclamation, "错误"
        Exit Sub
    End If
    On Error GoTo ErrorHandler

    ' 调用配置预览函数
    ApplyConfigTablePreview

    Exit Sub

ErrorHandler:
    MsgBox "应用配置预览时发生错误: " & Err.description, vbExclamation, "错误"
End Sub



Sub get_colorHexx()
    Dim rng As Range
    Dim strHexCode As String
    'Ensure a cell range is selected
    If TypeName(Selection) <> "Range" Then Exit Sub
    'Loop through each cell in the selected area

    For Each rng In Selection.Cells
        'Ensure the cell has a background color
        If rng.Interior.ColorIndex <> xlNone Then
            'Get the hex value
            strHexCode = Right("000000" & Hex(rng.Interior.Color), 6)
            'Reverse the hex value
            strHexCode = Right(strHexCode, 2) & Mid(strHexCode, 3, 2) & Left(strHexCode, 2)
            'Add # and display the value in the adjacent cell to the right
            rng.Offset(0, 1).Value = "#" & strHexCode
        End If
    Next rng
    'Select only the active cell
    ActiveCell.Select
End Sub

Sub SetTaskFontToMicrosoftYaHei()
    ' Set the font for the "task" range to Microsoft YaHei
    Application.ScreenUpdating = False

    With Sheet1.Range("task").Font
        .Name = "Microsoft YaHei"
        .Size = 9
    End With

    Application.ScreenUpdating = True
End Sub

Sub ConditionalFormats()
    Dim rag As Range
    Range("VNE_OPL").FormatConditions.Delete 'DATES FORMAT

    Set rag = Range("VNE_OPL[['Baseline]:['Late III]]")
    With rag.FormatConditions.Add(Type:=xlExpression, Formula1:="=AND(L4=MAX($L4:$O4),$P4="""",INT(MAX($L4:$O4))-INT(TODAY())<1)") 'Red timeout
        .Interior.Color = &H505AEB
        .Font.Color = vbWhite
    End With

    With rag.FormatConditions.Add(Type:=xlExpression, Formula1:="=AND(L4=MAX($L4:$O4),$P4="""",INT(MAX($L4:$O4))-INT(TODAY())<7,INT(MAX($L4:$O4))-INT(TODAY())>-1)") 'Yellow warning
        .Interior.Color = &H88DDFF
        .Font.Color = vbBlack
    End With

    Set rag = Range("VNE_OPL[Status]") 'STATUS FORMAT

    With rag.FormatConditions.Add(Type:=xlExpression, Formula1:="=Q4=""Open""")
        .Interior.Color = &H88DDFF
    End With

    With rag.FormatConditions.Add(Type:=xlExpression, Formula1:="=Q4=""Open-Late""")
        .Interior.Color = &H505AEB
        .Font.Color = vbWhite
    End With

    Set rag = Range("VNE_OPL")
    With rag.FormatConditions.Add(Type:=xlExpression, Formula1:="=OR($Q4=""Closed"",$Q4=""Closed-Late"")") 'Need to be taken out separately
        .Interior.Color = &HAB9291
    End With

    Set rag = Range("VNE_OPL[Risk]") 'RISK FORMAT

    With rag.FormatConditions.Add(Type:=xlExpression, Formula1:="=G4=""H""")
        .Interior.Color = &H505AEB
        .Font.Color = vbWhite
    End With



    With rag.FormatConditions.Add(Type:=xlExpression, Formula1:="=G4=""M""")
        .Interior.Color = &H88DDFF
        .Font.Color = vbBlack
    End With

    With rag.FormatConditions.Add(Type:=xlExpression, Formula1:="=G4=""L""")
        .Interior.Color = &HB2BA6A
        .Font.Color = vbBlack
    End With
    
        Set rag = Range("VNE_OPL[NO.]") 'CPI RISK hight to NO. column with red

    With rag.FormatConditions.Add(Type:=xlExpression, Formula1:="=$W4=""Y""")
        .Interior.Color = &H202DC
        .Font.Color = vbWhite
    End With
End Sub
Sub apply_colorHexx_optimized()
    ' 禁用屏幕更新和自动计算以提高性能
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    Application.EnableEvents = False
    
    Dim rg As Range
    Dim x As String
    Dim i As Long
    Dim colorRange As Range, itemRange As Range
    Dim colorValues() As Variant
    Dim itemValues() As Variant
    Dim colorCount As Long
    Dim randomOffset As Long
    Dim hexColor As String
    Dim colorCache As Object
    Dim vbaHexRange As Range
    Dim randomIndex As Long
    Dim randomFontColor As Long
    
    ' 创建字典对象用于缓存颜色值，避免重复计算
    Set colorCache = CreateObject("Scripting.Dictionary")
    
    ' 获取范围引用 - 一次性获取所有需要的范围
    Set colorRange = Range("color[COLOR]")
    Set itemRange = Range("color[ITEM]")
    Set vbaHexRange = Range("color[VBA_HEX]")
    colorCount = itemRange.Rows.Count
    
    ' 一次性读取所有值到数组中，减少与Excel的交互
    colorValues = colorRange.Value
    itemValues = itemRange.Value
    
    ' 设置"color"范围的行高为16 - 一次性操作
    Range("color").RowHeight = 16
    
    ' 重置Sheet5的填充颜色为白色 - 一次性操作
    Sheet5.UsedRange.Interior.ColorIndex = 2
    
    ' 预先计算随机颜色索引范围
    Dim vbaHexRowStart As Long, vbaHexRowCount As Long
    vbaHexRowStart = vbaHexRange.Row
    vbaHexRowCount = vbaHexRange.Rows.Count
    
    ' 预先创建颜色应用数组
    Dim colorApplyArray() As Variant
    ReDim colorApplyArray(1 To colorCount, 1 To 3) ' 1=颜色值, 2=是否应用, 3=随机偏移量
    
    ' 预处理所有颜色值
    For i = 1 To colorCount
        If Not IsEmpty(colorValues(i, 1)) Then
            x = Right(CStr(colorValues(i, 1)), 6)
            
            ' 使用缓存避免重复计算
            If Not colorCache.Exists(x) Then
                hexColor = "&H" & Right(x, 2) & Mid(x, 3, 2) & Left(x, 2)
                colorCache.Add x, hexColor
            Else
                hexColor = colorCache(x)
            End If
            
            ' 存储处理结果
            colorApplyArray(i, 1) = hexColor
            colorApplyArray(i, 2) = True
            colorApplyArray(i, 3) = WorksheetFunction.RandBetween(1, 5) ' 减少随机范围
        End If
    Next i
    
    ' 批量应用颜色 - 减少循环次数
    For i = 1 To colorCount
        If colorApplyArray(i, 2) Then
            ' 应用背景色
            colorRange.Cells(i).Resize(1, 2 + colorApplyArray(i, 3)).Interior.Color = colorApplyArray(i, 1)
            
            ' 设置值和偏移值
            colorRange.Cells(i).Value = UCase(colorValues(i, 1))
            colorRange.Cells(i).Offset(0, 3).Value = colorApplyArray(i, 1)
        End If
    Next i
    
    ' 优化字体颜色设置 - 使用预计算的随机索引
    randomIndex = WorksheetFunction.RandBetween(vbaHexRowStart, vbaHexRowStart + vbaHexRowCount - 1)
    randomFontColor = Sheet5.Cells(randomIndex, vbaHexRange.Column).Value
    
    ' 批量设置字体颜色
    For Each rg In Range("color")
        If rg <> "" Then
            rg.Font.Color = randomFontColor
        End If
    Next
    
    ' 优化ITEM列处理 - 减少条件检查
    Dim prevItemValue As Variant
    Dim prevFontColor As Long
    
    ' 处理第一行
    If Not IsEmpty(itemValues(1, 1)) Then
        prevItemValue = itemValues(1, 1)
        prevFontColor = colorRange.Cells(1).Interior.Color
        itemRange.Cells(1).Font.Color = prevFontColor
    End If
    
    ' 处理剩余行
    For i = 2 To colorCount
        If IsEmpty(itemValues(i, 1)) Then
            ' 空值继承前一行
            itemRange.Cells(i).Value = prevItemValue
            itemRange.Cells(i).Font.Color = prevFontColor
        ElseIf itemValues(i, 1) = prevItemValue Then
            ' 相同值继承前一行的字体颜色
            itemRange.Cells(i).Font.Color = prevFontColor
        Else
            ' 不同值使用当前行的颜色
            prevItemValue = itemValues(i, 1)
            prevFontColor = colorRange.Cells(i).Interior.Color
            itemRange.Cells(i).Font.Color = prevFontColor
        End If
    Next i
    
    ' 恢复Excel设置
    Application.Calculation = xlCalculationAutomatic
    Application.EnableEvents = True
    Application.ScreenUpdating = True
End Sub
