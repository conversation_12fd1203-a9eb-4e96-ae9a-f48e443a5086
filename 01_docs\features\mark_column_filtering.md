# Mark列过滤功能

## 功能概述

Mark列是taskTable中的一个可选列，用于控制哪些任务会被绘制到甘特图中。这个功能允许用户灵活地选择要显示的任务，而不需要删除或修改原始数据。

## 工作原理

### 过滤逻辑

1. **存在Mark列**：
   - 如果Mark列存在且该行的Mark列**非空**：该任务会被包含在甘特图中
   - 如果Mark列存在但该行的Mark列**为空**：该任务会被跳过，不会出现在甘特图中

2. **不存在Mark列**：
   - 所有任务行都会被处理（向后兼容现有项目）

### 代码实现

```vba
' 检查Mark列过滤条件（如果Mark列存在）
If hasMarkCol Then
    Dim markColPos As Long
    markColPos = markColIndex - idColIndex + 1 ' 计算Mark列在数组中的相对位置
    
    ' 如果Mark列存在且该行的Mark列为空，则跳过此行
    If markColPos > 0 And markColPos <= UBound(dataArray, 2) Then
        If IsEmpty(dataArray(i, markColPos)) Or dataArray(i, markColPos) = "" Then
            modDebug.LogVerbose "跳过第 " & i & " 行任务，Mark列为空", "modData.GetAllTasks"
            GoTo NextIteration ' 跳过当前行，继续下一行
        End If
    End If
End If
```

## 使用场景

### 1. 项目阶段性展示
- 在Mark列中标记当前阶段的任务（如"Phase1"、"Phase2"）
- 只显示特定阶段的任务，便于阶段性汇报

### 2. 任务优先级筛选
- 在Mark列中标记高优先级任务（如"High"、"Critical"）
- 生成只包含重要任务的简化甘特图

### 3. 状态筛选
- 在Mark列中标记特定状态的任务（如"Active"、"Pending"）
- 根据任务状态生成不同的视图

### 4. 自定义视图
- 用户可以根据需要在Mark列中添加任何标记
- 灵活控制甘特图显示的内容

## 使用示例

### 示例1：阶段性展示

| ID | Category | Description | Type | Start Date | End Date | Mark |
|----|----------|-------------|------|------------|----------|------|
| T001 | 设计 | 需求分析 | A | 2024-01-01 | 2024-01-05 | Phase1 |
| T002 | 设计 | 系统设计 | A | 2024-01-06 | 2024-01-10 | Phase1 |
| T003 | 开发 | 编码实现 | A | 2024-01-11 | 2024-01-20 |  |
| T004 | 测试 | 单元测试 | A | 2024-01-21 | 2024-01-25 |  |

在这个例子中，只有T001和T002会被绘制到甘特图中。

### 示例2：优先级筛选

| ID | Category | Description | Type | Start Date | End Date | Mark |
|----|----------|-------------|------|------------|----------|------|
| T001 | 核心功能 | 登录模块 | A | 2024-01-01 | 2024-01-05 | High |
| T002 | 核心功能 | 数据处理 | A | 2024-01-06 | 2024-01-10 | High |
| T003 | 辅助功能 | 帮助文档 | A | 2024-01-11 | 2024-01-15 |  |
| T004 | 辅助功能 | 界面美化 | A | 2024-01-16 | 2024-01-20 |  |

在这个例子中，只有标记为"High"的核心功能任务会被显示。

## 技术特点

### 1. 向后兼容
- 如果taskTable中没有Mark列，系统会正常处理所有任务
- 现有项目无需修改即可继续使用

### 2. 性能优化
- Mark列的检查在数据读取阶段进行，避免不必要的任务字典创建
- 使用数组批量处理，提高过滤效率

### 3. 灵活性
- Mark列可以包含任何值（文本、数字、日期等）
- 只要非空即可，具体内容不影响过滤逻辑

### 4. 调试支持
- 提供详细的日志记录，显示过滤统计信息
- 便于调试和验证过滤效果

## 日志输出示例

```
2024-01-15 10:30:15 | INFO     | modData.GetAllTasks      | 检测到Mark列，将只处理Mark列非空的任务行
2024-01-15 10:30:15 | VERBOSE  | modData.GetAllTasks      | 跳过第 3 行任务，Mark列为空
2024-01-15 10:30:15 | VERBOSE  | modData.GetAllTasks      | 跳过第 4 行任务，Mark列为空
2024-01-15 10:30:15 | INFO     | modData.GetAllTasks      | 任务数据处理完成 - 总行数: 4, 实际处理任务数: 2
```

## 注意事项

1. **Mark列不会被添加到任务字典中**：Mark列仅用于过滤，不会影响甘特图绘制逻辑
2. **空值判断**：空字符串("")和Excel的空值(Empty)都被视为"空"
3. **列名大小写**：列名必须准确为"Mark"（区分大小写）
4. **数据类型**：Mark列可以是任何数据类型，只要非空即可
