# modConfigDefaults 模块设计文档

## 1. 模块概述

modConfigDefaults模块是系统的配置默认值管理中心，负责提供所有配置项的默认值。它集中管理所有默认值，确保即使配置表中的配置项不存在或被禁用，系统也能使用合理的默认值继续运行。

本模块采用统一的默认值管理方式，不再按模块分类管理配置项，而是将所有配置项视为唯一标识符，直接通过配置名称获取对应的默认值。

> 注意：代码中的注释和用户界面消息已更新为英文，以避免在VBA编辑器中出现编码问题。本设计文档仍保持中文说明，以便于中文用户理解。

## 2. 模块结构

```mermaid
classDiagram
    class modConfigDefaults {
        +GetDefaults() Dictionary
    }
```

## 3. 主要功能

### 3.1 默认值管理流程

```mermaid
flowchart TD
    Start([开始]) --> GetDefaults[获取所有默认值]
    GetDefaults --> InitializeDefaults[初始化默认值字典]
    InitializeDefaults --> AddUIDefaults[添加UI相关默认值]
    AddUIDefaults --> AddGanttDefaults[添加Gantt相关默认值]
    AddGanttDefaults --> AddDataDefaults[添加Data相关默认值]
    AddDataDefaults --> AddDebugDefaults[添加Debug相关默认值]
    AddDebugDefaults --> ReturnDefaults[返回默认值字典]
    ReturnDefaults --> End([结束])
```

## 4. 函数说明

### 4.1 公共函数

| 函数名 | 返回类型 | 参数 | 说明 |
|--------|----------|------|------|
| GetDefaults | Dictionary | 无 | 获取所有配置项的默认值 |

## 5. 默认值详情

所有配置项的默认值都集中在 `GetDefaults` 函数中定义，按照功能分组进行组织。每个配置项都有唯一的 ConfigName，与 `config_table.txt` 中的定义保持一致。

### 5.1 UI相关默认值

| ConfigName | 默认值 | 说明 |
|------------|--------|------|
| ProjectNameFont | Barlow | 项目名称字体 |
| ProjectNameFontSize | 18 | 项目名称字号 |
| ProjectNameFontBold | True | 项目名称是否粗体 |
| ProjectNameFontColor | #D3E7E3 | 项目名称字体颜色 |
| ProjectNameBackColor | #EB5A50 | 项目名称背景颜色 |
| ProjectNameBorderStyle | none | 项目名称边框样式 |
| ProjectNameRowHeight | auto | 项目名称行高 |
| ProjectNameTextAlign | left | 项目名称文本对齐方式 |
| ProjectManagerFont | Barlow | 项目经理信息字体 |
| ProjectManagerFontSize | 11 | 项目经理信息字号 |
| ProjectManagerFontBold | False | 项目经理信息是否粗体 |
| ProjectManagerFontColor | #D3E7E3 | 项目经理信息字体颜色 |
| ProjectManagerBackColor | #EB5A50 | 项目经理信息背景颜色 |
| ProjectManagerBorderStyle | none | 项目经理信息边框样式 |
| ProjectManagerRowHeight | auto | 项目经理信息行高 |
| ProjectManagerTextAlign | left | 项目经理信息文本对齐方式 |
| SupplementInfoFont | Barlow | 补充信息字体 |
| SupplementInfoFontSize | 9 | 补充信息字号 |
| SupplementInfoFontBold | False | 补充信息是否粗体 |
| SupplementInfoFontColor | #FFFFFF | 补充信息字体颜色 |
| SupplementInfoBackColor | #EB5A50 | 补充信息背景颜色 |
| SupplementInfoBorderStyle | none | 补充信息边框样式 |
| SupplementInfoRowHeight | auto | 补充信息行高 |
| TimelineYearFont | Arial | 时间轴年份字体 |
| TimelineYearFontSize | 11 | 时间轴年份字号 |
| TimelineYearFontBold | True | 时间轴年份是否粗体 |
| TimelineYearFontColor | #FFFFFF | 时间轴年份字体颜色 |
| TimelineYearBackColor | #EB5A50 | 时间轴年份背景颜色 |
| TimelineYearBorderStyle | all | 时间轴年份边框样式 |
| TimelineYearRowHeight | auto | 时间轴年份行高 |
| TimelineMonthFont | Arial | 时间轴月份字体 |
| TimelineMonthFontSize | 10 | 时间轴月份字号 |
| TimelineMonthFontBold | False | 时间轴月份是否粗体 |
| TimelineMonthFontColor | #FFFFFF | 时间轴月份字体颜色 |
| TimelineMonthBackColor | #EB5A50 | 时间轴月份背景颜色 |
| TimelineMonthBorderStyle | all | 时间轴月份边框样式 |
| TimelineMonthRowHeight | auto | 时间轴月份行高 |
| TimelineWeekFont | Arial | 时间轴周数字体 |
| TimelineWeekFontSize | 9 | 时间轴周数字号 |
| TimelineWeekFontBold | False | 时间轴周数是否粗体 |
| TimelineWeekFontColor | #FFFFFF | 时间轴周数字体颜色 |
| TimelineWeekBackColor | #EB5A50 | 时间轴周数背景颜色 |
| TimelineWeekBorderStyle | all | 时间轴周数边框样式 |
| TimelineWeekRowHeight | auto | 时间轴周数行高 |
| CategoryFont | Barlow | 任务类别字体 |
| CategoryFontSize | 11 | 任务类别字号 |
| CategoryFontBold | True | 任务类别是否粗体 |
| CategoryFontColor | #000000 | 任务类别字体颜色 |
| CategoryBackColor | #F2F2F2 | 任务类别背景颜色 |
| CategoryBorderStyle | outline | 任务类别边框样式 |
| CategoryColumnWidth | 30 | 任务类别列宽 |
| CategoryWrapText | True | 任务类别文本是否自动换行 |
| CategoryTextAlign | left | 任务类别文本对齐方式 |
| ChartTheme | custom | 甘特图主题 |
| ChartGridlinesArea | all | 网格线应用区域 |
| ChartGridlinesType | all | 网格线类型 |
| ChartGridlineColor | #DDDDDD | 网格线颜色 |
| ChartBackgroundColor | #FFFFFF | 甘特图背景颜色 |
| ChartAlternateRowColor | #F5F5F5 | 甘特图交替行颜色 |
| ColumnAWidth | 2 | A列宽度 |
| EnableLogo | True | 是否启用Logo |
| LogoMargin | 2 | Logo边距 |

### 5.2 Gantt相关默认值

| ConfigName | 默认值 | 说明 |
|------------|--------|------|
| CellWidthFactor | 0.8 | 甘特图时间单元格宽度放大系数 |
| DefaultTaskPosition | next | 默认任务位置 |
| DefaultTaskTextPosition | right | 默认任务文字位置 |
| DefaultMilestoneTextPosition | right | 默认里程碑文字位置 |
| TaskBarHeight | 11 | 任务条高度 |
| LabelDistance | 5 | 标签与任务条/里程碑的距离 |
| RowPadding | 3 | 行高上下预留空隙 |
| DefaultTaskColor | #3366CC | 默认任务条颜色 |
| DefaultMilestoneColor | #FF9900 | 默认里程碑颜色 |
| TaskProgressColor | #66CC66 | 任务进度颜色 |
| CurrentDateLineColor | #41B7AC | 当前日期线颜色 |
| ProgressBarColor | #66CC66 | 进度条颜色 |
| EnableCurrentDateLine | False | 是否启用当前日期线 |
| CurrentDateLineStyle | 2 | 当前日期线样式 |
| CurrentDateLineWeight | 0.8 | 当前日期线宽度 |
| BaselineColor | #FF0000 | 基准线颜色 |
| BaselineStyle | 2 | 基准线样式 |
| BaselineWeight | 0.8 | 基准线宽度 |
| TaskBarBorderWidth | 0 | 任务条/里程碑边框宽度 |
| MilestoneShapeStyle | 1 | 里程碑形状样式(1=菱形) |
| TaskBarShapeStyle | 1 | 任务条形状样式 |
| EnableSpotlight | True | 是否启用聚光灯效果 |
| SpotlightMode | all | 聚光灯模式(all=水平和垂直/horizontal=仅水平/vertical=仅垂直) |
| SpotlightColor | #E6F2FF | 聚光灯颜色(浅蓝色) |
| EnableGanttBorder | True | 是否启用甘特图外边框 |
| GanttBorderColor | #D3D3D3 | 甘特图外边框颜色(浅灰色) |
| GanttBorderWeight | 1 | 甘特图外边框宽度(1=细) |
| GanttBorderStyle | 1 | 甘特图外边框样式(1=实线) |

### 5.3 Data相关默认值

| ConfigName | 默认值 | 说明 |
|------------|--------|------|
| AutoCalculateDuration | True | 是否自动计算任务持续时间 |
| ExcludeWeekends | True | 计算持续时间时是否排除周末 |
| DefaultTaskProgress | 0 | 默认任务进度 |

### 5.4 Debug相关默认值

| ConfigName | 默认值 | 说明 |
|------------|--------|------|
| EnableDebug | False | 是否开启debug模式 |
| DebugLevel | 4 | 调试级别(1=错误,2=警告,3=信息,4=详细) |
| EnableFileLogging | False | 是否启用文件日志 |
| EnableImmediateOutput | False | 是否输出到即时窗口 |
| UTF8Encoding | True | 是否使用UTF-8编码日志文件 |

## 6. 使用示例

### 6.1 获取默认值

```vba
' 获取所有默认配置
Dim defaults As Dictionary
Set defaults = modConfigDefaults.GetDefaults()

' 使用默认配置
Dim fontName As String
fontName = defaults("ProjectNameFont") ' 返回 "Barlow"

' 获取特定类别的默认值
Dim spotlightMode As String
spotlightMode = defaults("SpotlightMode") ' 返回 "all"

' 检查是否启用某个功能
Dim enableSpotlight As Boolean
enableSpotlight = CBool(defaults("EnableSpotlight")) ' 返回 True
```

### 6.2 与GetConfig结合使用

```vba
' 获取配置，使用默认值
Dim fontName As String
fontName = GetConfig("ProjectNameFont", modConfigDefaults.GetDefaults()("ProjectNameFont"))

' 使用配置设置项目名称格式
With ws.Cells(1, 2)
    .Value = projectInfo("ProjectName")
    .Font.Name = fontName
    .Font.Size = GetConfig("ProjectNameFontSize", 18)
    .Font.Bold = GetConfig("ProjectNameFontBold", True)
End With

' 获取模块配置，使用默认值
Dim debugConfig As Dictionary
Set debugConfig = GetModuleConfig("Debug", modConfigDefaults.GetDefaults())

' 使用模块配置
Dim debugLevel As Integer
debugLevel = CInt(debugConfig("DebugLevel")) ' 返回 4

' 从配置字典中获取值
Dim enableFileLogging As Boolean
enableFileLogging = CBool(GetConfigFromDict(debugConfig, "EnableFileLogging", False))
```

## 7. 依赖关系

modConfigDefaults模块的依赖关系：

```mermaid
flowchart TD
    modConfigDefaults --> VBA[VBA标准库]
    modData --> modConfigDefaults
    modUI --> modConfigDefaults
    modGantt --> modConfigDefaults
    modDebug --> modConfigDefaults
```

## 8. 设计考量

1. **统一管理**：所有默认值都集中在一个函数中管理，避免重复定义和模块依赖。
2. **唯一标识**：每个配置项都有唯一的ConfigName，与配置表中的定义保持一致。
3. **一致性**：确保默认值与配置表中的值保持一致，避免混淆。
4. **可扩展性**：可以轻松添加新的默认值，支持系统扩展。
5. **性能优化**：通过一次性获取所有默认值，减少重复创建字典的开销。
