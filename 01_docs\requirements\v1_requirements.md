# Project Management Gantt Chart System - 需求规格说明书 (V1.0)

## 1. 引言

### 1.1 目的

本文档旨在详细描述Excel VBA项目管理甘特图系统（第一版）的功能需求和非功能需求，为系统开发提供明确的指导和参考。

### 1.2 范围

本文档涵盖系统的基本功能、用户界面、数据结构、性能要求等方面，主要针对第一版系统的开发。

### 1.3 定义和缩写

- **VBA**: Visual Basic for Applications，Excel内置的编程语言
- **甘特图**: 一种条形图，用于显示项目进度计划
- **里程碑**: 项目中的重要节点或事件
- **超级表**: Excel中的表格区域，具有自动扩展和格式化功能

## 2. 系统概述

### 2.1 系统目标

开发一个基于Excel VBA的项目管理甘特图系统，使用户能够创建、管理和可视化项目计划，包括任务、里程碑和它们的时间线。

### 2.2 系统功能概述

- 项目信息管理
- 任务和里程碑管理
- 甘特图可视化
- 数据验证和错误处理

### 2.3 用户特征

系统主要面向项目经理和团队成员，用户应具备基本的Excel操作技能。

### 2.4 运行环境

- Microsoft Excel 2016或更高版本
- 启用宏功能

## 3. 功能需求

### 3.1 项目信息管理

#### 3.1.1 项目基本信息

- 系统应允许用户在项目信息工作表中输入和编辑以下项目信息：
  - 项目名称
  - 项目经理
  - 项目开始日期
  - 项目结束日期
  - 项目描述

#### 3.1.2 项目信息验证

- 系统应验证项目信息的完整性和有效性，包括：
  - 必填字段检查（项目名称、开始日期、结束日期）
  - 日期有效性检查（开始日期不晚于结束日期）

### 3.2 任务和里程碑管理

#### 3.2.1 任务和里程碑数据输入

- 系统应允许用户在任务/里程碑工作表中输入和编辑以下信息：
  - 类型（所属大类）
  - 属性（A:活动/M:里程碑）
  - 描述
  - ID（可通过公式自动生成）
  - 开始日期
  - 结束日期（里程碑只参照开始日期）
  - 进度（完成百分比）
  - 分配给（负责人）
  - 颜色（填充颜色）
  - 位置（相对位置设置）
  - 文字位置

#### 3.2.2 数据验证

- 系统应验证任务和里程碑数据的有效性，包括：
  - 日期格式检查
  - 里程碑的开始日期和结束日期一致性检查
  - 进度值范围检查（0-100%）
  - 位置和文字位置值的有效性检查

### 3.3 甘特图生成

#### 3.3.1 甘特图创建

- 系统应能根据用户指令创建新的甘特图工作表，包括：
  - 检查并删除旧的甘特图工作表
  - 创建新的甘特图工作表
  - 设置工作表格式和布局

#### 3.3.2 表头生成

- 系统应在甘特图中生成以下表头：
  - B1：项目名称（大字体）
  - B2：项目经理信息（小字体）
  - B3-B5：最近更新时间和计划版本（合并单元格）
  - C3行起：年份(yyyy)
  - C4行起：月份(mm)
  - C5行起：周数(cwxx)，基于ISO 8601标准
  - 相邻相等的单元格需合并处理

#### 3.3.3 任务和里程碑可视化

- 系统应在甘特图中可视化任务和里程碑，包括：
  - B6行起：显示任务/里程碑所属大类（粗体）
  - 任务显示为水平条
  - 里程碑显示为菱形
  - 根据进度显示任务条的彩色部分
  - 根据设置的位置和文字位置显示任务和里程碑

### 3.4 错误处理

- 系统应提供错误处理机制，包括：
  - 捕获和记录错误信息
  - 向用户显示友好的错误消息
  - 提供错误恢复建议

## 4. 非功能需求

### 4.1 性能需求

- 系统应能处理至少100个任务和里程碑
- 甘特图生成时间不应超过10秒（对于标准规模的项目）

### 4.2 可用性需求

- 系统界面应简洁直观
- 操作流程应符合用户习惯
- 提供清晰的错误提示

### 4.3 可靠性需求

- 系统应稳定运行，不应因数据输入错误而崩溃
- 应提供数据验证机制，防止无效数据输入

### 4.4 兼容性需求

- 兼容Excel 2016及以上版本
- 在Windows操作系统上运行

## 5. 界面需求

### 5.1 工作表界面

#### 5.1.1 项目信息工作表

- 应提供清晰的字段标签和输入区域
- 应使用适当的数据验证控件（如日期选择器）

#### 5.1.2 任务/里程碑工作表

- 应使用表格形式展示任务和里程碑数据
- 应提供适当的数据验证和格式控制
- 里程碑信息通常集中在表格上方，任务信息在下方

#### 5.1.3 甘特图工作表

- 应按照4.4.1中描述的格式规范生成
- 应提供清晰的视觉区分（任务、里程碑、进度）
- 应使用适当的颜色和形状表示不同元素

### 5.2 用户交互

- 系统操作应通过Excel开发调试面板手动触发宏
- 不需要自动初始化，配置好模块后直接从模块中调试即可

## 6. 数据需求

### 6.1 数据存储

- 项目信息存储在项目信息工作表中
- 任务和里程碑数据存储在任务/里程碑工作表中
- 配置参数存储在配置工作表中

### 6.2 数据格式

- 日期应使用标准Excel日期格式
- 进度应使用百分比格式（0-100%）
- 颜色应使用标准Excel颜色代码或名称

## 7. 系统架构

### 7.1 工作表结构

- **项目信息工作表**：默认超级表模板，存储项目基本信息
- **任务/里程碑工作表**：默认超级表模板，存储所有任务和里程碑数据
- **甘特图工作表**：每次生成时重新创建，展示可视化甘特图
- **配置工作表**：存储系统配置参数，用于后续扩展开发

### 7.2 VBA模块结构

- **Main模块**：主控模块，系统入口
- **Gantt模块**：甘特图生成和管理
- **UI模块**：用户界面交互
- **Data模块**：数据处理和验证

## 8. 约束条件

- 系统应仅使用Excel VBA开发，不依赖外部库或组件
- 第一版本中，任务之间的依赖关系暂不考虑
- 前期开发直接从Excel开发面板调试，暂不需要用户窗体

## 9. 验收标准

系统应满足以下验收标准：

1. 能够正确存储和管理项目信息
2. 能够正确存储和管理任务和里程碑数据
3. 能够根据数据生成符合规范的甘特图
4. 能够正确处理各种错误情况
5. 满足所有功能需求和非功能需求

## 10. 附录

### 10.1 术语表

- **甘特图**：一种条形图，显示项目、进度和其他与时间相关的系统进展的内在关系随着时间进展的情况。
- **里程碑**：项目中的重要事件或节点，通常标志着项目阶段的完成。
- **超级表**：Excel中的表格区域，具有自动扩展和格式化功能。
- **ISO 8601**：国际标准化组织发布的日期和时间的表示法标准。

### 10.2 参考文档

- Excel VBA编程指南
- 项目管理最佳实践指南
