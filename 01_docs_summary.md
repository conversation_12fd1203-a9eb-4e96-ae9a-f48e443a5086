# Excel VBA 项目管理甘特图系统文档清单

本文档提供了 @01_docs/ 目录及其子目录中所有 .md 文件的清单，并根据 @02_code\Debug/ 目录中的实际代码实现进行了更新。

## 1. 总体文档

| 文件路径 | 文件描述 | 状态 |
|---------|---------|------|
| [README.md](README.md) | 项目总体介绍和使用指南 | 已更新 |
| [01_docs\General\README.md](01_docs\General\README.md) | 项目概述、功能特性、系统架构和使用指南 | 已更新 |
| [01_docs\General\project_structure.md](01_docs\General\project_structure.md) | 项目文档结构说明 | 已更新 |

## 2. 需求文档

| 文件路径 | 文件描述 | 状态 |
|---------|---------|------|
| [01_docs\Requirements\requirements.md](01_docs\Requirements\requirements.md) | 系统总体需求文档 | 已更新 |
| [01_docs\Requirements\v1_requirements.md](01_docs\Requirements\v1_requirements.md) | 第一版本详细需求规格说明书 | 已更新 |

## 3. 设计文档

### 3.1 系统架构设计

| 文件路径 | 文件描述 | 状态 |
|---------|---------|------|
| [01_docs\Design\architecture.md](01_docs\Design\architecture.md) | 系统整体架构设计 | 已更新 |
| [01_docs\Design\data_model.md](01_docs\Design\data_model.md) | 数据模型设计 | 已更新 |
| [01_docs\Design\ui_design.md](01_docs\Design\ui_design.md) | 用户界面设计 | 已更新 |
| [01_docs\Design\v1_system_design.md](01_docs\Design\v1_system_design.md) | 第一版本系统设计文档 | 已更新 |

### 3.2 模块设计

| 文件路径 | 文件描述 | 状态 |
|---------|---------|------|
| [01_docs\Design\module_design.md](01_docs\Design\module_design.md) | 模块总体设计 | 已更新 |
| [01_docs\Design\module_flow_diagrams.md](01_docs\Design\module_flow_diagrams.md) | 模块流程图 | 已更新 |
| [01_docs\Design\modData_design.md](01_docs\Design\modData_design.md) | 数据处理模块设计 | 已更新 |
| [01_docs\Design\modDebug_design.md](01_docs\Design\modDebug_design.md) | 调试模块设计 | 已更新，与实际代码一致 |
| [01_docs\Design\modUtilities_design.md](01_docs\Design\modUtilities_design.md) | 工具模块设计 | 已更新 |
| [01_docs\Design\modConfigDefaults_design.md](01_docs\Design\modConfigDefaults_design.md) | 配置默认值模块设计 | 已更新 |
| [01_docs\Design\ThisWorkbook_design.md](01_docs\Design\ThisWorkbook_design.md) | ThisWorkbook类设计 | 已更新 |

### 3.3 配置设计

| 文件路径 | 文件描述 | 状态 |
|---------|---------|------|
| [01_docs\Design\config_worksheet_new.md](01_docs\Design\config_worksheet_new.md) | 配置工作表设计（新版） | 已更新 |
| [01_docs\Design\config_worksheet_updated.md](01_docs\Design\config_worksheet_updated.md) | 配置工作表设计（更新版） | 已更新 |
| [01_docs\Design\task_fields_updated.md](01_docs\Design\task_fields_updated.md) | 任务字段定义（更新版） | 已更新 |
| [01_docs\Design\table_data_access.md](01_docs\Design\table_data_access.md) | 超级表数据访问示例 | 已更新 |

## 4. 调试模块文档

| 文件路径 | 文件描述 | 状态 |
|---------|---------|------|
| [01_docs\Debug\debug_config.md](01_docs\Debug\debug_config.md) | Debug模块配置说明 | 已更新，与实际代码一致 |

## 5. 甘特图功能文档

| 文件路径 | 文件描述 | 状态 |
|---------|---------|------|
| [01_docs\Gantt\CategoryTitleMerge_Function.md](01_docs\Gantt\CategoryTitleMerge_Function.md) | 类别标题合并功能说明 | 已更新 |
| [01_docs\Gantt\MilestoneVsTaskLabel_Differences.md](01_docs\Gantt\MilestoneVsTaskLabel_Differences.md) | 里程碑与任务条标签位置计算逻辑差异说明 | 已更新 |

## 6. 性能优化文档

| 文件路径 | 文件描述 | 状态 |
|---------|---------|------|
| [01_docs\Performance\optimization_summary.md](01_docs\Performance\optimization_summary.md) | 性能优化总结 | 已更新 |
| [01_docs\Performance\array_optimization.md](01_docs\Performance\array_optimization.md) | VBA中的数组优化技术 | 已更新 |

## 7. Debug模块代码实现

Debug模块的实际代码实现位于 `@02_code\Debug\` 目录中，主要包括以下文件：

| 文件路径 | 文件描述 | 状态 |
|---------|---------|------|
| [02_code\Debug\modDebug.bas](02_code\Debug\modDebug.bas) | Debug模块主要实现 | 已实现 |
| [02_code\Debug\modData.bas](02_code\Debug\modData.bas) | 数据处理模块 | 已实现 |
| [02_code\Debug\modMain.bas](02_code\Debug\modMain.bas) | 主控模块 | 已实现 |
| [02_code\Debug\modUtilities.bas](02_code\Debug\modUtilities.bas) | 工具函数模块 | 已实现 |
| [02_code\Debug\modConfigDefaults.bas](02_code\Debug\modConfigDefaults.bas) | 配置默认值模块 | 已实现 |
| [02_code\Debug\modUI.bas](02_code\Debug\modUI.bas) | 用户界面模块 | 已实现 |
| [02_code\Debug\modRibbon.bas](02_code\Debug\modRibbon.bas) | Ribbon界面模块 | 已实现 |

## 8. Debug模块主要功能

根据 `modDebug.bas` 的实际代码实现，Debug模块提供以下主要功能：

1. **多级日志记录**：支持错误(ERROR)、警告(WARNING)、信息(INFO)和详细(VERBOSE)四个级别的日志记录
2. **文件日志**：支持将日志输出到UTF-8编码的文本文件
3. **即时窗口输出**：支持将日志输出到VBE即时窗口
4. **函数进入/退出跟踪**：记录函数的进入和退出，便于跟踪代码执行流程
5. **配置化**：通过配置表控制调试功能的启用/禁用、日志级别、文件日志和即时窗口输出等

## 9. Debug模块配置参数

根据实际代码实现，Debug模块支持以下配置参数：

| 配置名称 | 默认值 | 说明 |
|---------|-------|------|
| EnableDebug | False | 是否开启debug模式 |
| DebugLevel | 4 | 调试级别(1=错误,2=警告,3=信息,4=详细) |
| EnableFileLogging | False | 是否启用文件日志 |
| EnableImmediateOutput | False | 是否输出到即时窗口 |
| UTF8Encoding | True | 是否使用UTF-8编码日志文件 |

## 10. Debug模块使用示例

```vba
' 初始化调试模块
modDebug.InitDebug level:=4, enableDebug:=True, enableFileLogging:=True

' 记录错误信息
modDebug.LogError Err.Number, Err.Description, "modMain.SomeFunction"

' 记录警告信息
modDebug.LogWarning "配置项未找到，使用默认值", "modData.GetConfigValue"

' 记录一般信息
modDebug.LogInfo "开始生成甘特图", "modMain.GenerateGanttChart"

' 记录详细跟踪信息
modDebug.LogVerbose "处理第 " & i & " 行数据", "modData.ProcessData"

' 记录函数进入
modDebug.LogFunctionEntry "modMain.SomeFunction", "param1=" & param1

' 记录函数退出
modDebug.LogFunctionExit "modMain.SomeFunction", "result=" & result
```
