# Project Management Gantt Chart System - 数据模型设计

## 1. 数据模型概述

本系统的数据模型基于Excel工作表实现，通过结构化的工作表设计存储项目管理所需的各类数据。数据模型设计遵循以下原则：
- **数据完整性**：确保数据的一致性和完整性，通过验证规则防止无效数据
- **易于访问**：提供简单的数据访问方法，使用字典和集合对象封装数据
- **高效存储**：优化数据存储结构，使用数组批量处理提高性能
- **可扩展性**：支持可选列和配置驱动设计，便于功能扩展
- **容错性**：提供默认值和错误处理机制，增强系统稳定性

## 2. 工作表数据结构

### 2.1 项目信息工作表 (ProjectInfo)

存储项目的基本信息，使用命名区域而非超级表。

| 命名区域 | 数据类型 | 说明 | 是否必填 |
|----------|----------|------|----------|
| projectName | String | 项目名称 | 是 |
| projectManager | String | 项目经理 | 是 |
| projectStartDate | Date | 项目开始日期 | 是 |
| projectEndDate | Date | 项目结束日期 | 是 |
| projectDescription | String | 项目描述 | 否 |

#### 2.1.1 数据访问方式

项目信息通过命名区域访问，并封装在字典对象中返回：

```vba
' 获取项目信息
Public Function GetProjectInfo() As Dictionary
    On Error GoTo ErrorHandler

    Dim projectInfo As New Dictionary

    ' 使用命名区域获取项目信息
    projectInfo.Add "ProjectName", Range("projectName").Value
    projectInfo.Add "ProjectManager", Range("projectManager").Value
    projectInfo.Add "StartDate", Range("projectStartDate").Value
    projectInfo.Add "EndDate", Range("projectEndDate").Value
    projectInfo.Add "Description", Range("projectDescription").Value

    Set GetProjectInfo = projectInfo
    Exit Function

ErrorHandler:
    ' 错误处理
    modUtilities.LogError Err.Number, Err.Description, "modData.GetProjectInfo"
    Set GetProjectInfo = New Dictionary ' 返回空字典
End Function
```

### 2.2 任务和里程碑工作表 (Milestones&WBS)

存储任务和里程碑数据，使用超级表 (ListObject) 名为 "taskTable"。

| 列名 | 数据类型 | 说明 | 是否必填 | 默认值 |
|------|----------|------|----------|--------|
| ID | 文本/数字 | 任务/里程碑的唯一标识符 | 是 | - |
| Category | 文本 | 所属类别 | 是 | - |
| Description | 文本 | 任务/里程碑描述 | 是 | - |
| Type | 文本 | 类型（A=任务活动，M=里程碑） | 是 | - |
| Start Date | 日期 | 开始日期 | 是 | - |
| End Date | 日期 | 结束日期（里程碑只参照开始日期） | 条件必填 | 对于里程碑，等于开始日期 |
| Duration | 数字 | 持续时间（天） | 自动计算 | 自动计算 |
| Progress | 百分比 | 完成百分比（0-100%） | 否 | 0 |
| Position | 文本/数字 | 相对于上一行的位置（same/next/数字） | 否 | next |
| Color | 文本/颜色代码 | 任务条/里程碑的填充颜色 | 否 | 任务:#3366CC<br>里程碑:#FF9900 |
| Text Position | 文本 | 文字相对于任务条/里程碑的位置 | 否 | 任务:right<br>里程碑:right |
| ShowDateInLabel | 文本 | 是否在标签中显示日期（Y=显示） | 否 | 空白（不显示） |
| Baseline | 日期 | 基准线日期 | 否 | - |

#### 2.2.1 数据访问方式

任务和里程碑数据通过超级表访问，使用数组优化批量读取，并封装在集合对象中返回：

```vba
' 获取所有任务/里程碑
Public Function GetAllTasks(Optional ByRef outBaselineCollection As Collection = Nothing) As Collection
    On Error GoTo ErrorHandler

    Dim tasks As New Collection
    Dim task As Dictionary
    Dim tbl As ListObject

    ' 获取超级表引用
    Set tbl = ThisWorkbook.Worksheets("Milestones&WBS").ListObjects("taskTable")

    ' 如果表格没有数据，返回空集合
    If tbl.DataBodyRange Is Nothing Then
        Set GetAllTasks = tasks
        Exit Function
    End If

    ' 获取数据范围
    Set dataRange = tbl.DataBodyRange

    ' 一次性读取整个数据区域到数组
    Dim dataArray As Variant
    dataArray = dataRange.Value
    Dim rowCount As Long
    rowCount = UBound(dataArray, 1)

    ' 遍历数组处理数据
    For i = 1 To rowCount
        Set task = New Dictionary

        ' 从数组中获取数据
        task.Add "ID", dataArray(i, 1)
        task.Add "Category", dataArray(i, 2)
        task.Add "Description", dataArray(i, 3)
        task.Add "Type", dataArray(i, 4)
        task.Add "StartDate", dataArray(i, 5)
        ' ... 其他字段

        tasks.Add task
    Next i

    Set GetAllTasks = tasks
    Exit Function

ErrorHandler:
    ' 错误处理
    Set GetAllTasks = New Collection ' 返回空集合
End Function
```

### 2.3 配置工作表 (Config)

存储系统配置，使用超级表 (ListObject) 名为 "configTable"。

| 列名 | 数据类型 | 说明 | 是否必填 |
|------|----------|------|----------|
| Module | 文本 | 配置所属模块 | 是 |
| ConfigName | 文本 | 配置名称 | 是 |
| ConfigValue | 变体 | 配置值 | 是 |
| IsEnabled | 布尔 | 是否启用 | 是 |
| Description | 文本 | 配置说明 | 否 |

#### 2.3.1 数据访问方式

配置数据通过超级表访问，使用数组优化批量读取，并提供多种访问方法：

```vba
' 获取特定模块的所有配置
Public Function GetModuleConfig(moduleName As String, Optional defaultValues As Dictionary = Nothing) As Dictionary
    On Error GoTo ErrorHandler

    Dim result As New Dictionary
    Dim tbl As ListObject

    ' 获取配置表引用
    Set tbl = ThisWorkbook.Worksheets("Config").ListObjects("configTable")

    ' 如果表格没有数据，返回默认值字典或空字典
    If tbl.DataBodyRange Is Nothing Then
        If Not defaultValues Is Nothing Then
            Set GetModuleConfig = defaultValues
        Else
            Set GetModuleConfig = result
        End If
        Exit Function
    End If

    ' 获取列索引
    Dim moduleCol As Long, enabledCol As Long, nameCol As Long, valueCol As Long
    moduleCol = tbl.ListColumns("Module").Index
    enabledCol = tbl.ListColumns("IsEnabled").Index
    nameCol = tbl.ListColumns("ConfigName").Index
    valueCol = tbl.ListColumns("ConfigValue").Index

    ' 一次性读取整个数据区域到数组
    Dim dataArray As Variant
    dataArray = tbl.DataBodyRange.Value

    ' 遍历数组处理数据
    For i = LBound(dataArray, 1) To UBound(dataArray, 1)
        ' 如果模块名称匹配且配置已启用
        If dataArray(i, moduleColArray) = moduleName And dataArray(i, enabledColArray) = True Then
            result.Add dataArray(i, nameColArray), dataArray(i, valueColArray)
        End If
    Next i

    ' 合并默认值
    If Not defaultValues Is Nothing Then
        For Each key In defaultValues.Keys
            If Not result.Exists(key) Then
                result.Add key, defaultValues(key)
            End If
        Next key
    End If

    Set GetModuleConfig = result
    Exit Function

ErrorHandler:
    ' 错误处理
    If Not defaultValues Is Nothing Then
        Set GetModuleConfig = defaultValues
    Else
        Set GetModuleConfig = New Dictionary
    End If
End Function

' 直接获取配置项的值
Public Function GetConfig(configName As String, Optional defaultValue As Variant = Null) As Variant
    On Error GoTo ErrorHandler

    Dim tbl As ListObject

    ' 获取配置表引用
    Set tbl = ThisWorkbook.Worksheets("Config").ListObjects("configTable")

    ' 如果表格没有数据，返回默认值
    If tbl.DataBodyRange Is Nothing Then
        GetConfig = defaultValue
        Exit Function
    End If

    ' 获取列索引
    Dim enabledCol As Long, nameCol As Long, valueCol As Long
    enabledCol = tbl.ListColumns("IsEnabled").Index
    nameCol = tbl.ListColumns("ConfigName").Index
    valueCol = tbl.ListColumns("ConfigValue").Index

    ' 一次性读取整个数据区域到数组
    Dim dataArray As Variant
    dataArray = tbl.DataBodyRange.Value

    ' 遍历数组处理数据
    For i = LBound(dataArray, 1) To UBound(dataArray, 1)
        ' 如果配置名称匹配且配置已启用
        If dataArray(i, nameColArray) = configName And dataArray(i, enabledColArray) = True Then
            GetConfig = dataArray(i, valueColArray)
            Exit Function
        End If
    Next i

    ' 如果未找到配置项，返回默认值
    GetConfig = defaultValue
    Exit Function

ErrorHandler:
    GetConfig = defaultValue
End Function
```

### 2.4 甘特图工作表 (GanttChart)

甘特图工作表是动态生成的，用于显示甘特图。它不存储持久数据，而是在每次生成甘特图时重新创建。

| 区域 | 说明 |
|------|------|
| A列 | 任务ID |
| B列 | 任务类别和描述 |
| C列及以后 | 时间轴和甘特图 |
| 第1-2行 | 项目信息 |
| 第3行 | 年份 |
| 第4行 | 月份 |
| 第5行 | 周数 |
| 第6行及以后 | 任务和里程碑 |

## 3. 数据关系

### 3.1 主要数据关系

- **项目与任务/里程碑**：一对多关系，一个项目包含多个任务和里程碑
- **类别与任务/里程碑**：一对多关系，一个类别包含多个任务和里程碑
- **配置模块与配置项**：一对多关系，一个模块包含多个配置项

### 3.2 关系图

```mermaid
erDiagram
    ProjectInfo ||--o{ Task : contains
    Category ||--o{ Task : groups
    Module ||--o{ ConfigItem : contains

    ProjectInfo {
        string projectName
        string projectManager
        date projectStartDate
        date projectEndDate
        string projectDescription
    }

    Task {
        string ID
        string Category
        string Description
        string Type
        date StartDate
        date EndDate
        int Duration
        double Progress
        string Position
        string Color
        string TextPosition
        date Baseline
        boolean ShowDateInLabel
    }

    Category {
        string Name
    }

    Module {
        string Name
    }

    ConfigItem {
        string Module
        string ConfigName
        variant ConfigValue
        boolean IsEnabled
        string Description
    }
```

## 4. 数据验证规则

### 4.1 项目信息验证
- projectName不能为空
- projectManager不能为空
- projectStartDate必须是有效日期
- projectEndDate必须是有效日期且不早于projectStartDate

### 4.2 任务和里程碑验证
- ID不能为空且必须唯一
- Category不能为空
- Description不能为空
- Type必须为"A"（任务活动）或"M"（里程碑）
- Start Date必须是有效日期
- End Date对于任务（Type="A"）必须是有效日期且不早于Start Date
- 对于里程碑（Type="M"），End Date会自动设置为等于Start Date
- Progress值必须在0-100之间

### 4.3 配置验证
- Module不能为空
- ConfigName不能为空且在同一模块内必须唯一
- IsEnabled必须是布尔值

### 4.4 验证实现

系统使用ValidateAllData函数验证所有数据，并在验证失败时显示错误消息：

```vba
' 验证所有数据
Public Function ValidateAllData() As Boolean
    On Error GoTo ErrorHandler

    Dim errorMessages As New Collection
    Dim errorCells As New Collection
    Dim errorCount As Long
    errorCount = 0

    ' 清除所有验证标记
    ClearAllValidationMarks

    ' 验证项目信息
    If Not ValidateProjectInfo(errorMessages, errorCells, errorCount) Then
        ValidateAllData = False
    End If

    ' 验证任务数据
    If Not ValidateTasksData(errorMessages, errorCells, errorCount) Then
        ValidateAllData = False
    End If

    ' 如果有错误，显示错误消息
    If errorCount > 0 Then
        Dim msg As String
        msg = "发现 " & errorCount & " 个数据验证错误:" & vbCrLf & vbCrLf

        For i = 1 To errorMessages.Count
            msg = msg & "- " & errorMessages(i) & vbCrLf
        Next i

        MsgBox msg, vbExclamation, "数据验证错误"
        ValidateAllData = False
    Else
        ValidateAllData = True
    End If

    Exit Function

ErrorHandler:
    MsgBox "验证数据时发生错误: " & Err.Description, vbCritical, "错误"
    ValidateAllData = False
End Function
```

## 5. 数据访问方法

### 5.1 数据读取函数

| 函数名 | 返回类型 | 参数 | 说明 |
|--------|----------|------|------|
| GetProjectInfo() | Dictionary | 无 | 获取项目信息，包括名称、经理、日期等 |
| GetAllTasks() | Collection | Optional ByRef outBaselineCollection As Collection | 获取所有任务和里程碑数据，可选返回基准线集合 |
| GetModuleConfig() | Dictionary | moduleName As String, Optional defaultValues As Dictionary | 获取特定模块的所有配置，支持默认值 |
| GetConfig() | Variant | configName As String, Optional defaultValue As Variant | 获取特定配置项的值，支持默认值 |
| GetConfigFromDict() | Variant | dict As Dictionary, configName As String, Optional defaultValue As Variant | 从配置字典中获取值，支持默认值 |

### 5.2 数据验证函数

| 函数名 | 返回类型 | 参数 | 说明 |
|--------|----------|------|------|
| ValidateAllData() | Boolean | 无 | 验证所有数据，包括项目信息和任务数据 |
| ValidateProjectInfo() | Boolean | ByRef errorMessages As Collection, ByRef errorCells As Collection, ByRef errorCount As Long | 验证项目信息 |
| ValidateTasksData() | Boolean | ByRef errorMessages As Collection, ByRef errorCells As Collection, ByRef errorCount As Long | 验证任务和里程碑数据 |

### 5.3 数据结构

系统使用以下数据结构在内存中表示和处理数据：

#### 5.3.1 项目信息字典

```vba
Dim projectInfo As Dictionary
' 键为字符串，值为相应的数据类型
projectInfo("ProjectName") = "项目名称"
projectInfo("ProjectManager") = "项目经理"
projectInfo("StartDate") = #1/1/2023#
projectInfo("EndDate") = #12/31/2023#
projectInfo("Description") = "项目描述"
```

#### 5.3.2 任务字典

```vba
Dim task As Dictionary
' 键为字符串，值为相应的数据类型
task("ID") = "1"
task("Category") = "项目启动"
task("Description") = "任务描述"
task("Type") = "A" ' A=任务活动，M=里程碑
task("StartDate") = #1/1/2023#
task("EndDate") = #1/15/2023#
task("Duration") = 15
task("Progress") = 0.5 ' 50%
task("Position") = "next"
task("Color") = "#3366CC"
task("TextPosition") = "right"
' 可选字段
If hasBaselineCol Then task("Baseline") = #1/10/2023#
If hasShowDateInLabelCol Then task("ShowDateInLabel") = "Y"
```

#### 5.3.3 任务集合

```vba
Dim tasks As Collection
' 集合中的每个元素都是一个任务字典
Set task1 = New Dictionary
' 设置task1的属性...
tasks.Add task1

Set task2 = New Dictionary
' 设置task2的属性...
tasks.Add task2
```

#### 5.3.4 配置字典

```vba
Dim config As Dictionary
' 键为配置名称，值为配置值
config("CellWidthFactor") = 1.2
config("DefaultTaskColor") = "#3366CC"
config("DefaultMilestoneColor") = "#FF9900"
config("ShowCurrentDateLine") = True
```

## 6. 数据存储优化

### 6.1 数组批量处理

系统使用数组批量处理数据，避免频繁访问Excel对象模型，显著提高性能：

```vba
' 一次性读取整个数据区域到数组
Dim dataArray As Variant
dataArray = dataRange.Value
Dim rowCount As Long
rowCount = UBound(dataArray, 1)

' 在内存中处理数据
For i = 1 To rowCount
    ' 处理每行数据
    ' ...
Next i
```

### 6.2 列索引映射

系统使用列索引映射，确保正确访问数组中的数据：

```vba
' 获取列索引（相对于ListObject）
Dim moduleCol As Long, enabledCol As Long, nameCol As Long, valueCol As Long
moduleCol = tbl.ListColumns("Module").Index
enabledCol = tbl.ListColumns("IsEnabled").Index
nameCol = tbl.ListColumns("ConfigName").Index
valueCol = tbl.ListColumns("ConfigValue").Index

' 计算数组中的相对列索引
Dim moduleColArray As Long, enabledColArray As Long, nameColArray As Long, valueColArray As Long
Dim firstColIndex As Long

' 获取ListObject第一列的索引
firstColIndex = tbl.ListColumns(1).Index

' 计算数组中的相对列索引
moduleColArray = moduleCol - firstColIndex + 1
enabledColArray = enabledCol - firstColIndex + 1
nameColArray = nameCol - firstColIndex + 1
valueColArray = valueCol - firstColIndex + 1
```

### 6.3 配置缓存

系统缓存频繁访问的配置值，避免重复查询：

```vba
' 一次性获取所有配置
Dim config As Dictionary
Set config = GetModuleConfig("Gantt")

' 使用缓存的配置
Dim cellWidthFactor As Double
cellWidthFactor = CDbl(GetConfigFromDict(config, "CellWidthFactor", 1.2))
```

### 6.4 可选列处理

系统支持可选列，通过错误处理检测列是否存在：

```vba
' 检查Baseline列是否存在
On Error Resume Next
baselineColIndex = tbl.ListColumns("Baseline").Index
hasBaselineCol = (Err.Number = 0)

' 检查ShowDateInLabel列是否存在
Err.Clear
showDateInLabelColIndex = tbl.ListColumns("ShowDateInLabel").Index
hasShowDateInLabelCol = (Err.Number = 0)
On Error GoTo ErrorHandler
```

## 7. 错误处理和日志记录

### 7.1 错误处理策略

系统使用标准的错误处理模式，确保数据操作的稳定性：

```vba
Public Function GetProjectInfo() As Dictionary
    On Error GoTo ErrorHandler

    ' 函数主体...
    Exit Function

ErrorHandler:
    modUtilities.LogError Err.Number, Err.Description, "modData.GetProjectInfo"
    Set GetProjectInfo = New Dictionary ' 返回空字典
End Function
```

### 7.2 日志记录

系统使用调试模块记录数据操作的详细信息：

```vba
' 记录函数进入
modDebug.LogFunctionEntry "modData.GetModuleConfig"

' 记录详细信息
modDebug.LogVerbose "列索引映射 - Module: " & moduleCol & "->" & moduleColArray & _
                   ", Enabled: " & enabledCol & "->" & enabledColArray & _
                   ", Name: " & nameCol & "->" & nameColArray & _
                   ", Value: " & valueCol & "->" & valueColArray, _
                   "modData.GetModuleConfig"

' 记录函数退出
modDebug.LogFunctionExit "modData.GetModuleConfig"
```

### 7.3 默认值机制

系统提供默认值机制，确保在数据缺失或错误时仍能正常运行：

```vba
' 获取配置值，支持默认值
Public Function GetConfig(configName As String, Optional defaultValue As Variant = Null) As Variant
    ' 如果未找到配置项，返回默认值
    GetConfig = defaultValue
End Function

' 获取模块配置，支持默认值字典
Public Function GetModuleConfig(moduleName As String, Optional defaultValues As Dictionary = Nothing) As Dictionary
    ' 如果出错且提供了默认值字典，返回默认值字典
    If Not defaultValues Is Nothing Then
        Set GetModuleConfig = defaultValues
    Else
        Set GetModuleConfig = New Dictionary ' 返回空字典
    End If
End Function
```
