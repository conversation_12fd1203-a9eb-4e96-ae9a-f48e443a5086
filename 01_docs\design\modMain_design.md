# modMain 模块设计文档

## 1. 模块概述

modMain模块是系统的入口点和控制中心，负责协调其他模块的工作，处理主要事件和错误。它是用户与系统交互的主要接口，负责调用其他模块完成甘特图的生成。

> 注意：代码中的注释和用户界面消息已更新为英文，以避免在VBA编辑器中出现编码问题。本设计文档仍保持中文说明，以便于中文用户理解。

## 2. 模块结构

```mermaid
classDiagram
    class modMain {
        +GenerateGanttChart()
        +HandleError(errNumber, errDescription, errSource)
        -ValidateData() Boolean
        -PrepareGanttSheet()
        -CreateGanttChart()
    }
```

## 3. 主要功能

### 3.1 甘特图生成流程

```mermaid
flowchart TD
    Start([开始]) --> GenerateGanttChart[生成甘特图主函数]

    GenerateGanttChart --> ValidateData{验证数据}
    ValidateData -->|验证失败| ShowError[显示错误消息]
    ValidateData -->|验证成功| PrepareGanttSheet[准备甘特图工作表]

    PrepareGanttSheet --> CreateGanttChart[创建甘特图]
    CreateGanttChart --> ShowSuccess[显示成功消息]

    ShowError --> End([结束])
    ShowSuccess --> End
```

### 3.2 模块交互流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Main as modMain
    participant Data as modData
    participant UI as modUI
    participant Gantt as modGantt

    User->>Main: 触发GenerateGanttChart()
    Main->>Data: ValidateAllData()
    Data-->>Main: 返回验证结果

    alt 数据验证成功
        Main->>UI: PrepareGanttWorksheet()
        UI-->>Main: 工作表准备完成

        Main->>Gantt: CreateGanttChart()
        Gantt-->>Main: 甘特图创建完成

        Main-->>User: 显示成功消息
    else 数据验证失败
        Main-->>User: 显示错误消息
    end
```

### 3.3 错误处理流程

```mermaid
flowchart TD
    Start([开始]) --> HandleError[处理错误]

    HandleError --> LogError[记录错误信息]
    LogError --> DisplayMessage[显示用户友好消息]
    DisplayMessage --> AttemptRecovery{尝试恢复?}

    AttemptRecovery -->|是| RecoveryAction[执行恢复操作]
    AttemptRecovery -->|否| End([结束])

    RecoveryAction --> End
```

## 4. 函数说明

### 4.1 公共函数

| 函数名 | 返回类型 | 参数 | 说明 |
|--------|----------|------|------|
| GenerateGanttChart | 无 | 无 | 生成甘特图的主函数，系统入口点 |
| HandleError | 无 | errNumber As Long, errDescription As String, errSource As String | 处理系统错误，记录错误信息并显示用户友好消息 |

### 4.2 私有函数

| 函数名 | 返回类型 | 参数 | 说明 |
|--------|----------|------|------|
| ValidateData | Boolean | 无 | 验证数据，调用modData.ValidateAllData() |
| PrepareGanttSheet | 无 | 无 | 准备甘特图工作表，调用modUI.PrepareGanttWorksheet() |
| CreateGanttChart | 无 | 无 | 创建甘特图，调用modGantt.CreateGanttChart() |

## 5. 事件处理

modMain模块可能需要处理以下Excel事件：

```vba
' 工作簿打开事件
Private Sub Workbook_Open()
    ' 初始化系统
    InitializeSystem
End Sub

' 工作表变更事件
Private Sub Workbook_SheetChange(ByVal Sh As Object, ByVal Target As Range)
    ' 处理数据变更
    If Sh.Name = "ProjectInfo" Or Sh.Name = "Milestones&WBS" Then
        ' 可能需要更新甘特图
    End If
End Sub
```

## 6. 用户界面交互

modMain模块负责与用户的交互，包括：

- 显示消息框
- 处理用户操作
- 响应按钮点击事件

```vba
' 显示成功消息（代码中使用英文）
MsgBox "Gantt chart generated successfully!", vbInformation, "Success"

' 显示错误消息（代码中使用英文）
MsgBox "Error occurred: " & errDescription, vbExclamation, "Error"
```

## 7. 错误处理

模块中的所有公共函数都应包含错误处理代码：

```vba
Public Sub GenerateGanttChart()
    On Error GoTo ErrorHandler

    ' 函数主体代码
    ' ...

    Exit Sub

ErrorHandler:
    ' 处理错误
    HandleError Err.Number, Err.Description, "modMain.GenerateGanttChart"
    Resume CleanUp  ' 注意：实际代码中使用了CleanUp标签进行资源清理
End Sub
```

## 8. 依赖关系

modMain模块的依赖关系：

```mermaid
flowchart TD
    modMain --> modData[modData]
    modMain --> modUI[modUI]
    modMain --> modGantt[modGantt]
    modMain --> modDebug[modDebug]
    modMain --> modUtilities[modUtilities]
    modMain --> Excel[Excel对象模型]
    modMain --> VBA[VBA标准库]
```
