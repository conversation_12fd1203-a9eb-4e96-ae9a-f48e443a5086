# modUtilities 模块设计文档

## 1. 模块概述

modUtilities模块提供通用工具函数，为其他模块提供支持。它包含日期处理、单元格操作、错误处理等通用功能，是系统的基础支持模块。

> 注意：代码中的注释和用户界面消息已更新为英文，以避免在VBA编辑器中出现编码问题。本设计文档仍保持中文说明，以便于中文用户理解。

## 2. 模块结构

```mermaid
classDiagram
    class modUtilities {
        +CalculateWorkingDays(startDate, endDate, excludeWeekends) Long
        +GetWeekNumber(inputDate) String
        +FormatDate(inputDate, formatString) String
        +MergeCells(ws, rangeAddress)
        +ApplyFormat(range, formatParams)
        +LogError(errNumber, errDescription, errSource)
        +IsInRange(value, minValue, maxValue) Boolean
        +GetCellAddress(ws, row, col) String
        +IsEmptyOrNull(value) Boolean
        +TrimAll(text) String
        +GetUniqueID(prefix) String
        +GetRGBColor(colorHex) Long
    }
```

## 3. 主要功能

### 3.1 日期处理

```mermaid
flowchart TD
    Start([开始]) --> DateProcessing[日期处理]

    DateProcessing --> FunctionType{函数类型}
    FunctionType -->|计算工作日| CalculateWorkingDays[计算工作日]
    FunctionType -->|获取周数| GetWeekNumber[获取周数]
    FunctionType -->|格式化日期| FormatDate[格式化日期]

    CalculateWorkingDays --> ExcludeWeekends{排除周末?}
    ExcludeWeekends -->|是| CalculateExcluding[排除周六周日]
    ExcludeWeekends -->|否| CalculateAll[计算所有天数]
    CalculateExcluding --> ReturnDays[返回工作日数]
    CalculateAll --> ReturnDays

    GetWeekNumber --> UseISO[使用ISO 8601标准]
    UseISO --> FormatCW[格式化为cwXX]
    FormatCW --> ReturnWeek[返回周数字符串]

    FormatDate --> ApplyFormat[应用日期格式]
    ApplyFormat --> ReturnFormatted[返回格式化日期]

    ReturnDays --> End([结束])
    ReturnWeek --> End
    ReturnFormatted --> End
```

### 3.2 单元格操作

```mermaid
flowchart TD
    Start([开始]) --> CellOperation[单元格操作]

    CellOperation --> OperationType{操作类型}
    OperationType -->|合并单元格| MergeCells[合并单元格]
    OperationType -->|应用格式| ApplyFormat[应用格式]
    OperationType -->|获取地址| GetCellAddress[获取单元格地址]

    MergeCells --> MergeRange[合并指定范围]
    MergeRange --> ReturnMerge[完成合并]

    ApplyFormat --> CheckParams{检查格式参数}
    CheckParams --> SetFont[设置字体属性]
    SetFont --> SetColors[设置颜色属性]
    SetColors --> SetAlignment[设置对齐方式]
    SetAlignment --> SetBorders[设置边框属性]
    SetBorders --> ReturnFormat[完成格式设置]

    GetCellAddress --> GetAddress[获取单元格地址]
    GetAddress --> ReturnAddress[返回地址字符串]

    ReturnMerge --> End([结束])
    ReturnFormat --> End
    ReturnAddress --> End
```

### 3.3 错误处理

```mermaid
flowchart TD
    Start([开始]) --> ErrorHandling[错误处理]

    ErrorHandling --> FunctionType{函数类型}
    FunctionType -->|记录错误| LogError[记录错误]
    FunctionType -->|检查范围| IsInRange[检查值是否在范围内]
    FunctionType -->|检查空值| IsEmptyOrNull[检查值是否为空或Null]

    LogError --> ForwardToDebug[转发到modDebug.LogError]
    ForwardToDebug --> ReturnLog[完成记录]

    IsInRange --> CheckBounds[检查上下界]
    CheckBounds --> ReturnBool[返回布尔结果]

    IsEmptyOrNull --> CheckValue[检查值状态]
    CheckValue --> ReturnEmpty[返回布尔结果]

    ReturnLog --> End([结束])
    ReturnBool --> End
    ReturnEmpty --> End
```

## 4. 函数说明

### 4.1 日期处理函数

| 函数名 | 返回类型 | 参数 | 说明 |
|--------|----------|------|------|
| CalculateWorkingDays | Long | startDate As Date, endDate As Date, [excludeWeekends As Boolean = True] | 计算两个日期之间的工作日数量，可选是否排除周末 |
| GetWeekNumber | String | inputDate As Date | 获取日期的周数，格式为"cwXX"，使用ISO 8601标准 |
| FormatDate | String | inputDate As Date, [formatString As String = "yyyy-mm-dd"] | 格式化日期为指定格式的字符串 |

### 4.2 单元格操作函数

| 函数名 | 返回类型 | 参数 | 说明 |
|--------|----------|------|------|
| MergeCells | 无 | ws As Worksheet, rangeAddress As String | 合并指定范围的单元格，使用地址字符串 |
| ApplyFormat | 无 | range As Range, formatParams As Dictionary | 应用格式到指定范围，支持字体、颜色、对齐和边框设置 |
| GetCellAddress | String | ws As Worksheet, row As Long, col As Long | 获取指定行列的单元格地址 |

### 4.3 错误处理函数

| 函数名 | 返回类型 | 参数 | 说明 |
|--------|----------|------|------|
| LogError | 无 | errNumber As Long, errDescription As String, errSource As String | 记录错误信息到日志，转发到modDebug.LogError |
| IsInRange | Boolean | value As Variant, minValue As Variant, maxValue As Variant | 检查值是否在指定范围内，包含上下界 |

### 4.4 其他工具函数

| 函数名 | 返回类型 | 参数 | 说明 |
|--------|----------|------|------|
| IsEmptyOrNull | Boolean | value As Variant | 检查值是否为空、Null或空字符串 |
| TrimAll | String | text As String | 去除字符串中的所有空白字符，使用WorksheetFunction.Trim |
| GetUniqueID | String | [prefix As String = ""] | 生成唯一ID，基于GUID，可选添加前缀 |
| GetRGBColor | Long | colorHex As String | 将十六进制颜色代码转换为RGB颜色值，支持#前缀 |

## 5. 实现示例

### 5.1 日期处理函数

```vba
' 计算两个日期之间的工作日数量
Public Function CalculateWorkingDays(startDate As Date, endDate As Date, Optional excludeWeekends As Boolean = True) As Long
    On Error GoTo ErrorHandler

    Dim days As Long
    Dim currentDate As Date

    days = 0
    currentDate = startDate

    ' 遍历每一天
    Do While currentDate <= endDate
        ' 检查是否为工作日
        If Not excludeWeekends Or (Weekday(currentDate) <> vbSaturday And Weekday(currentDate) <> vbSunday) Then
            days = days + 1
        End If

        ' 下一天
        currentDate = currentDate + 1
    Loop

    CalculateWorkingDays = days
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.CalculateWorkingDays"
    CalculateWorkingDays = 0 ' 返回默认值
End Function

' 获取日期的周数
Public Function GetWeekNumber(inputDate As Date) As String
    On Error GoTo ErrorHandler

    Dim weekNum As Integer

    ' 使用ISO 8601标准计算周数
    weekNum = DatePart("ww", inputDate, vbMonday, vbFirstFourDays)

    ' 格式化为"cwXX"
    GetWeekNumber = "cw" & Format(weekNum, "00")
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.GetWeekNumber"
    GetWeekNumber = "cw00" ' 返回默认值
End Function

' 格式化日期为指定格式的字符串
Public Function FormatDate(inputDate As Date, Optional formatString As String = "yyyy-mm-dd") As String
    On Error GoTo ErrorHandler

    FormatDate = Format(inputDate, formatString)
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.FormatDate"
    FormatDate = "" ' 返回默认值
End Function
```

### 5.2 单元格操作函数

```vba
' 合并指定范围的单元格
Public Sub MergeCells(ws As Worksheet, rangeAddress As String)
    On Error GoTo ErrorHandler

    ws.Range(rangeAddress).Merge
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.MergeCells"
End Sub

' 应用格式到指定范围
Public Sub ApplyFormat(range As Range, formatParams As Dictionary)
    On Error GoTo ErrorHandler

    ' 设置字体
    If formatParams.Exists("FontName") Then
        range.Font.Name = formatParams("FontName")
    End If

    If formatParams.Exists("FontSize") Then
        range.Font.Size = formatParams("FontSize")
    End If

    If formatParams.Exists("FontBold") Then
        range.Font.Bold = formatParams("FontBold")
    End If

    ' 设置颜色
    If formatParams.Exists("FontColor") Then
        range.Font.Color = formatParams("FontColor")
    End If

    If formatParams.Exists("BackColor") Then
        range.Interior.Color = formatParams("BackColor")
    End If

    ' 设置对齐
    If formatParams.Exists("HAlign") Then
        range.HorizontalAlignment = formatParams("HAlign")
    End If

    If formatParams.Exists("VAlign") Then
        range.VerticalAlignment = formatParams("VAlign")
    End If

    ' 设置边框
    If formatParams.Exists("BorderWeight") Then
        range.BorderAround Weight:=formatParams("BorderWeight")
    End If

    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.ApplyFormat"
End Sub

' 获取指定行列的单元格地址
Public Function GetCellAddress(ws As Worksheet, row As Long, col As Long) As String
    On Error GoTo ErrorHandler

    GetCellAddress = ws.Cells(row, col).Address
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.GetCellAddress"
    GetCellAddress = "" ' 返回默认值
End Function
```

### 5.3 错误处理函数

```vba
' 记录错误信息到日志 (转发到modDebug.LogError)
Public Sub LogError(errNumber As Long, errDescription As String, errSource As String)
    On Error Resume Next ' 避免日志记录本身出错

    ' 直接调用modDebug模块的LogError函数
    modDebug.LogError errNumber, errDescription, errSource
End Sub

' 检查值是否在指定范围内
Public Function IsInRange(value As Variant, minValue As Variant, maxValue As Variant) As Boolean
    On Error GoTo ErrorHandler

    IsInRange = (value >= minValue And value <= maxValue)
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.IsInRange"
    IsInRange = False ' 返回默认值
End Function
```

### 5.4 其他工具函数

```vba
' 检查值是否为空或Null
Public Function IsEmptyOrNull(value As Variant) As Boolean
    On Error GoTo ErrorHandler

    IsEmptyOrNull = (IsEmpty(value) Or IsNull(value) Or value = "")
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.IsEmptyOrNull"
    IsEmptyOrNull = True ' 返回默认值
End Function

' 去除字符串中的所有空白字符
Public Function TrimAll(text As String) As String
    On Error GoTo ErrorHandler

    TrimAll = Application.WorksheetFunction.Trim(text)
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.TrimAll"
    TrimAll = "" ' 返回默认值
End Function

' 生成唯一ID
Public Function GetUniqueID(Optional prefix As String = "") As String
    On Error GoTo ErrorHandler

    Dim guid As String
    guid = Mid(CreateObject("Scriptlet.TypeLib").GUID, 2, 36)

    If prefix <> "" Then
        GetUniqueID = prefix & "_" & guid
    Else
        GetUniqueID = guid
    End If

    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.GetUniqueID"
    GetUniqueID = Format(Now, "yyyymmddhhmmss") ' 返回基于时间的ID作为备选
End Function

' 获取RGB颜色值
Public Function GetRGBColor(colorHex As String) As Long
    On Error GoTo ErrorHandler

    Dim r As Long, g As Long, b As Long

    ' 移除可能的#前缀
    If Left(colorHex, 1) = "#" Then
        colorHex = Mid(colorHex, 2)
    End If

    ' 解析十六进制颜色值
    r = CLng("&H" & Mid(colorHex, 1, 2))
    g = CLng("&H" & Mid(colorHex, 3, 2))
    b = CLng("&H" & Mid(colorHex, 5, 2))

    ' 返回RGB值
    GetRGBColor = RGB(r, g, b)
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.GetRGBColor"
    GetRGBColor = RGB(0, 0, 0) ' 返回黑色作为默认值
End Function
```

## 6. 错误处理

### 6.1 标准错误处理模式

模块中的所有公共函数都应包含错误处理代码，使用以下标准模式：

```vba
Public Function SomeFunction(param1 As Type1, param2 As Type2) As ReturnType
    On Error GoTo ErrorHandler

    ' 函数主体代码
    ' ...

    ' 设置返回值并退出
    SomeFunction = result
    Exit Function

ErrorHandler:
    ' 记录错误
    modDebug.LogError Err.Number, Err.Description, "modUtilities.SomeFunction"

    ' 返回适当的默认值
    SomeFunction = defaultValue
End Function
```

### 6.2 错误处理策略

1. **所有公共函数必须包含错误处理**：确保每个公共函数都有错误处理代码，防止错误传播。

2. **使用默认返回值**：当发生错误时，函数应返回一个合理的默认值，而不是让错误继续传播。
   - 数值函数返回0或适当的默认数值
   - 字符串函数返回空字符串或适当的默认文本
   - 布尔函数通常返回False
   - 日期函数可返回当前日期或空日期

3. **记录所有错误**：使用modDebug.LogError记录所有错误，包括错误号、描述和来源。

4. **避免显示错误消息**：工具函数不应直接显示错误消息，而是记录错误并返回默认值，让调用者决定如何处理。

## 7. 依赖关系

### 7.1 模块依赖

modUtilities模块的依赖关系：

```mermaid
flowchart TD
    modUtilities --> modDebug[modDebug]
    modUtilities --> Excel[Excel对象模型]
    modUtilities --> VBA[VBA标准库]
    modUtilities --> FileSystem[文件系统]
    modUtilities --> Scriptlet[Scriptlet.TypeLib]
```

### 7.2 被依赖情况

几乎所有其他模块都依赖于modUtilities模块：

```mermaid
flowchart TD
    modData --> modUtilities
    modGantt --> modUtilities
    modUI --> modUtilities
    modMain --> modUtilities
    modRibbon --> modUtilities
    ThisWorkbook --> modUtilities
```

### 7.3 函数调用关系

| 调用方向 | 函数名 | 被调用模块.函数 |
|---------|--------|----------------|
| 调用 | LogError | modDebug.LogError |
| 被调用 | CalculateWorkingDays | modData.GetAllTasks |
| 被调用 | GetWeekNumber | modGantt.GenerateTimeline |
| 被调用 | FormatDate | 多个模块 |
| 被调用 | GetRGBColor | 多个模块 |
| 被调用 | IsEmptyOrNull | 多个模块 |
