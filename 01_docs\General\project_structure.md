# Project Management Gantt Chart System - 文档结构

## 1. 需求文档
- `requirements.md` - 系统功能需求和非功能需求

## 2. 设计文档
- `design/architecture.md` - 系统整体架构设计
- `design/data_model.md` - 数据模型设计
- `design/ui_design.md` - 用户界面设计
- `design/module_design.md` - 模块设计

## 3. 开发文档
- `development/coding_standards.md` - 编码规范
- `development/vba_modules.md` - VBA模块说明
- `development/userforms.md` - 用户表单说明

## 4. 测试文档
- `testing/test_plan.md` - 测试计划
- `testing/test_cases.md` - 测试用例
- `testing/test_results.md` - 测试结果

## 5. 用户文档
- `user_guide.md` - 用户使用指南
- `faq.md` - 常见问题解答

## 6. 版本控制
- `version_history.md` - 版本历史记录

## 7. 项目管理
- `project_plan.md` - 项目计划
- `meeting_notes/` - 会议记录目录
