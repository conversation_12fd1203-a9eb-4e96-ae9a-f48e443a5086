# 任务/里程碑工作表字段定义

**工作表名称**: `Milestones&WBS`
**超级表名称**: `taskTable`

| 字段名        | 数据类型  | 说明                                          | 是否必填 | 定义名称         |
| ------------- | --------- | --------------------------------------------- | -------- | ---------------- |
| ID            | 文本/数字 | 任务/里程碑的唯一标识符（可通过公式自动生成） | 是       | taskId           |
| Category      | 文本      | 所属父任务的大类，如"项目启动"                | 是       | taskCategory     |
| Description   | 文本      | 任务/里程碑的描述                             | 是       | taskDescription  |
| Type          | 文本      | A/M（A:activity任务活动，M:milestone里程碑）  | 是       | taskType         |
| Start Date    | 日期      | 任务开始的时间                                | 是       | taskStartDate    |
| End Date      | 日期      | 任务结束的时间（里程碑只参照开始日期）        | 条件必填 | taskEndDate      |
| Duration      | 数字      | 自动计算（天）                                | 自动计算 | taskDuration     |
| Progress      | 百分比    | 完成百分比（0-100%）                          | 否       | taskProgress     |
| Position      | 文本/数字 | 相对于上一行的位置（same/next/数字）          | 否       | taskPosition     |
| Color         | 文本/数字 | 任务条/里程碑的填充颜色                       | 否       | taskColor        |
| Text Position | 文本      | 文字相对于任务条/里程碑的位置                 | 否       | taskTextPosition |
| Baseline      | 日期      | 基准线日期，在甘特图中显示为垂直虚线          | 否       | taskBaseline     |
