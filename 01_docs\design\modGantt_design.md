# modGantt 模块设计文档

## 1. 模块概述

modGantt模块负责甘特图的生成和管理，是系统的核心功能模块。它根据项目信息和任务数据创建时间轴，绘制任务条和里程碑，应用格式和样式，最终生成可视化的甘特图。

> 注意：代码中的注释和用户界面消息已更新为英文，以避免在VBA编辑器中出现编码问题。本设计文档仍保持中文说明，以便于中文用户理解。

## 2. 模块结构

```mermaid
classDiagram
    class modGantt {
        +CreateGanttChart()
        -CreateGanttHeader(projectInfo)
        -CreateTimeline(startDate, endDate)
        -DrawTasksAndMilestones(tasks)
        -DetermineTaskRowAndCategory(task, ws, currentRow, lastTaskRow, taskIndex, currentCategory, isCurrentRowCategorySet) Long
        -CalculateTimeRange(startDate, endDate) Dictionary
        -DrawTask(task, row)
        -DrawMilestone(task, row)
        -DrawBaseline(baselineDate, ws, timelineCoords)
        -AddTaskLabelWithCoordinates(task, shape, centerX, centerY, shapeSize) Shape
        -EstablishTimelineCoordinateSystem(ws, projectInfo) Dictionary
        -CalculateXCoordinate(targetDate, coords) Double
        -GetLastColumnWithData(ws, rowNum) Long
        -GetRGBColor(hexColor) Long
        -MergeTimelineCells(ws, startCol, endCol)
        -MergeProjectInfoCells(ws, endCol)
    }
```

## 3. 主要功能

### 3.1 甘特图生成流程

```mermaid
flowchart TD
    Start([开始]) --> CreateGanttChart[创建甘特图]

    CreateGanttChart --> GetProjectInfo[获取项目信息]
    GetProjectInfo --> GetAllTasks[获取所有任务]

    GetAllTasks --> CreateTimeline[创建时间轴]
    CreateTimeline --> CreateGanttHeader[创建甘特图表头]
    CreateGanttHeader --> DrawTasksAndMilestones[绘制任务和里程碑]

    DrawTasksAndMilestones --> End([结束])
```

### 3.2 时间轴生成流程

```mermaid
flowchart TD
    Start([开始]) --> CreateTimeline[创建时间轴]

    CreateTimeline --> CalculateTimeRange[计算时间范围]
    CalculateTimeRange --> GenerateYearRow[生成年份行]
    GenerateYearRow --> GenerateMonthRow[生成月份行]
    GenerateMonthRow --> GenerateWeekRow[生成周数行]

    GenerateWeekRow --> MergeCells[合并相同单元格]
    MergeCells --> FormatTimeline[格式化时间轴]

    FormatTimeline --> End([结束])
```

### 3.3 任务绘制流程

```mermaid
flowchart TD
    Start([开始]) --> DrawTasksAndMilestones[绘制任务和里程碑]

    DrawTasksAndMilestones --> GetTasksData[获取任务数据]
    GetTasksData --> InitializeRowPosition[初始化行位置]
    InitializeRowPosition --> CollectBaselines[收集基准线信息]

    CollectBaselines --> ProcessTasks[处理每个任务]
    ProcessTasks --> DetermineRowAndCategory[确定行位置并处理类别]

    DetermineRowAndCategory --> UpdateRowPosition[更新行位置]
    UpdateRowPosition --> TaskType{任务类型?}

    TaskType -->|里程碑M| DrawMilestone[绘制里程碑]
    TaskType -->|任务A| DrawTask[绘制任务条]

    DrawMilestone --> MoreTasks{还有任务?}
    DrawTask --> MoreTasks

    MoreTasks -->|是| ProcessTasks
    MoreTasks -->|否| ProcessBaselines[处理所有基准线]

    ProcessBaselines --> End([结束])
```

## 4. 函数说明

### 4.1 公共函数

| 函数名 | 返回类型 | 参数 | 说明 |
|--------|----------|------|------|
| CreateGanttChart | 无 | 无 | 创建甘特图的主函数 |

### 4.2 私有函数

| 函数名 | 返回类型 | 参数 | 说明 |
|--------|----------|------|------|
| CreateGanttHeader | 无 | projectInfo As Dictionary | 创建甘特图表头，显示项目信息 |
| CreateTimeline | 无 | startDate As Date, endDate As Date | 创建时间轴，包括年份、月份和周数 |
| DrawTasksAndMilestones | 无 | tasks As Collection | 绘制所有任务和里程碑 |
| DetermineTaskRowAndCategory | Long | task As Dictionary, ws As Worksheet, currentRow As Long, lastTaskRow As Long, taskIndex As Long, ByRef currentCategory As String, ByRef isCurrentRowCategorySet As Boolean | 确定任务的行位置并处理类别信息，返回任务应该放置的行号 |
| CalculateTimeRange | Dictionary | startDate As Date, endDate As Date | 计算时间范围，返回包含年份、月份和周数信息的字典 |
| DrawTask | 无 | task As Dictionary, row As Long | 绘制任务条 |
| DrawMilestone | 无 | task As Dictionary, row As Long | 绘制里程碑 |
| DrawBaseline | 无 | baselineDate As Date, ws As Worksheet, timelineCoords As Dictionary | 绘制基准线，显示为垂直的红色虚线 |
| AddTaskLabelWithCoordinates | Shape | task As Dictionary, shape As Shape, centerX As Double, centerY As Double, shapeSize As Double | 使用坐标系添加任务标签 |
| EstablishTimelineCoordinateSystem | Dictionary | ws As Worksheet, projectInfo As Dictionary | 建立时间轴坐标系 |
| CalculateXCoordinate | Double | targetDate As Date, coords As Dictionary | 计算日期在坐标系中的X坐标 |
| GetLastColumnWithData | Long | ws As Worksheet, rowNum As Long | 获取指定行中有数据的最后一列 |
| GetRGBColor | Long | hexColor As String | 从十六进制颜色代码获取RGB颜色值 |
| MergeProjectInfoCells | 无 | ws As Worksheet, endCol As Long | 合并项目信息单元格 |

## 5. 关键算法

### 5.1 时间轴计算

时间轴计算是甘特图生成的关键算法之一，主要包括：

1. 计算项目开始日期到结束日期的时间跨度
2. 确定年份、月份和周数的范围
3. 计算每个时间单位在甘特图中的位置

```vba
' 计算时间范围
Private Function CalculateTimeRange(startDate As Date, endDate As Date) As Dictionary
    Dim result As New Dictionary

    ' 计算年份范围
    Dim startYear As Integer, endYear As Integer
    startYear = Year(startDate)
    endYear = Year(endDate)

    ' 计算月份范围
    ' ...

    ' 计算周数范围
    ' ...

    ' 返回结果
    Set CalculateTimeRange = result
End Function
```

### 5.2 日期到列位置的转换算法

日期到列位置的转换是甘特图生成的关键算法之一，主要包括：

1. 确定时间轴的起始日期和结束日期
2. 计算目标日期在时间范围内的相对位置
3. 根据相对位置计算对应的列号
4. 在计算得到的列附近搜索，找到年月最匹配的列

```vba
' 根据日期获取对应的列
Private Function GetColumnFromDate(targetDate As Date, projectStartDate As Date) As Long
    ' 获取时间轴的起始和结束日期
    Dim timelineStartDate As Date, timelineEndDate As Date
    timelineStartDate = projectStartDate

    ' 获取时间轴的结束日期（从工作表中读取）
    ' ...

    ' 计算目标日期在时间轴上的相对位置
    Dim totalDays As Long, targetDays As Long
    totalDays = DateDiff("d", timelineStartDate, timelineEndDate)
    targetDays = DateDiff("d", timelineStartDate, targetDate)

    ' 计算目标列
    Dim targetCol As Long
    targetCol = startCol + Round((endCol - startCol) * targetDays / totalDays)

    ' 在目标列附近搜索最佳匹配
    ' ...

    GetColumnFromDate = bestCol
End Function
```

## 6. 绘图方法

modGantt模块使用Excel的Shape对象进行绘图，主要包括：

1. 使用Shapes.AddShape方法创建任务条和里程碑
2. 设置Shape的属性，如位置、大小、颜色等
3. 添加文本标签

```vba
' 绘制任务条
Private Sub DrawTask(task As Dictionary, row As Long)
    On Error GoTo ErrorHandler

    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("GanttChart")

    ' 获取项目信息
    Dim projectInfo As Dictionary
    Set projectInfo = GetProjectInfo()

    ' 计算任务在时间轴上的位置
    Dim startCol As Long, endCol As Long
    startCol = GetColumnFromDate(task("StartDate"), projectInfo("StartDate"))
    endCol = GetColumnFromDate(task("EndDate"), projectInfo("StartDate"))

    ' 确保开始列不大于结束列
    If startCol > endCol Then
        Dim tempCol As Long
        tempCol = startCol
        startCol = endCol
        endCol = tempCol
    End If

    ' 确保列位置有效
    Dim lastCol As Long
    lastCol = GetLastColumnWithData(ws, 5)

    If endCol > lastCol Then
        endCol = lastCol
    End If

    ' 计算任务条的位置和大小
    Dim left As Double, top As Double, width As Double, height As Double
    left = ws.Cells(row, startCol).Left
    top = ws.Cells(row, startCol).Top
    width = ws.Cells(row, endCol).Left + ws.Cells(row, endCol).Width - left
    height = ws.Cells(row, startCol).Height

    ' 确保宽度为正值
    If width <= 0 Then
        width = ws.Cells(row, startCol).Width
    End If

    ' 创建任务条形状
    Dim taskShape As Shape
    Set taskShape = ws.Shapes.AddShape(msoShapeRectangle, left, top, width, height)

    ' 设置任务条格式
    Dim taskColor As String
    taskColor = task("Color")
    taskShape.Fill.ForeColor.RGB = GetRGBColor(taskColor)
    taskShape.Line.ForeColor.RGB = GetRGBColor(taskColor)

    ' 如果有进度，绘制进度条
    If task("Progress") > 0 Then
        Dim progressWidth As Double
        progressWidth = width * task("Progress")

        Dim progressShape As Shape
        Set progressShape = ws.Shapes.AddShape(msoShapeRectangle, left, top, progressWidth, height)

        progressShape.Fill.ForeColor.RGB = GetRGBColor(GetConfigValue("GT017", "#66CC66"))
        progressShape.Line.ForeColor.RGB = GetRGBColor(taskColor)

        ' 将进度条置于任务条下方
        progressShape.ZOrder msoSendBackward
    End If

    ' 添加任务标签
    AddTaskLabel task, taskShape, row, startCol, endCol

    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modGantt.DrawTask"
    Err.Raise Err.Number, "modGantt.DrawTask", Err.Description
End Sub

' 绘制里程碑
Private Sub DrawMilestone(task As Dictionary, row As Long)
    On Error GoTo ErrorHandler

    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("GanttChart")

    ' 获取项目信息
    Dim projectInfo As Dictionary
    Set projectInfo = GetProjectInfo()

    ' 计算里程碑在时间轴上的位置
    Dim col As Long
    col = GetColumnFromDate(task("StartDate"), projectInfo("StartDate"))

    ' 确保列位置有效
    Dim lastCol As Long
    lastCol = GetLastColumnWithData(ws, 5)

    If col > lastCol Then
        col = lastCol
    End If

    ' 计算里程碑的位置和大小
    Dim left As Double, top As Double, size As Double
    left = ws.Cells(row, col).Left + ws.Cells(row, col).Width / 2
    top = ws.Cells(row, col).Top + ws.Cells(row, col).Height / 2
    size = ws.Cells(row, col).Height * 0.8

    ' 创建里程碑形状（菱形）
    Dim milestoneShape As Shape
    Set milestoneShape = ws.Shapes.AddShape(msoShapeDiamond, left - size / 2, top - size / 2, size, size)

    ' 设置里程碑格式
    Dim milestoneColor As String
    milestoneColor = task("Color")
    milestoneShape.Fill.ForeColor.RGB = GetRGBColor(milestoneColor)
    milestoneShape.Line.ForeColor.RGB = GetRGBColor(milestoneColor)

    ' 添加里程碑标签
    AddTaskLabel task, milestoneShape, row, col, col

    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modGantt.DrawMilestone"
    Err.Raise Err.Number, "modGantt.DrawMilestone", Err.Description
End Sub
```

## 7. 错误处理

模块中的所有公共函数都应包含错误处理代码：

```vba
Public Sub CreateGanttChart()
    On Error GoTo ErrorHandler

    ' 记录函数进入
    modDebug.LogFunctionEntry "modGantt.CreateGanttChart"

    ' 函数主体代码
    ' ...

    ' 记录函数退出
    modDebug.LogFunctionExit "modGantt.CreateGanttChart", "成功"
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modGantt.CreateGanttChart"
    Err.Raise Err.Number, "modGantt.CreateGanttChart", Err.Description
End Sub
```

## 8. 依赖关系

modGantt模块的依赖关系：

```mermaid
flowchart TD
    modGantt --> modData[modData]
    modGantt --> modDebug[modDebug]
    modGantt --> Excel[Excel对象模型]
    modGantt --> VBA[VBA标准库]
```

## 9. 最新改进

### 9.1 任务行位置和类别处理的整合

在最新版本中，我们整合了任务行位置计算和类别处理功能，创建了新的`DetermineTaskRowAndCategory`函数：

1. 将原来分离的`DetermineTaskRow`和`ProcessTaskCategory`函数整合为一个函数
2. 消除了`isFirstTaskProcessed`标记，直接使用`taskIndex > 1`判断是否是首个任务
3. 提高了代码一致性，行位置计算和类别处理在同一个函数中完成
4. 简化了调用方式，减少了变量和参数的数量
5. 统一了错误处理和日志记录

```mermaid
flowchart TD
    A[开始] --> B[获取任务Position属性]
    B --> C[根据Position确定行位置]
    C --> D[获取任务Category属性]
    D --> E{是新行或首个任务?}
    E -->|是| F[重置行类别设置状态]
    E -->|否| G[保持行类别设置状态]
    F --> H{当前行类别已设置?}
    G --> H
    H -->|否| I{任务有非空类别?}
    H -->|是| L[保持当前类别]
    I -->|是| J[更新当前类别]
    I -->|否| L
    J --> K{是新行或首个任务?}
    K -->|是| M[添加类别标题]
    K -->|否| L
    M --> L
    L --> N[返回行位置]
    N --> O[结束]
```

### 9.2 日期到列位置转换算法的改进

在最新版本中，我们对`GetColumnFromDate`函数进行了重大改进，使其能够更准确地将日期映射到甘特图中的列位置：

1. 使用线性插值算法，根据日期在项目时间范围内的相对位置计算列位置
2. 增加了在计算得到的列附近搜索最匹配列的功能
3. 添加了更多的错误处理和调试信息
4. 确保即使找不到精确匹配，也能返回一个合理的列位置

### 9.3 任务和里程碑绘制的改进

1. 修改了`DrawTask`和`DrawMilestone`函数的签名，移除了不再使用的参数
2. 增强了错误处理和调试信息
3. 添加了对任务宽度的检查，确保宽度为正值
4. 改进了对列位置超出时间轴范围的处理

### 9.4 调试功能的增强

1. 添加了更详细的日志记录，帮助诊断问题
2. 每次生成甘特图时重新初始化日志文件，使调试更加清晰
3. 添加了对异常情况的更好处理

### 9.5 基准线功能的添加

1. 添加了对任务基准线的支持，可以在甘特图中显示垂直的基准线
2. 基准线通过任务表中的"Baseline"列进行配置，值为日期或留空
3. 基准线显示为垂直的红色虚线，长度不超出甘特图区域
4. 基准线的颜色、线型和线宽可通过配置项进行自定义
5. 基准线的绘制在所有任务和里程碑绘制完成后进行

```mermaid
flowchart TD
    Start([开始]) --> DrawBaseline[绘制基准线]
    DrawBaseline --> CheckDate{日期在范围内?}
    CheckDate -->|否| Exit[退出]
    CheckDate -->|是| CalculateX[计算X坐标]
    CalculateX --> GetBoundaries[获取甘特图边界]
    GetBoundaries --> CreateLine[创建线形状]
    CreateLine --> FormatLine[设置线格式]
    FormatLine --> End([结束])
```
