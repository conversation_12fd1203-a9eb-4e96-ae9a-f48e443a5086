# 配置工作表设计

配置工作表用于存储系统配置参数，为系统提供可配置的选项，便于后续扩展开发。第一版本需要考虑甘特图中项目名称/项目经理信息，以及时间表头部分的字体、颜色、字号以及甘特图时间最小单元的宽度放大系数。

**工作表名称**: `Config`

## 配置表结构

| 字段名 | 数据类型 | 说明 | 默认值 | 所属模块 |
| ------ | -------- | ---- | ------ | -------- |
| ConfigID | 文本 | 配置项的唯一标识符 | - | - |
| ConfigName | 文本 | 配置项的名称 | - | - |
| ConfigValue | 文本/数字 | 配置项的值 | - | - |
| Description | 文本 | 配置项的描述 | - | - |
| Module | 文本 | 所属模块（UI/Gantt/Data/Main） | - | - |
| Category | 文本 | 配置类别 | - | - |
| IsEnabled | 布尔值 | 是否启用该配置项。当设置为False时，GetModuleConfig函数会忽略该配置项，返回默认值。这允许临时禁用某些配置项而不需要删除它们。 | True | - |

## 配置项列表

### UI模块配置

| ConfigID | ConfigName | ConfigValue | Description | Module | Category | 实际使用情况 |
| -------- | ---------- | ----------- | ----------- | ------ | -------- | ------------ |
| UI001 | ProjectNameFont | Arial | 项目名称字体 | UI | Font | 在modUI.FormatGanttWorksheet中通过GetModuleConfig获取 |
| UI002 | ProjectNameFontSize | 16 | 项目名称字号 | UI | Font | 在modUI.FormatGanttWorksheet中通过GetModuleConfig获取 |
| UI003 | ProjectNameFontBold | True | 项目名称是否粗体 | UI | Font | 在modUI.FormatGanttWorksheet中通过GetModuleConfig获取 |
| UI004 | ProjectNameFontColor | #000000 | 项目名称字体颜色 | UI | Color | 在modUI.FormatGanttWorksheet中通过GetModuleConfig获取 |
| UI005 | ProjectManagerFont | Arial | 项目经理信息字体 | UI | Font | 在modUI.FormatGanttWorksheet中通过GetModuleConfig获取 |
| UI006 | ProjectManagerFontSize | 12 | 项目经理信息字号 | UI | Font | 在modUI.FormatGanttWorksheet中通过GetModuleConfig获取 |
| UI007 | ProjectManagerFontColor | #000000 | 项目经理信息字体颜色 | UI | Color | 在modUI.FormatGanttWorksheet中通过GetModuleConfig获取 |
| UI008 | ChartTheme | 1 | 甘特图主题 | UI | Theme | 在modUI.ApplyGanttTheme中使用，可选值：1-5，每个主题包含预配置的各种选项 |
| UI009 | ChartGridlinesArea | all | 网格线应用区域 | UI | Layout | 在modUI.ApplyGanttTheme中使用，可选值：all/header/gantt/task/header,gantt/header,task/gantt,task |
| UI009_Type | ChartGridlinesType | all | 网格线类型 | UI | Layout | 在modUI.ApplyGanttTheme中使用，可选值：all/horizontal/vertical |
| UI010 | ChartGridlineColor | #DDDDDD | 网格线颜色 | UI | Color | 在modUI.ApplyGanttTheme中使用 |
| UI011 | ChartBackgroundColor | #FFFFFF | 甘特图背景颜色 | UI | Color | 在modUI.ApplyGanttTheme中使用 |
| UI012 | ChartAlternateRowColor | #F5F5F5 | 甘特图交替行颜色 | UI | Color | 在modUI.ApplyGanttTheme中使用 |
| UI013 | ChartFont | Barlow | 甘特图默认字体 | UI | Font | 在modUI.ApplyGanttTheme中使用 |
| UI014 | ChartFontSize | 10 | 甘特图默认字号 | UI | Font | 在modUI.ApplyGanttTheme中使用 |

### Gantt模块配置

| ConfigID | ConfigName | ConfigValue | Description | Module | Category | 实际使用情况 |
| -------- | ---------- | ----------- | ----------- | ------ | -------- | ------------ |
| GT001 | TimelineYearFont | Arial | 时间轴年份字体 | Gantt | Font | 在modGantt.CreateTimeline中使用 |
| GT002 | TimelineYearFontSize | 11 | 时间轴年份字号 | Gantt | Font | 在modGantt.CreateTimeline中使用 |
| GT003 | TimelineYearFontBold | True | 时间轴年份是否粗体 | Gantt | Font | 在modGantt.CreateTimeline中使用 |
| GT004 | TimelineYearFontColor | #000000 | 时间轴年份字体颜色 | Gantt | Color | 在modGantt.CreateTimeline中使用 |
| GT005 | TimelineYearBackColor | #E0E0E0 | 时间轴年份背景颜色 | Gantt | Color | 在modGantt.CreateTimeline中使用 |
| GT006 | TimelineMonthFont | Arial | 时间轴月份字体 | Gantt | Font | 在modGantt.CreateTimeline中使用 |
| GT007 | TimelineMonthFontSize | 10 | 时间轴月份字号 | Gantt | Font | 在modGantt.CreateTimeline中使用 |
| GT008 | TimelineMonthFontColor | #000000 | 时间轴月份字体颜色 | Gantt | Color | 在modGantt.CreateTimeline中使用 |
| GT009 | TimelineMonthBackColor | #F0F0F0 | 时间轴月份背景颜色 | Gantt | Color | 在modGantt.CreateTimeline中使用 |
| GT010 | TimelineWeekFont | Arial | 时间轴周数字体 | Gantt | Font | 在modGantt.CreateTimeline中使用 |
| GT011 | TimelineWeekFontSize | 9 | 时间轴周数字号 | Gantt | Font | 在modGantt.CreateTimeline中使用 |
| GT012 | TimelineWeekFontColor | #000000 | 时间轴周数字体颜色 | Gantt | Color | 在modGantt.CreateTimeline中使用 |
| GT013 | TimelineWeekBackColor | #FFFFFF | 时间轴周数背景颜色 | Gantt | Color | 在modGantt.CreateTimeline中使用 |
| GT014 | CellWidthFactor | 1.2 | 甘特图时间单元格宽度放大系数 | Gantt | Layout | 在modUI.FormatGanttWorksheet中通过GetConfigValue("GT014", 1.2)获取 |
| GT015 | DefaultTaskColor | #3366CC | 默认任务条颜色 | Gantt | Color | 在modGantt.DrawTask中使用 |
| GT016 | DefaultMilestoneColor | #FF9900 | 默认里程碑颜色 | Gantt | Color | 在modGantt.DrawMilestone中使用 |
| GT017 | TaskProgressColor | #66CC66 | 任务进度颜色 | Gantt | Color | 在modGantt.DrawTask中使用 |
| GT018 | CurrentDateLineColor | #FF0000 | 当前日期线颜色 | Gantt | Color | 在modGantt.DrawCurrentDateLine中使用 |
| GT019 | CurrentDateLineStyle | 2 | 当前日期线样式（1-实线，2-虚线） | Gantt | Style | 在modGantt.DrawCurrentDateLine中使用 |
| GT020 | DefaultTaskPosition | next | 默认任务位置 | Gantt | Layout | 在modGantt.DetermineTaskRowAndCategory中使用 |
| GT021 | DefaultTaskTextPosition | right | 默认任务文字位置 | Gantt | Layout | 在modGantt.DrawTask中使用 |
| GT022 | DefaultMilestoneTextPosition | right | 默认里程碑文字位置 | Gantt | Layout | 在modGantt.DrawMilestone中使用 |
| GT023 | BaselineColor | #FF0000 | 基准线颜色 | Gantt | Color | 在modGantt.DrawBaselineWithBounds中通过GetConfigValue("GT023", "#FF0000")获取 |
| GT024 | BaselineStyle | 2 | 基准线样式（1-实线，2-虚线） | Gantt | Style | 在modGantt.DrawBaselineWithBounds中通过GetConfigValue("GT024", "2")获取 |
| GT025 | BaselineWeight | 1.5 | 基准线宽度 | Gantt | Style | 在modGantt.DrawBaselineWithBounds中通过GetConfigValue("GT025", "1.5")获取 |
| GT026 | TaskBarHeight | 11 | 任务条高度/里程碑大小（像素） | Gantt | Layout | 在modGantt.DrawTask和DrawMilestone中通过GetConfigValue("GT026", "11")获取 |
| GT027 | TaskBarBorderWidth | 0 | 任务条/里程碑边框宽度（0=无边框） | Gantt | Style | 在modGantt.DrawTask和DrawMilestone中通过GetConfigValue("GT027", "0")获取 |
| GT028 | ProgressBarColor | #66CC66 | 进度条颜色 | Gantt | Color | 在modGantt.DrawTask中通过GetConfigValue("GT028", "#66CC66")获取 |
| GT029 | LabelDistance | 5 | 标签与任务条/里程碑的距离（像素） | Gantt | Layout | 在modGantt.DrawTask和DrawMilestone中通过GetConfigValue("GT029", "5")获取 |
| GT030 | RowPadding | 3 | 行高上下预留空隙（像素） | Gantt | Layout | 在modGantt.DrawTask和DrawMilestone中通过GetConfigValue("GT030", "3")获取 |
| GT031 | CategoryFontName | Barlow | 类别标题字体名称 | Gantt | Font | 在modGantt.MergeCategoryTitles中通过GetConfigValue("GT031", "Barlow")获取 |
| GT032 | CategoryFontSize | 11 | 类别标题字体大小 | Gantt | Font | 在modGantt.MergeCategoryTitles中通过GetConfigValue("GT032", "11")获取 |
| GT033 | CategoryFontBold | 1 | 类别标题是否粗体 | Gantt | Font | 在modGantt.MergeCategoryTitles中通过GetConfigValue("GT033", "1")获取 |
| GT034 | CategoryColumnWidth | 15 | 类别标题列宽 | Gantt | Layout | 在modGantt.MergeCategoryTitles中通过GetConfigValue("GT034", "15")获取 |

### Data模块配置

| ConfigID | ConfigName | ConfigValue | Description | Module | Category | 实际使用情况 |
| -------- | ---------- | ----------- | ----------- | ------ | -------- | ------------ |
| DT001 | AutoCalculateDuration | True | 是否自动计算任务持续时间 | Data | Calculation | 在modData.ValidateTasksData中使用 |
| DT002 | ExcludeWeekends | True | 计算持续时间时是否排除周末 | Data | Calculation | 在modData.ValidateTasksData中使用 |
| DT003 | DefaultTaskProgress | 0 | 默认任务进度 | Data | Default | 在modData.GetAllTasks中使用 |

### Debug模块配置

| ConfigID | ConfigName | ConfigValue | Description | Module | Category | 实际使用情况 |
| -------- | ---------- | ----------- | ----------- | ------ | -------- | ------------ |
| DB001 | EnableDebug | False | 是否开启debug模式(True=开启，False=关闭) | Debug | Default | 在modDebug.InitDebug中通过参数或GetConfig("EnableDebug", False)获取，控制整个调试系统是否启用 |
| DB002 | DebugLevel | 4 | 调试级别(1=错误,2=警告,3=信息,4=详细) | Debug | Default | 在modDebug.InitDebug中通过参数或GetConfig("DebugLevel", 4)获取，默认为DEBUG_LEVEL_VERBOSE |
| DB003 | EnableFileLogging | False | 是否启用文件日志(True=启用，False=禁用) | Debug | Default | 在modDebug.InitDebug中通过参数或GetConfig("EnableFileLogging", False)获取，控制是否将日志写入文件 |
| DB004 | EnableImmediateOutput | False | 是否输出到即时窗口(True=输出，False=不输出) | Debug | Default | 在modDebug.WriteToLog中使用，控制是否将日志输出到VBE即时窗口 |
| DB005 | UTF8Encoding | True | 是否使用UTF-8编码日志文件(True=使用UTF-8，False=使用默认编码) | Debug | Default | 在modDebug模块中使用，确保日志文件支持中文等Unicode字符 |

## 配置访问函数

系统中使用以下函数访问配置项：
```vba
' 获取特定模块的所有配置，使用ConfigName作为键，并支持默认值
Public Function GetModuleConfig(moduleName As String, Optional defaultValues As Dictionary = Nothing) As Dictionary
    On Error GoTo ErrorHandler

    Dim result As New Dictionary
    Dim tbl As ListObject
    Dim i As Long

    ' 记录函数进入
    modDebug.LogFunctionEntry "modData.GetModuleConfig"

    ' 获取配置表引用
    Set tbl = ThisWorkbook.Worksheets("Config").ListObjects("configTable")

    ' 如果表格没有数据，返回默认值字典或空字典
    If tbl.DataBodyRange Is Nothing Then
        If Not defaultValues Is Nothing Then
            Set GetModuleConfig = defaultValues
            modDebug.LogInfo "配置表为空，使用提供的默认值", "modData.GetModuleConfig"
        Else
            Set GetModuleConfig = result
            modDebug.LogInfo "配置表为空，返回空字典", "modData.GetModuleConfig"
        End If
        modDebug.LogFunctionExit "modData.GetModuleConfig"
        Exit Function
    End If

    ' 遍历每一行
    For i = 1 To tbl.DataBodyRange.Rows.Count
        ' 如果模块名称匹配且配置已启用
        If tbl.DataBodyRange.Cells(i, tbl.ListColumns("Module").Index).Value = moduleName And _
           tbl.DataBodyRange.Cells(i, tbl.ListColumns("IsEnabled").Index).Value = True Then
            Dim configName As String
            Dim configValue As Variant

            configName = tbl.DataBodyRange.Cells(i, tbl.ListColumns("ConfigName").Index).Value
            configValue = tbl.DataBodyRange.Cells(i, tbl.ListColumns("ConfigValue").Index).Value

            ' 使用ConfigName作为键
            result.Add configName, configValue
        End If
    Next i

    ' 如果提供了默认值字典，将缺失的键从默认值字典中添加到结果中
    If Not defaultValues Is Nothing Then
        Dim key As Variant
        For Each key In defaultValues.Keys
            If Not result.Exists(key) Then
                result.Add key, defaultValues(key)
                modDebug.LogVerbose "配置项" & key & " 不存在，使用默认值 " & defaultValues(key), "modData.GetModuleConfig"
            End If
        Next key
    End If

    Set GetModuleConfig = result
    modDebug.LogFunctionExit "modData.GetModuleConfig", "获取了" & result.Count & " 个配置项"
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modData.GetModuleConfig"

    ' 如果出错且提供了默认值字典，返回默认值字典
    If Not defaultValues Is Nothing Then
        Set GetModuleConfig = defaultValues
    Else
        Set GetModuleConfig = New Dictionary ' 返回空字典
    End If
    modDebug.LogFunctionExit "modData.GetModuleConfig", "错误: " & Err.Description
End Function

' 从配置字典中获取值，支持默认值
Public Function GetConfigFromDict(dict As Dictionary, configName As String, Optional defaultValue As Variant = Null) As Variant
    On Error GoTo ErrorHandler

    If dict.Exists(configName) Then
        GetConfigFromDict = dict(configName)
    Else
        GetConfigFromDict = defaultValue
        modDebug.LogVerbose "配置项" & configName & " 在字典中不存在，使用默认值 " & defaultValue, "modData.GetConfigFromDict"
    End If

    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modData.GetConfigFromDict"
    GetConfigFromDict = defaultValue
End Function
```

## 配置项访问模式
在实际代码中，配置项的访问主要采用以下模式：

### 1. 使用GetModuleConfig获取模块所有配置项

这是推荐的方式，特别是当需要访问同一模块的多个配置项时：

```vba
' 获取UI模块配置，使用默认值
Dim uiConfig As Dictionary
Set uiConfig = GetModuleConfig("UI", modConfigDefaults.GetUIDefaults())

' 使用获取的配置设置项目名称格式
With ws.Cells(1, 2)
    .Value = projectInfo("ProjectName")
    .Font.Name = uiConfig("ProjectNameFont")
    .Font.Size = uiConfig("ProjectNameFontSize")
    .Font.Bold = uiConfig("ProjectNameFontBold")
    .Font.Color = modUtilities.GetRGBColor(uiConfig("ProjectNameFontColor"))
End With
```

### 2. 使用GetConfigFromDict从配置字典中获取单个值

当从配置字典中获取单个值时，可以使用GetConfigFromDict函数，它支持提供默认值：

```vba
' 从配置字典中获取值
Dim fontName As String
fontName = GetConfigFromDict(uiConfig, "ProjectNameFont", "Arial")
```

### 3. 性能优化建议

在循环中访问配置项时，应该在循环外获取配置，避免重复查询：
```vba
' 在循环外获取配置
Dim ganttConfig As Dictionary
Set ganttConfig = GetModuleConfig("Gantt", modConfigDefaults.GetGanttDefaults())
Dim taskHeight As Double
taskHeight = CDbl(ganttConfig("TaskBarHeight"))

' 在循环中直接使用
For i = 1 To taskCount
    ' 使用taskHeight，避免重复查询
    ' ...
Next i
```

### 4. 默认值管理
所有默认值都集中在modConfigDefaults模块中管理，按模块分组：

```vba
' 获取UI模块默认配置
Dim uiDefaults As Dictionary
Set uiDefaults = modConfigDefaults.GetUIDefaults()

' 获取Gantt模块默认配置
Dim ganttDefaults As Dictionary
Set ganttDefaults = modConfigDefaults.GetGanttDefaults()

' 获取所有模块的默认配置
Dim allDefaults As Dictionary
Set allDefaults = modConfigDefaults.GetAllDefaults()
```

### 5. 配置项访问的关键逻辑

1. **错误处理**：所有配置项访问都包含错误处理，确保即使配置表不存在或配置项不存在，代码也能继续运行。
2. **默认值**：GetModuleConfig函数支持提供默认值字典，确保即使配置项不存在或被禁用，也能使用合理的默认值。
3. **类型转换**：从配置表获取的值都是Variant类型，需要根据实际使用进行类型转换（如CStr、CDbl、CBool等）。
4. **模块分组**：配置项按模块分组，便于管理和访问。GetModuleConfig函数可以一次性获取某个模块的所有配置项。
5. **启用/禁用**：配置表中的IsEnabled字段允许临时禁用某些配置项，而不需要删除它们。GetModuleConfig函数会检查IsEnabled字段，只返回启用的配置项。
6. **ListObject访问**：代码使用ListObject对象模型访问配置表，而不是直接访问单元格，这使得代码更加健壮，不受表格位置变化的影响。
7. **性能优化**：
   - 使用GetModuleConfig一次性获取所有配置项，然后多次使用，比多次调用单独的配置项访问函数更高效，特别是在循环中。
   - 使用数组处理代替单元格访问：GetModuleConfig函数将整个数据区域一次性读取到数组中，然后在内存中处理，避免了多次访问Excel对象模型，显著提高了性能。
   - 预先获取列索引：在循环外提前获取所需列的索引，避免在循环中重复查找。
